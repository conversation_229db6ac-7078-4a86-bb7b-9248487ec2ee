package com.reltio.qa.model.devOps;

import com.reltio.qa.enums.devOps.ReltioService;
import com.reltio.qa.enums.devOps.ServicePurpose;
import com.reltio.qa.enums.devOps.TenantSize;
import com.reltio.qa.enums.match.MatchingStrategy;
import com.reltio.qa.utils.GsonUtils;

import java.util.List;
import java.util.Map;

/**
 * Copy of com.reltio.devops.api.model.TenantModel class and related objects
 */
public class DevOpsTenantModel {
    public enum Tier {
        STANDARD,
        PREMIUM
    }

    private String tenantName;
    private String tenantId;
    private String tenantCustomer;
    private TenantSize tenantSize;
    private MatchingStrategy matchingStrategy;
    private Boolean needIteractionKeyspace;
    private int replicationFactor;
    private int historyReplicationFactor;
    private Integer tenantInternalId;
    private String gbtHistoryTtl;
    private Boolean useSpannerCloudFunction;
    private Boolean useSpannerForInteractions;
    private String vaultUrl;
    private ServicePurpose servicePurpose;
    private Map<ServicePurpose, List<ReltioService>> storagePriorityList;
    private Boolean useServiceBusFunction;
    private String serviceBusNamespace;
    private Tier serviceBusPricingTier;
    private String cosmosAccountName;

    private DevOpsTenantModel(DevOpsTenantModelBuilder builder) {
        this.tenantName = builder.tenantName;
        this.tenantId = builder.tenantId;
        this.tenantCustomer = builder.tenantCustomer;
        this.tenantSize = builder.tenantSize;
        this.matchingStrategy = builder.matchingStrategy;
        this.needIteractionKeyspace = builder.needIteractionKeyspace;
        this.replicationFactor = builder.replicationFactor;
        this.historyReplicationFactor = builder.historyReplicationFactor;
        this.tenantInternalId = builder.tenantInternalId;
        this.gbtHistoryTtl = builder.gbtHistoryTtl;
        this.useSpannerCloudFunction = builder.useSpannerCloudFunction;
        this.useSpannerForInteractions = builder.useSpannerForInteractions;
        this.vaultUrl = builder.vaultUrl;
        this.servicePurpose = builder.servicePurpose;
        this.storagePriorityList = builder.storagePriorityList;
        this.useServiceBusFunction = builder.useServiceBusFunction;
        this.serviceBusNamespace = builder.serviceBusNamespace;
        this.serviceBusPricingTier = builder.serviceBusPricingTier;
        this.cosmosAccountName = builder.cosmosAccountName;
    }

    public static class DevOpsTenantModelBuilder {
        private String tenantName;
        private String tenantId;
        private String tenantCustomer;
        private TenantSize tenantSize;
        private MatchingStrategy matchingStrategy;
        private Boolean needIteractionKeyspace;
        private int replicationFactor;
        private int historyReplicationFactor;
        private Integer tenantInternalId;
        private String gbtHistoryTtl;
        private Boolean useSpannerCloudFunction;
        private Boolean useSpannerForInteractions;
        private String vaultUrl;
        private ServicePurpose servicePurpose;
        private Map<ServicePurpose, List<ReltioService>> storagePriorityList;
        private Boolean useServiceBusFunction;
        private String serviceBusNamespace;
        private Tier serviceBusPricingTier;
        private String cosmosAccountName;

        public DevOpsTenantModelBuilder setTenantName(String tenantName) {
            this.tenantName = tenantName;
            return this;
        }

        public DevOpsTenantModelBuilder setTenantId(String tenantId) {
            this.tenantId = tenantId;
            return this;
        }

        public DevOpsTenantModelBuilder setTenantCustomer(String tenantCustomer) {
            this.tenantCustomer = tenantCustomer;
            return this;
        }

        public DevOpsTenantModelBuilder setTenantSize(TenantSize tenantSize) {
            this.tenantSize = tenantSize;
            return this;
        }

        public DevOpsTenantModelBuilder setMatchingStrategy(MatchingStrategy matchingStrategy) {
            this.matchingStrategy = matchingStrategy;
            return this;
        }

        public DevOpsTenantModelBuilder setNeedIteractionKeyspace(Boolean needIteractionKeyspace) {
            this.needIteractionKeyspace = needIteractionKeyspace;
            return this;
        }

        public DevOpsTenantModelBuilder setReplicationFactor(int replicationFactor) {
            this.replicationFactor = replicationFactor;
            return this;
        }

        public DevOpsTenantModelBuilder setHistoryReplicationFactor(int historyReplicationFactor) {
            this.historyReplicationFactor = historyReplicationFactor;
            return this;
        }

        public DevOpsTenantModelBuilder setTenantInternalId(Integer tenantInternalId) {
            this.tenantInternalId = tenantInternalId;
            return this;
        }

        public DevOpsTenantModelBuilder setGbtHistoryTtl(String gbtHistoryTtl) {
            this.gbtHistoryTtl = gbtHistoryTtl;
            return this;
        }

        public DevOpsTenantModelBuilder setUseSpannerCloudFunction(Boolean useSpannerCloudFunction) {
            this.useSpannerCloudFunction = useSpannerCloudFunction;
            return this;
        }

        public DevOpsTenantModelBuilder setUseSpannerForInteractions(Boolean useSpannerForInteractions) {
            this.useSpannerForInteractions = useSpannerForInteractions;
            return this;
        }

        public DevOpsTenantModelBuilder setVaultUrl(String vaultUrl) {
            this.vaultUrl = vaultUrl;
            return this;
        }

        public DevOpsTenantModelBuilder setServicePurpose(ServicePurpose servicePurpose) {
            this.servicePurpose = servicePurpose;
            return this;
        }

        public DevOpsTenantModelBuilder setStoragePriorityList(Map<ServicePurpose, List<ReltioService>> storagePriorityList) {
            this.storagePriorityList = storagePriorityList;
            return this;
        }

        public DevOpsTenantModelBuilder setUseServiceBusFunction(Boolean useServiceBusFunction) {
            this.useServiceBusFunction = useServiceBusFunction;
            return this;
        }

        public DevOpsTenantModelBuilder setServiceBusNamespace(String serviceBusNamespace) {
            this.serviceBusNamespace = serviceBusNamespace;
            return this;
        }

        public DevOpsTenantModelBuilder setServiceBusPricingTier(Tier serviceBusPricingTier) {
            this.serviceBusPricingTier = serviceBusPricingTier;
            return this;
        }

        public DevOpsTenantModelBuilder setCosmosAccountName(String cosmosAccountName) {
            this.cosmosAccountName = cosmosAccountName;
            return this;
        }

        public DevOpsTenantModel build() {
            return new DevOpsTenantModel(this);
        }
    }

    @Override
    public String toString() {
        return GsonUtils.getGson().toJson(this);
    }
}

