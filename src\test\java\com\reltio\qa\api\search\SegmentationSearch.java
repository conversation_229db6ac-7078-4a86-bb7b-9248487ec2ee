package com.reltio.qa.api.search;

import com.google.gson.JsonElement;
import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.enums.tasks.ExecutionType;
import com.reltio.qa.exceptions.ApplicationGlobalException;
import com.reltio.qa.helpers.loaddata.LoadData;
import com.reltio.qa.model.*;
import com.reltio.qa.model.Interactions.InteractionMemberModel;
import com.reltio.qa.model.Interactions.InteractionModel;
import com.reltio.qa.model.Interactions.InteractionSubMember;
import com.reltio.qa.services.*;
import com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformConfigs;
import com.reltio.qa.services.tenant.config.TenantManagementServiceForInteractions;
import com.reltio.qa.utils.RandomGenerator;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.Assert;
import org.testng.SkipException;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.sql.Date;
import java.util.HashMap;
import java.util.Map;

import static com.reltio.qa.helpers.CommonStrings.*;
import static com.reltio.qa.model.Interactions.InteractionModel.interactionWithSimpleAttr;
import static com.reltio.qa.model.Interactions.InteractionModel.simpleInteraction;
import static com.reltio.qa.model.SearchFilterExpression.*;
import static com.reltio.qa.services.EntityService.addAnalyticsAttribute;
import static com.reltio.qa.services.SearchService.searchTotal;
import static com.reltio.qa.services.TenantManagementService.cleanTenant;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForConsistency;

@Log4j2
public class SegmentationSearch extends TestNGBaseTest {

    private static final String SEGMENTATION_TEST_POSTFIX = " segmentation test";

    private static final String USER_2 = "User" + RandomGenerator.getInstance().getRandomString(5);
    private String User2;
    private JsonElement searchResult;
    SearchFilter searchFilter;
    GetEntityRequestParameters filterAndOptions;
    private EntityModel en1, en2, en3;
    private InteractionModel inter1;
    private String en_createdTime, en_updatedTime, en_FirstNameStartsWith;
    private String inter_crosswalkType_short, inter_crosswalkValue, inter_boolean;
    private String email_createdTime, email_attribute_subject_value;

    @BeforeClass(alwaysRun = true)
    public void bcSegmentation() throws Exception {

        if (!GlobalConfig.isUseSpannerForInteractions()) {
            throw new SkipException("Segmentation search available only for Spanner database!");
        }

        cleanTenant();

        AccountService.createUser(USER_2, "ROLE_API", "ROLE_USER", "ROLE_TEST");
        BusinessConfigurationService.updateConfig(loadFile("ss-tenant-config.json"));

        log.info("Using Spanner for Interactions");
        SearchService.setV2(true);

        if (!TenantManagementServiceForInteractions.isInteractionsRepositoryVersionSetToV2()) {
            log.info("Update tenant's config to use SpannerDB for Interactions...");
            TenantManagementServiceForInteractions.enableInteractionsV2Config(Config.getTenantId());
            SmartWaitingPlatformConfigs.waitForRepositoryVersionSetToV2InConfig();
        }

        precondition();
    }


    private void precondition() throws Exception {

        // - add HCP1 by User2
        // - add HCP2
        // - add HCO1 by User2
        // - add HCP3 by User2 w/o Interaction
        log.info("Run precondition...");
        User2 = AccountService.getAccount(USER_2).getUsername();
        en1 = LoadData.createHCP("AMA").post(User2);
        en2 = new EntityModel(ENTITY_TYPE_HCP).post();
        en3 = new EntityModel(ENTITY_TYPE_HCO).post(User2);
        EntityModel entityWOInteractions = new EntityModel(ENTITY_TYPE_HCP).post(User2);
        email_attribute_subject_value = "Subject. test$01".concat(SEGMENTATION_TEST_POSTFIX);

        // - add Interaction1 for HCP1,
        // - add Interaction2 for HCP2 by User2,
        // - add Interaction3 for HCO and HCP2
        inter1 = simpleInteraction(en1, INTERACTION_TYPE_EMAIL, "From").post();
        interactionWithSimpleAttr(en2, INTERACTION_TYPE_PRESCRIPTIONS, "HCP", "Subject", "Subject.prescription").post(User2);
        interactionWithSimpleAttr(en3, INTERACTION_TYPE_EMAIL, "From", "Subject", email_attribute_subject_value).post();

        //load members and ExhibitionEventInteraction
        EntityService.postEntities(loadFile("HCP-HCO-members.json"));
        InteractionModel im = new InteractionModel(loadFile("interaction-ExhEvent.json")).post();
        inter_boolean = im.getAttribute("EXHBT_GLOBAL").getValue().toString();

        //add analytics attributes
        addAnalyticsAttribute(en1, "AT_EXHBT_NAME",
                new SimpleAttributeModel("name1"));

        CrosswalkModel crw = inter1.getCrosswalkByType("Reltio");
        inter_crosswalkType_short = crw.getType().split("/")[2];
        inter_crosswalkValue = crw.getValue();

        en_createdTime = String.valueOf(en1.getCreatedTime());
        en_FirstNameStartsWith = en1.getAttribute(FIRST_NAME).getValue().toString().substring(0, 1);
        en_updatedTime = String.valueOf(en2.getUpdatedTime());
        email_createdTime = String.valueOf(inter1.getCreatedTime());

        TaskService.segmentationSync(ExecutionType.TENANT, new PostTaskRequestParameters().tenantId(Config.getTenantId())).waitWhileRunning();

    }


    @TmsLink("RP-TC-13131")
    @Test(groups = {"regression"}, description = "Search by entities generic, regular and analytics attributes")
    public void runRpTc13131() throws Exception {

        String issueDateValue = RandomGenerator.getInstance().getRandomDate();
        en1.addAttributeToModel("License", new NestedAttributeModel(Map.of("IssueDate",
                new SimpleAttributeModel(issueDateValue)))).post();
        waitForConsistency(en1);

        log.info("Step1. check filter equals entity.id");
        searchFilter = equalsFilter("entity.id", en1.getId());
        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");

        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step2. check filter equals entity.type long format");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("entity.type", ENTITY_TYPE_HCP))
                .select("uri");

        checkSegmentationSearchResult(filterAndOptions, 4);

        log.info("Step3. check filter equals entity.type short format and offset");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("entity.type", "HCP"))
                .select("uri")
                .offset(1);

        checkSegmentationSearchResult(filterAndOptions, 3);

        log.info("Step4. check filter equals entity.type short format and max");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("entity.type", "HCP"))
                .select("uri")
                .max(1);

        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step5. check filter equals entity.createdBy ");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("entity.createdBy", User2))
                .select("uri");

        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step6. check filter equals entity.updatedBy");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(startsWith("entity.createdBy", User2.substring(0, 12)))
                .select("uri");

        checkSegmentationSearchResult(filterAndOptions, 9);

        log.info("Step7. check filter equalsCaseSensitive entity.updatedBy in lower case");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsCaseSensitiveFilter("entity.createdBy", User2.toLowerCase()))
                .select("uri");

        checkSegmentationSearchResult(filterAndOptions, 0);

        log.info("Step8. check filter fullText entity.updatedBy");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(fullText("entity.updatedBy", "User"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step9. check filter missing entity.updatedBy");

        filterAndOptions = new GetEntityRequestParameters()
                .filter(missing("entity.updatedBy"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 0);

        log.info("Step10. check filter exists entity.updatedBy");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(exists("entity.updatedBy"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 9);

        log.info("Step11. check filter contains entity.updatedBy");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(contains("entity.updatedBy", "*User*"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step12. check filter equals entity.createdTime");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("entity.createdTime", en_createdTime))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step13. check filter gte entity.updatedTime");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(gte("entity.updatedTime", en_updatedTime))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 8);


        log.info("Step14.Check fullText for entity attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(fullText("entity.attributes.FirstName", "HCP AF test FN")))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step15.Check search by date type for entity attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(gte("entity.attributes.License.IssueDate",
                        String.valueOf(Date.valueOf(issueDateValue).getTime())))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step16.Check filter exists entity.updatedTime");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(exists("entity.updatedTime"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 9);

    }

    @TmsLink("RP-TC-13134")
    @Test(groups = {"regression"}, description = "Search entities by interactions system and regular attributes")
    public void runRpTc13134() throws Exception {

        log.info("Step1. check filter equals interaction.crosswalkType");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("interaction.crosswalkType", inter_crosswalkType_short))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 3);

        log.info("Step2. check filter equals interaction.crosswalkValue");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("interaction.crosswalkValue", inter_crosswalkValue))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step3.Check filter equals interaction.attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("interaction.attributes.Subject", email_attribute_subject_value))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step4.Check filter missing interaction.attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(missing("interaction.attributes.Subject"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 7);

        log.info("Step5.Check filter gte interaction.createdTime");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(gte("interaction.createdTime", email_createdTime))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 5);

        log.info("Step6.Check filter missing interaction.createdTime");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(missing("interaction.createdTime"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 4);

        log.info("Step7.Check filter equals member.type");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(equalsFilter("member.type", "HCP"))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step8.Check filter missing interaction.createdBy");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(missing("interaction.createdBy", ""))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 4);

    }

    @TmsLink("RP-TC-13305")
    @Test(groups = {"regression"}, description = "Search entities by entities and interactions attributes")
    public void runRpTc13305() throws Exception {
        log.info("Step1.Check filtering with complex filter interaction system attr + interaction regular attr");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(gte("interaction.createdTime", inter1.getCreatedTime().toString()))
                        .and(new SearchFilterGroup(equalsFilter("interaction.attributes.Subject", email_attribute_subject_value))
                        ))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);

        log.info("Step2.Check filtering with complex filter interaction system attr + interaction system attr");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(equalsFilter("interaction.type", "Email"))
                        .or(new SearchFilterGroup(equalsFilter("interaction.attributes.Subject", email_attribute_subject_value))
                        ))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step3.Check filtering with EhxitionEvent attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(equalsFilter("interaction.type", "ExhibitionEvent"))
                        .or(new SearchFilterGroup(equalsCaseSensitiveFilter("interaction.attributes.EXHBT_GLOBAL", inter_boolean))
                        ))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step4.Check complex filtering:system entity attributes, regular+system interactions attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(gte("entity.createdTime", en_createdTime))
                        .and(new SearchFilterGroup(missing("interaction.attributes.Subject"))
                                .or(new SearchFilterGroup(equalsFilter("interaction.crosswalkType", inter_crosswalkType_short)
                                ))

                        ))
                .select("uri")
                .offset(5)
                .max(5);
        checkSegmentationSearchResult(filterAndOptions, 4);

        log.info("Step5.Check complex filtering:system+regular entity attributes, regular+system interactions attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(gte("entity.createdTime", en_createdTime))
                        .and(new SearchFilterGroup(missing("interaction.attributes.Subject"))
                                .and(new SearchFilterGroup(equalsFilter("interaction.crosswalkType", inter_crosswalkType_short))
                                        .and(new SearchFilterGroup(startsWith("entity.attributes.FirstName", en_FirstNameStartsWith))
                                        ))))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);


        log.info("Step6.Check filtering: analytics attributes");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(equalsCaseSensitiveFilter("entity.analyticsAttributes.AT_EXHBT_NAME", "name1"))
                        .and(new SearchFilterGroup(missing("interaction.attributes.Subject"))
                                .and(new SearchFilterGroup(equalsFilter("interaction.crosswalkType", inter_crosswalkType_short))
                                        .and(new SearchFilterGroup(exists("entity.attributes.FirstName"))
                                        ))))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1);

    }

    @TmsLink("RP-TC-13619")
    @Test(groups = {"regression"}, description = "Search after merge split members")
    public void runRpTc13619() throws Exception {
        //TODO: remove checkTotal after bugs are fixed
        boolean checkTotal = false;

        log.info("Make en2,en3 members in several interactions");
        interactionWithSimpleAttr(en3, INTERACTION_TYPE_EMAIL, "From", "Subject", email_attribute_subject_value)
                .addMember("To", new InteractionMemberModel(ENTITY_TYPE_HCP)
                        .addSubMember(new InteractionSubMember(en2.getUri()))).post();

        log.info("Merge members");
        EntityService.mergeEntities(en1, en2, en1);

        log.info("Step1. Check search after Merge members");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(equalsFilter("interaction.type", "Email"))
                        .and(new SearchFilterGroup(equalsFilter("interaction.attributes.Subject", email_attribute_subject_value))
                        ))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 1, checkTotal);
        checkUriExistsInSearchResult(searchResult, en2.getUri(), true);

        log.info("Splitting members");
        en1.unmergeAndWait(en2);

        log.info("Step2. Check search after Split members");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(equalsFilter("interaction.type", "Email"))
                        .and(new SearchFilterGroup(equalsFilter("interaction.attributes.Subject", email_attribute_subject_value))
                        ))
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2, false);

    }

    @TmsLink("RP-TC-13641")
    @Test(groups = {"regression"}, description = "Search for enities with endDate")
    public void runRpTc13641() throws Exception {
        //set endDate for some entity
        log.info("set EndDate for en2");
        en2.setStartEndDate("endDate", System.currentTimeMillis());
        waitForConsistency(en2);

        log.info("Step1. Check search does not include endDated entity");
        filterAndOptions = new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(equalsFilter("interaction.type", "Email"))
                        .and(new SearchFilterGroup(equalsFilter("interaction.attributes.Subject", email_attribute_subject_value))
                        ))
                .select("uri");
        //TODO: remove checkTotal after bugs are fixed
        boolean checkTotal = false;

        checkSegmentationSearchResult(filterAndOptions, 1, checkTotal);
        checkUriExistsInSearchResult(searchResult, en2.getUri(), true);

        //reset endDate for en2 to complete next tests
        en2.setStartEndDate("endDate", null);
        waitForConsistency(en2);
    }

    @TmsLink("RP-TC-13643")
    @Test(groups = {"regression"}, description = "Search with inSameInteraction group")
    public void runRpTc13643() throws Exception {

        log.info("Step1. Check search with inSameInteraction group");
        searchFilter = new SearchFilterGroup(inSameInteraction(
                new SearchFilterGroup(equalsFilter("interaction.type", "Email"))
                        .and(new SearchFilterGroup(startsWith("interaction.attributes.Subject", "Subject"))
                        )));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        //TODO: remove checkTotal after bugs are fixed
        boolean checkTotal = false;
        checkSegmentationSearchResult(filterAndOptions, 2, checkTotal);

    }

    @TmsLink("RP-TC-13644")
    @Test(groups = {"regression"}, description = "Search with searchByOv")
    public void runRpTc13644() throws Exception {

        EntityModel newHcp = new EntityModel(ENTITY_TYPE_HCP).addCrosswalk("AMA","12345").post();

        String OV_TRUE_VALUE_STRING = "ov-true-value";
        String DATE = RandomGenerator.getInstance().getRandomDate();
        String BOOLEAN = String.valueOf(true);
        String INT = "1999";
        String Numeric = "100";
        String SubAttrType = "IMS ID";

        HashMap<String, String> attr = new HashMap<>();
        attr.put("FirstName", OV_TRUE_VALUE_STRING);
        attr.put("KaiserProvider", BOOLEAN);
        attr.put("DoD", DATE);
        attr.put("YoB", INT);
        attr.put("NumberofPhysicansPediatric", Numeric);

        //update HCP en2 with ovTrue value in simple attributes
        attr.forEach((key, value) -> en2.addSimpleAttributeToModel(key, value));

        //update HCP en2 with ovTrue value in Nested attributes
        HashMap<String, Object> subAttr = new HashMap<>();
        subAttr.put("Type", new SimpleAttributeModel(SubAttrType));
        NestedAttributeModel Nested = new NestedAttributeModel(subAttr);
        en2.addAttributeToModel("Identifiers", Nested).post();

        //update newHCP to have ov false in attributes
        attr.forEach((key, value) -> addAttributeValueOvFalse(newHcp, key, value));
        addNestedAttributeValueOvFalse(newHcp, "Identifiers", subAttr);

        waitForConsistency(newHcp);
        waitForConsistency(en2);

        log.info("Step1. Check search by default: ov+nonOv");

        searchFilter = new SearchFilterGroup(equalsFilter("entity.type", "HCP"))
                .and(startsWith("entity.attributes.FirstName", "ov"));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        log.info("Step2. Check search with searchByOv for String: ov only");

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri").options("searchByOv");
        checkSegmentationSearchResult(filterAndOptions, 1);
        checkUriExistsInSearchResult(searchResult, newHcp.getUri(), true);

        log.info("Step3. Check search with searchByOv for boolean: ov only");

        searchFilter = new SearchFilterGroup(equalsFilter("entity.type", "HCP"))
                .and(equalsFilter("entity.attributes.KaiserProvider", "true"));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 3);

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri").options("searchByOv");
        checkSegmentationSearchResult(filterAndOptions, 2);
        checkUriExistsInSearchResult(searchResult, newHcp.getUri(), true);


        log.info("Step4. Check search with searchByOv for Date: ov only");

        searchFilter = new SearchFilterGroup(equalsFilter("entity.type", "HCP"))
                .and(gte("entity.attributes.DoD", String.valueOf(Date.valueOf(DATE).getTime())));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri").options("searchByOv");
        checkSegmentationSearchResult(filterAndOptions, 1);
        checkUriExistsInSearchResult(searchResult, newHcp.getUri(), true);

        log.info("Step5. Check search with searchByOv for int: ov only");

        searchFilter = new SearchFilterGroup(equalsFilter("entity.type", "HCP"))
                .and(equalsFilter("entity.attributes.YoB", INT));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri").options("searchByOv");
        checkSegmentationSearchResult(filterAndOptions, 1);
        checkUriExistsInSearchResult(searchResult, newHcp.getUri(), true);

        log.info("Step6. Check search with searchByOv for Numeric: ov only");
        searchFilter = new SearchFilterGroup(equalsFilter("entity.type", "HCP"))
                .and(equalsFilter("entity.attributes.NumberofPhysicansPediatric", Numeric));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri").options("searchByOv");
        checkSegmentationSearchResult(filterAndOptions, 1);
        checkUriExistsInSearchResult(searchResult, newHcp.getUri(), true);

        log.info("Step 7. Check search with searchByOv for Nested: ov only");
        searchFilter = new SearchFilterGroup(equalsFilter("entity.type", "HCP"))
                .and(equalsFilter("entity.attributes.Identifiers.Type", SubAttrType));

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri");
        checkSegmentationSearchResult(filterAndOptions, 2);

        filterAndOptions = new GetEntityRequestParameters()
                .filter(searchFilter)
                .select("uri").options("searchByOv");
        checkSegmentationSearchResult(filterAndOptions, 1);
        checkUriExistsInSearchResult(searchResult, newHcp.getUri(), true);


    }


    private void checkSegmentationSearchResult(GetEntityRequestParameters filterAndOptions, int expectedValue,
                                               boolean checkTotal) throws Exception {
        try {
            searchResult = SearchService.searchEntitiesSegmentation(filterAndOptions);
            Assert.assertEquals(searchResult.getAsJsonArray().size(), expectedValue, "search v2 result is not expected!");

            // TODO: remove condition after all bugs with _total are fixed
            if (checkTotal) {
                checkSegmentationTotalResult(filterAndOptions, expectedValue);
            }
            checkSegmentationScanResult(filterAndOptions, expectedValue);
            log.info("Check search v2 result passed");
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Segmentation search result fails with: {}\n", e.getMessage());
            throw new SkipException("Test skipped", e);
        }
    }

    private void checkSegmentationSearchResult(GetEntityRequestParameters filterAndOptions, int expectedValue) throws Exception {
        checkSegmentationSearchResult(filterAndOptions, expectedValue, true);
    }

    private void checkSegmentationScanResult(GetEntityRequestParameters filterAndOptions, int expectedValue) throws Exception {
        try {
            if (filterAndOptions.getOffset() == null) {
                JsonElement searchScanResult = SearchService.searchScanJson(filterAndOptions);
                // check cursor
                String cursor = searchScanResult.getAsJsonObject().get("cursor").getAsJsonObject().get("value").getAsString();
                if ((expectedValue != 0)) {
                    Assert.assertFalse(cursor.isEmpty());
                    Assert.assertEquals(searchScanResult.getAsJsonObject().get("objects").getAsJsonArray().size(),
                            expectedValue, "scan v2 result is not expected!");
                } else {
                    Assert.assertTrue(cursor.isEmpty());
                }
            }
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Segmentation scan v2 result fails with: {}\n", e.getMessage());
            throw new SkipException("Test skipped", e);
        }
    }

    private void checkSegmentationTotalResult(GetEntityRequestParameters filterAndOptions, int expectedValue) throws Exception {
        try {
            if (filterAndOptions.getMax() == null) {
                int correctTotal1 = filterAndOptions.getOffset() != null ? Integer.parseInt(filterAndOptions.getOffset()) : 0;
                Assert.assertEquals(searchTotal(filterAndOptions), expectedValue + correctTotal1, "total number is not expected!");
            }
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Segmentation total v2 result fails with: {}\n", e.getMessage());
            throw new SkipException("Test skipped", e);
        }
    }

    private void checkUriExistsInSearchResult(JsonElement searchResult, String expectedUri, boolean checkUriMissed) {
        try {
            String actualUri = searchResult.getAsJsonArray().get(0).getAsJsonObject().get("uri").getAsString();
            if (checkUriMissed) {
                Assert.assertNotEquals(expectedUri, actualUri);
            } else {
                Assert.assertEquals(expectedUri, actualUri);
            }
        } catch (Exception e) {
            log.error("checkUriExistsInSearchResult fails with: {}\n", e.getMessage());
        }
    }

    private void addAttributeValueOvFalse(EntityModel em, String attrName, String attrValue) {
        try {
            AttributeModel am = new SimpleAttributeModel(attrValue);
            am.setIgnored(true);
            em.addAttributeToModel(attrName, am);
            em.post();
        } catch (Exception e) {
            log.error("Error when add attribute with ov false", e);
            throw new ApplicationGlobalException(e);
        }
    }

    private void addNestedAttributeValueOvFalse(EntityModel em, String attrName, HashMap<String, Object> subAttr) throws Exception {
        AttributeModel am = new NestedAttributeModel(subAttr);
        am.setIgnored(true);
        em.addAttributeToModel(attrName, am);
        em.post();
    }


    @AfterClass(alwaysRun = true)
    private void cleanUp() {
        AccountService.deleteAccountIgnoreExceptions(USER_2);
    }
}
