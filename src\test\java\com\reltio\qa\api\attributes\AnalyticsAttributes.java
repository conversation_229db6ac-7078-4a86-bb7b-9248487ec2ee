package com.reltio.qa.api.attributes;

import com.reltio.qa.Config;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.helpers.TestNGAssertHelper;
import com.reltio.qa.json.JsonDiff;
import com.reltio.qa.model.*;
import com.reltio.qa.service.retry.RetryAnalyzer;
import com.reltio.qa.service.retry.RetryAnalyzerWithClean;
import com.reltio.qa.services.BusinessConfigurationService;
import com.reltio.qa.services.TenantManagementService;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.throwingfunctions.ThrowingSupplier;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Predicate;

import static com.reltio.qa.helpers.CommonStrings.ENTITY_TYPE_HCO;
import static com.reltio.qa.helpers.CommonStrings.ENTITY_TYPE_HCP;
import static com.reltio.qa.helpers.TestNGAssertHelper.shouldBeAttrsEqual;
import static com.reltio.qa.model.SearchFilterExpression.*;
import static com.reltio.qa.services.EntityService.*;
import static com.reltio.qa.services.SearchService.searchEntitiesModels;
import static com.reltio.qa.services.SearchService.searchFacetsJson;
import static com.reltio.qa.services.TenantManagementService.cleanTenant;
import static com.reltio.qa.services.TenantManagementService.setOvStrategy;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForConsistency;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForValue;
import static com.reltio.qa.utils.ListUtils.isListEquals;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.testng.Assert.assertEquals;

@Log4j2
public class AnalyticsAttributes extends TestNGBaseTest {

    @BeforeClass(alwaysRun = true)
    public void bcAnalyticsAttributes() throws Exception {
        if (!Config.isIsolatedEnvironment() && !Config.has("analyticsUrl")) {
            log.warn("'analyticsUrl' absent at the config");
        }
        cleanTenant();
        BusinessConfigurationService.updateConfig(loadFile("aa-config.json"));
    }

    @TmsLink("RP-TC-580")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression", "smoke"}, description = "Get entity with analytics attributes")
    public void runRpTc580() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        addAnalyticsAttribute(en, "TestName",
                new SimpleAttributeModel("name1"),
                new SimpleAttributeModel("name2"),
                new SimpleAttributeModel("name3"));

        NestedAttributeModel nested = new NestedAttributeModel();
        nested.addAttributeToModel("First", new SimpleAttributeModel("a"));
        nested.addAttributeToModel("Second", new SimpleAttributeModel("b"));
        addAnalyticsAttribute(en, "TestNested", nested);
        //2
        en.get();
        assertEquals(2, en.getAnalyticsAttributes().size(), "AnalyticsAttributes count");
        assertEquals(2, en.getAnalyticsAttributes("TestName").size(), "maxOccurs = 2 for TestName, count of values");
        assertEquals("a", en.getAnalyticsAttributes("TestNested/First").get(0).getAsSimple().getValue(), "Value of TestNested/First");
        assertEquals("Nested: a - b", en.getAnalyticsAttributes("TestNested").get(0).getLabel(), "Label of TestNested");
    }

    @TmsLink("RP-TC-582")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Search by analytics attributes")
    public void runRpTc582() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        waitForConsistency(en);

        Map<String, List<AttributeModel>> posted = new HashMap<>();
        posted.put("TestName", singletonList(new SimpleAttributeModel("HO-HO-HO")));
        addAnalyticsAttribute(en, posted);

        //1
        waitForValue(() -> {
            List<EntityModel> found = searchEntitiesModels(new GetEntityRequestParameters()
                    .filter(equalsFilter("analyticsAttributes.TestName", "HO-HO-HO")));
            if (found.size() == 1)
                shouldBeAttrsEqual(found.get(0).getAnalyticsAttributes(), posted);
            return found.size();
        }, 1, "Only one entity should be found");

        //2
        List<EntityModel> found = searchEntitiesModels(new GetEntityRequestParameters().filter(startsWith("analyticsAttributes", "HO-HO-HO")));

        assertEquals(found.size(), 1, "Found entities count");
        shouldBeAttrsEqual(found.get(0).getAnalyticsAttributes(), posted);
    }

    @TmsLink("RP-TC-583")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Merge&Unmerge entity with analytics attributes")
    public void runRpTc583() throws Exception {
        //1
        EntityModel en1 = new EntityModel(ENTITY_TYPE_HCP).post();
        addAnalyticsAttribute(en1, "TestName", new SimpleAttributeModel("name1"));
        EntityModel en2 = new EntityModel(ENTITY_TYPE_HCP).post();
        addAnalyticsAttribute(en2, "TestName", new SimpleAttributeModel("name2"));
        //2
        mergeEntities(en1, en2, en1);
        assertEquals(0, en1.get().getAnalyticsAttributes().size(), "AnalyticsAttributes count");
        //3
        addAnalyticsAttribute(en1, "TestName", new SimpleAttributeModel("n3"));
        assertEquals("n3", en1.get().getAnalyticsAttributes("TestName").get(0).getAsSimple().getValue(), "TestName");
        //4
        unmergeEntities(en1, en2);
        assertEquals(0, en1.get().getAnalyticsAttributes().size(), "AnalyticsAttributes count");
        assertEquals(0, en2.get().getAnalyticsAttributes().size(), "AnalyticsAttributes count");
    }

    @TmsLink("RP-TC-2190")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Facet search by analytics attributes")
    public void runRpTc2190() throws Exception {
        GetEntityRequestParameters filter = new GetEntityRequestParameters().filter(startsWith("analyticsAttributes.TestLastName", "A"));

        EntityModel en1 = new EntityModel(ENTITY_TYPE_HCP).post();
        EntityModel en2 = new EntityModel(ENTITY_TYPE_HCP).post();
        EntityModel en3 = new EntityModel(ENTITY_TYPE_HCP).post();
        waitForConsistency(en1, en2, en3);

        SimpleAttributeModel anton = new SimpleAttributeModel("Anton");
        SimpleAttributeModel albert = new SimpleAttributeModel("Albert");
        SimpleAttributeModel aaa = new SimpleAttributeModel("Aaa");

        NestedAttributeModel nestedRU = (NestedAttributeModel) new NestedAttributeModel().addSubAttribute("First", new SimpleAttributeModel("Russia"));
        NestedAttributeModel nestedUS = (NestedAttributeModel) new NestedAttributeModel().addSubAttribute("First", new SimpleAttributeModel("USA"));
        NestedAttributeModel nestedUK = (NestedAttributeModel) new NestedAttributeModel().addSubAttribute("First", new SimpleAttributeModel("UK"));

        addAnalyticsAttribute(en1, "TestLastName", anton, albert, aaa);
        addAnalyticsAttribute(en2, "TestLastName", anton, albert);
        addAnalyticsAttribute(en3, "TestLastName", anton);
        waitForValue(() -> searchEntitiesModels(new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(containsWordStartingWith("analyticsAttributes.TestLastName", "A")))).size(), 3);

        addAnalyticsAttribute(en1, "TestNested", nestedRU, nestedUS, nestedUK);
        addAnalyticsAttribute(en2, "TestNested", nestedRU, nestedUS);
        addAnalyticsAttribute(en3, "TestNested", nestedRU);

        waitForValue(() -> searchEntitiesModels(new GetEntityRequestParameters()
                .filter(new SearchFilterGroup(containsWordStartingWith("analyticsAttributes.TestLastName", "A"))
                        .and(equalsFilter("analyticsAttributes.TestNested.First", "Russia")))).size(), 3);

        //1
        waitForValue(() -> new JsonDiff(searchFacetsJson(filter, loadFile("2190-body1.json")), loadJson("2190-etalon1.json")).isDifferent(), false);
        //2
        waitForValue(() -> new JsonDiff(searchFacetsJson(filter, loadFile("2190-body2.json")), loadJson("2190-etalon2.json")).isDifferent(), false);
    }

    @TmsLink("RP-TC-2837")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Search by analytics attributes Date")
    public void runRpTc2837() throws Exception {
        EntityModel en1 = new EntityModel(ENTITY_TYPE_HCP).post();
        EntityModel en2 = new EntityModel(ENTITY_TYPE_HCP).post();
        new EntityModel(ENTITY_TYPE_HCP).post();
        new EntityModel(ENTITY_TYPE_HCP).post();

        waitForConsistency(en1, en2);
        Long randomTimestamp1 = getRandomTimeStamp();
        String timestamp1 = Long.toString(randomTimestamp1);
        addAnalyticsAttribute(en1, "TestDate", new SimpleAttributeModel(timestamp1));

        String timestamp2 = Long.toString(getRandomTimeStamp());
        addAnalyticsAttribute(en2, "TestDate", new SimpleAttributeModel(timestamp2));
        //1
        waitForValue(() -> searchEntitiesModels(new GetEntityRequestParameters()
                .filter(equalsFilter("analyticsAttributes.TestDate", timestamp1))).size(), 1, "Size of entities with TestDate=" + timestamp1 + " should be 1");

        waitForSimilarList(singletonList(en1.getUri()), () -> searchEntitiesModels(new GetEntityRequestParameters()
                        .filter(equalsFilter("analyticsAttributes.TestDate", timestamp1)))
                        .stream().map(ReltioModel::getUri).toList()
                , "Entities with TestDate=" + timestamp1);

        waitForValue(() -> searchEntitiesModels(new GetEntityRequestParameters()
                        .filter(equalsFilter("analyticsAttributes.TestDate", timestamp1)))
                        .get(0).getAnalyticsAttributes("TestDate").get(0).getAsSimple().getValue()
                , getDateFromTimeStamp(randomTimestamp1), "Value of TestDate");
        //2
        waitForValue(() -> searchEntitiesModels(new GetEntityRequestParameters()
                .filter(exists("analyticsAttributes.TestDate"))).size(), 2, "Size of entities with existing analyticsAttributes.TestDate should be 2");

        waitForSimilarList(asList(en1.getUri(), en2.getUri()), () -> searchEntitiesModels(new GetEntityRequestParameters()
                        .filter(exists("analyticsAttributes.TestDate")))
                        .stream().map(ReltioModel::getUri).toList()
                , "Exists TestDate analytics attribute");
    }

    private void waitForSimilarList(List<String> expected, ThrowingSupplier<List<String>> f, String message) throws Exception {
        Predicate<List<String>> validator = (list) -> isListEquals(expected, list);
        List<String> result = SmartWaiting.waitForCondition(f, validator, SmartWaiting.getDefSleep() / 1000,
                SmartWaiting.getDefTriesCount());
        Assert.assertNotNull(result, "Response array should not be null");
        TestNGAssertHelper.assertListEquals(result, expected, message);
    }

    @TmsLink("RP-TC-581")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Check inheritance for analytics attributes")
    public void runRpTc581() throws Exception {
        //1
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        waitForConsistency(en);

        NestedAttributeModel nested = new NestedAttributeModel();
        nested.addAttributeToModel("Common", new SimpleAttributeModel("a"));
        nested.addAttributeToModel("NameI", new SimpleAttributeModel("b"));
        nested.addAttributeToModel("NameHCP", new SimpleAttributeModel("c"));

        Map<String, List<AttributeModel>> attrMap = new HashMap<>();
        attrMap.put("NameI", singletonList(new SimpleAttributeModel("name")));
        attrMap.put("NestedCommon", singletonList(nested));

        addAnalyticsAttribute(en, attrMap);
        //2
        TestNGAssertHelper.shouldBeAttrsEqual(en.get().getAnalyticsAttributes(), attrMap);
    }

    @TmsLink("RP-TC-8487")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Search analytic attributes by OV with filter=inSameAttribute")
    public void runRpTc8487() throws Exception {
        TenantManagementService.cleanTenant();
        setOvStrategy("STATIC", false);
        EntityModel en1 = new EntityModel(ENTITY_TYPE_HCO).post();
        EntityModel en2 = new EntityModel(ENTITY_TYPE_HCO).post();
        try {
            waitForConsistency(en1, en2);

            NestedAttributeModel nested = new NestedAttributeModel();
            nested.addAttributeToModel("CountPossibleDDs", new SimpleAttributeModel("1"));
            nested.addAttributeToModel("CountTotalTx", new SimpleAttributeModel("80"));
            nested.addAttributeToModel("PctLikelyDDs", new SimpleAttributeModel("80"));
            addAnalyticsAttribute(en1, "DDAnalysis", nested);

            SearchFilterGroup filter = new SearchFilterGroup(
                    equalsFilter("type", ENTITY_TYPE_HCO))
                    .and(inSameAttributeValue(new SearchFilterGroup
                            (equalsFilter("analyticsAttributes.DDAnalysis.CountPossibleDDs", "1"))
                            .and(range("analyticsAttributes.DDAnalysis.CountTotalTx", "75", "100"))
                            .and(range("analyticsAttributes.DDAnalysis.PctLikelyDDs", "75", "100"))));
            GetEntityRequestParameters params = new GetEntityRequestParameters()
                    .filter(filter)
                    .scoreEnabled(false)
                    .options("searchByOv")
                    .options("ovOnly")
                    .offset(0);

            waitForValue(() -> {
                List<EntityModel> found = searchEntitiesModels(params);
                if (found.size() == 1)
                    assertEquals(found.get(0).getUri(), en1.getUri(), "First entity should be found");
                return found.size();
            }, 1, "Only one entity should be found");

        } finally {
            en1.delete();
            en2.delete();
        }
    }

    private String getDateFromTimeStamp(long input) {
        return new SimpleDateFormat("yyyy-MM-dd").format(new Date(input));
    }

    private long getRandomTimeStamp() {
        long millisInDay = 60 * 60 * 24 * 1000;
        long randomDateOnly = (new Date().getTime() / millisInDay) * millisInDay - new Random().nextInt(30) * millisInDay;
        return new Date(randomDateOnly).getTime();
    }
}
