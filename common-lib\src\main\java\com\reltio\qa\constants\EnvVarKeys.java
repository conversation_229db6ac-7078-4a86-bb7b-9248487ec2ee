package com.reltio.qa.constants;

public class EnvVarKeys {

    public static final String KEY_ENV_VAR_AL_QUESTIONS = "AL_QUESTIONS";
    public static final String KEY_ENV_VAR_ANSWER_THRESHOLD_VALUE = "ANSWER_THRESHOLD_VALUE";
    public static final String KEY_ENV_VAR_API_HZ_ADDRESS = "API_HZ_ADDRESS";

    public static final String KEY_ENV_VAR_BRANCH_NAME = "BRANCH_NAME";
    public static final String KEY_ENV_VAR_BUILD_NUMBER = "BUILD_NUMBER";
    public static final String KEY_ENV_VAR_BUILD_TAG = "BUILD_TAG";

    public static final String KEY_ENV_VAR_CASSANDRA_HOSTS = "CASSANDRA_HOSTS";
    public static final String KEY_ENV_VAR_CassandraCluster = "CassandraCluster";
    public static final String KEY_ENV_VAR_CLOUD_TYPE = "CLOUD_TYPE";
    public static final String KEY_ENV_VAR_COMPUTERNAME = "COMPUTERNAME";
    public static final String KEY_ENV_VAR_CONFIG = "CONFIG";
    public static final String KEY_ENV_VAR_CONFIG_FILE = "CONFIG_FILE";
    public static final String KEY_ENV_VAR_CONFIG_PATH = "CONFIG_PATH";
    public static final String KEY_ENV_VAR_CONFIG_SECTION = "CONFIG_SECTION";
    public static final String KEY_ENV_VAR_CONFIG_USERS_SECTION = "CONFIG_USERS_SECTION";
    public static final String KEY_ENV_VAR_CUSTOM_EVENT_PROCESSORS = "CUSTOM_EVENT_PROCESSORS";

    public static final String KEY_ENV_VAR_daJarType = "daJarType";
    public static final String KEY_ENV_VAR_DELETE_FLOW = "DELETE_FLOW";
    public static final String KEY_ENV_VAR_DELETE_SCALABLE_TARGETS = "DELETE_SCALABLE_TARGETS";
    public static final String KEY_ENV_VAR_DONT_UPDATE_VERTICALS = "DONT_UPDATE_VERTICALS";
    public static final String KEY_ENV_VAR_DYNAMODB_ENDPOINT = "DYNAMODB_ENDPOINT";

    public static final String KEY_ENV_VAR_ElasticSearchCluster = "ElasticSearchCluster";
    public static final String KEY_ENV_VAR_ENV_NAME = "ENV_NAME";
    public static final String KEY_ENV_VAR_ENVS = "ENVS";
    public static final String KEY_ENV_VAR_ES_HOSTS = "ES_HOSTS";
    public static final String KEY_ENV_VAR_ES_VERSION = "ES_VERSION";
    public static final String KEY_ENV_VAR_EXPORT_COMMENTS = "EXPORT_COMMENTS";

    public static final String KEY_ENV_VAR_FAIL_IF_INCONSISTENT = "FAIL_IF_INCONSISTENT";
    public static final String KEY_ENV_VAR_FULL_EXPORT = "FULL_EXPORT";

    public static final String KEY_ENV_VAR_GBQ_DATASET_NAME = "GBQ_DATASET_NAME";
    public static final String KEY_ENV_VAR_GCP_PROJECT_ID = "GCP_PROJECT_ID";
    public static final String KEY_ENV_VAR_GCS_BUCKET_NAME = "GCS_BUCKET_NAME";
    public static final String KEY_ENV_VAR_GENERATE_DATA = "GENERATE_DATA";

    public static final String KEY_ENV_VAR_HOSTNAME = "HOSTNAME";
    public static final String KEY_ENV_VAR_HZ_GROUP_NAME = "HZ_GROUP_NAME";

    public static final String KEY_ENV_VAR_LIMIT_DAYS = "LIMIT_DAYS";
    public static final String KEY_ENV_VAR_LOGNAME = "LOGNAME";

    public static final String KEY_ENV_VAR_MODE = "MODE";

    public static final String KEY_ENV_VAR_NEW_SPANNER_INTERACTIONS = "NEW_SPANNER_INTERACTIONS";

    public static final String KEY_ENV_VAR_PREPARE_COMPONENTS = "PREPARE_COMPONENTS";
    public static final String KEY_ENV_VAR_PROJECT_ALIAS = "PROJECT_ALIAS";

    public static final String KEY_ENV_VAR_QA_VAULT_PATH = "QA_VAULT_PATH";
    public static final String KEY_ENV_VAR_QA_VAULT_TOKEN = "QA_VAULT_TOKEN";
    public static final String KEY_ENV_VAR_FAIL_ON_NO_VAULT_CONNECTION = "FAIL_ON_NO_VAULT_CONNECTION";

    public static final String KEY_ENV_VAR_READ_TIMEOUT = "READ_TIMEOUT";
    public static final String KEY_ENV_VAR_RELEASE_VERSION = "RELEASE_VERSION";
    public static final String KEY_ENV_VAR_REQUIRED_COMPONENTS = "REQUIRED_COMPONENTS";
    public static final String KEY_ENV_VAR_RP_NAME = "RP_NAME";
    public static final String KEY_ENV_VAR_RUN_ANALYZE_ATTRIBUTES = "RUN_ANALYZE_ATTRIBUTES";
    public static final String KEY_ENV_VAR_RUN_MODE = "RUN_MODE";


    public static final String KEY_ENV_VAR_SERVICE_NAME = "SERVICE_NAME";
    public static final String KEY_ENV_VAR_SKIP_HISTORY_CLEANUP = "SKIP_HISTORY_CLEANUP";
    public static final String KEY_ENV_VAR_SKIP_REQUEST_DETAILS_IN_LOGS = "SKIP_REQUEST_DETAILS_IN_LOGS";
    public static final String KEY_ENV_VAR_SNOWFLAKE_AWS_ROLE = "SNOWFLAKE_AWS_ROLE";
    public static final String KEY_ENV_VAR_SNOWFLAKE_BUCKET_NAME = "SNOWFLAKE_BUCKET_NAME";
    public static final String KEY_ENV_VAR_SPANNER_EMULATOR_URL = "SPANNER_EMULATOR_URL";
    public static final String KEY_ENV_VAR_SPANNER_INSTANCE_ID = "SPANNER_INSTANCE_ID";
    public static final String KEY_ENV_VAR_STARTING_OFFSET = "STARTING_OFFSET";
    public static final String KEY_ENV_VAR_STEPS = "STEPS";

    public static final String KEY_ENV_VAR_TEAM_NAME = "TEAM_NAME";
    public static final String KEY_ENV_VAR_TEST_RUN_TYPE = "TEST_RUN_TYPE";
    public static final String KEY_ENV_VAR_TESTLINK_BUILD_NAME = "TESTLINK_BUILD_NAME";
    public static final String KEY_ENV_VAR_TESTNG_THREAD_COUNT = "TESTNG_THREAD_COUNT";
    public static final String KEY_ENV_VAR_TOTAL = "TOTAL";

    public static final String KEY_ENV_VAR_USE_DEVOPS_API = "USE_DEVOPS_API";
    public static final String KEY_ENV_VAR_USE_DEVOPS_ETALON_URL = "USE_DEVOPS_ETALON_URL";
    public static final String KEY_ENV_VAR_USE_INDEXES = "USE_INDEXES";
    public static final String KEY_ENV_VAR_USE_SPANNER_CLOUD_FUNCTION = "USE_SPANNER_CLOUD_FUNCTION";
    public static final String KEY_ENV_VAR_USER = "USER";
    public static final String KEY_ENV_VAR_USERNAME = "USERNAME";
    public static final String KEY_ENV_VAR_USERS_SECTION = "USERS_SECTION";

    public static final String KEY_ENV_VAR_workato_api_collection_url = "workato_api_collection_url";
    public static final String KEY_ENV_VAR_workato_client_id = "workato_client_id";
    public static final String KEY_ENV_VAR_workato_client_secret = "workato_client_secret";
    public static final String KEY_ENV_VAR_WRITE_JIRA_COMMENTS = "WRITE_JIRA_COMMENTS";

}
