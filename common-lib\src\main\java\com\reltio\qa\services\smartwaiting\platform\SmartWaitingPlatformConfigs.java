package com.reltio.qa.services.smartwaiting.platform;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;
import com.reltio.qa.Config;
import com.reltio.qa.enums.common.Service;
import com.reltio.qa.json.JsonDiff;
import com.reltio.qa.model.GeneratorModel;
import com.reltio.qa.model.PermissionsConfigModel;
import com.reltio.qa.model.TenantModel;
import com.reltio.qa.model.platform.messaging.MessagingModel;
import com.reltio.qa.request.Request;
import com.reltio.qa.services.CleanseService;
import com.reltio.qa.services.EnvironmentService;
import com.reltio.qa.services.GeneratorService;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.services.smartwaiting.SmartWaitingExport;
import com.reltio.qa.services.smartwaiting.allpods.WaitSyncOfAllPods;
import com.reltio.qa.services.tenant.config.TenantManagementServiceForInteractions;
import com.reltio.qa.utils.GsonUtils;
import lombok.extern.log4j.Log4j2;

import java.util.*;

import static com.reltio.qa.request.Request.Type.GET;
import static com.reltio.qa.services.AccountService.ADMIN_NAME;
import static com.reltio.qa.services.EnvironmentService.fixResponseForSystemConfig;
import static com.reltio.qa.services.smartwaiting.allpods.WaitSyncOfAllPods.DEFAULT_ERROR_MESSAGE_NOT_ALL_PODS_ARE_SYNC;

@Log4j2
public final class SmartWaitingPlatformConfigs {

    private SmartWaitingPlatformConfigs() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    //-------------
    // System

    /**
     * Ensures that systemConfig (GET /admin/systemConfig) is the same on all api nodes
     */
    public static void waitSystemConfigConsistent() {
        new WaitSyncOfAllPods()
                .setCheckedRequest(EnvironmentService.getRequestForSystemConfig())
                .setCheckCondition(
                        uniqueResponses -> {
                            if (uniqueResponses.size() == 1) {
                                return null;
                            } else {
                                // systemConfig may contain section "tps_config", which contains "PID" parameter. PID is always different on each node. Remove this parameter from json before comparing
                                // remove runtime, fix java and os version comparison in tps_config
                                Set<JsonElement> fixedResponses = new HashSet<>();
                                uniqueResponses.forEach(r -> fixedResponses.add(fixResponseForSystemConfig(r)));
                                if (fixedResponses.size() == 1) {
                                    return null;
                                }
                                return "System config is different on API nodes";
                            }
                        }
                )
                .setFailAfterAll(false)
                .waitSyncOfAllPods();
    }

    //-------------
    // Verticals

    public static void waitVerticalUpdated(String uri) {
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/admin/" + uri)
                .setFailAfterAll(false)
                .waitSyncOfAllPods();
    }

    //-------------
    // Tenant - PhysConfig

    // TODO strange method with if/else that can be reworked and collapsed with waitForTenantConfigurationUpdated(tenantId);
    public static void waitForTenantConfigurationUpdated(JsonElement tenantPhysConfigExpected) {
        String tenantId = tenantPhysConfigExpected.getAsJsonObject().get("tenantId").getAsString();
        String tenantName = tenantPhysConfigExpected.getAsJsonObject().get("tenantName").getAsString();

        if (tenantName.split(":").length == 3) {
            String checkedUrlForGETRequest = Config.getPlatformUrl() + "/tenants/" + tenantId + "?safeMode=false"; // ?safeMode=false -> from cache of the pod + tenant has to be initialized
            new WaitSyncOfAllPods()
                    .setCheckedRequest(checkedUrlForGETRequest)
                    .setService(Service.API)
                    .setCheckCondition(
                            uniqueResponses -> uniqueResponses.stream()
                                    .allMatch(response -> response.getAsJsonObject().get("tenantName").getAsString().equals(tenantName))
                                    ? null : DEFAULT_ERROR_MESSAGE_NOT_ALL_PODS_ARE_SYNC)
                    .waitSyncOfAllPods();
        } else {
            waitForTenantConfigurationUpdated(tenantId);
        }
    }

    public static void waitForTenantConfigurationUpdated() {
        waitForTenantConfigurationUpdated(Config.getTenantId());
    }

    public static void waitForTenantConfigurationUpdated(String tenantId) {
        log.info("Wait. Syncing physical-configuration' for '{}' across all Platform pods (?safeMode=true - additionally will try to get config from DB ignoring cache [tenant can be NOT initialized yet])", tenantId);
        new WaitSyncOfAllPods()
                .setCheckedRequest(new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "?safeMode=true"))  // ?safeMode=true -> additionally will try to get config from DB ignoring cache (tenant can be NOT initialized yet)
                .waitSyncOfAllPods();

        log.info("Wait. Syncing physical-configuration' for '{}' across all Platform pods (?safeMode=false - from cache of the pod + tenant HAS TO BE Initialized)", tenantId);
        new WaitSyncOfAllPods()
                .setCheckedRequest(new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "?safeMode=false")) // ?safeMode=false -> from cache of the pod + tenant HAS TO BE Initialized
                .waitSyncOfAllPods();
    }

    //-------------
    // Tenant - PhysConfig - "optionalParameters"

    /**
     * Think to use {@link #waitForOptionalParametersSyncedAndContains} as it will be more strict and correct check
     */
    public static void waitForOptionalParametersSynced(String tenantId) {
        log.info("Wait. Syncing 'optionalParameters' for '{}' across all Platform pods (full phys-config; try to ignore cache of the pod)", tenantId);
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/tenants/" + tenantId + "/?safeMode=true")
                .waitSyncOfAllPods();
        log.info("Wait. Syncing 'optionalParameters' for '{}' across all Platform pods (full phys-config; from cache of the pod)", tenantId);
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/tenants/" + tenantId + "/?safeMode=false")
                .waitSyncOfAllPods();
        log.info("Wait. Syncing 'optionalParameters' for '{}' across all Platform pods (dedicated endpoint)", tenantId);
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/tenants/" + tenantId + "/optionalParameters")
                .waitSyncOfAllPods();
    }

    public static void waitForOptionalParametersSyncedAndContains(String tenantId, Map<String, Object> optionalParametersExpectedSubMap) {
        if (optionalParametersExpectedSubMap == null || optionalParametersExpectedSubMap.isEmpty()) {
            throw new IllegalArgumentException("'optionalParametersExpectedSubMap' cannot be 'null' or empty but it is: " + optionalParametersExpectedSubMap);
        }

        String postfix = "And contains sub-map %s".formatted(optionalParametersExpectedSubMap);

        log.info("Wait. Syncing 'optionalParameters' for '{}' across all Platform pods (full phys-config; try to ignore cache of the pod). {}", tenantId, postfix);
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/tenants/" + tenantId + "/?safeMode=true")
                .setCheckCondition(
                        uniqueResponses -> uniqueResponses.stream()
                                .map(response -> TenantModel.fromJson(response.getAsJsonObject()).getOptionalParameters())
                                .allMatch(optionalParametersActual -> optionalParametersActual != null && optionalParametersActual.entrySet().containsAll(optionalParametersExpectedSubMap.entrySet())
                                ) ? null : "Not all Platform pods contains requested sub-map"
                )
                .waitSyncOfAllPods();

        log.info("Wait. Syncing 'optionalParameters' for '{}' across all Platform pods (full phys-config; from cache of the pod). {}", tenantId, postfix);
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/tenants/" + tenantId + "/?safeMode=false")
                .setCheckCondition(
                        uniqueResponses -> uniqueResponses.stream()
                                .map(response -> TenantModel.fromJson(response.getAsJsonObject()).getOptionalParameters())
                                .allMatch(optionalParametersActual -> optionalParametersActual != null && optionalParametersActual.entrySet().containsAll(optionalParametersExpectedSubMap.entrySet())
                                ) ? null : "Not all Platform pods contains requested sub-map"
                )
                .waitSyncOfAllPods();

        log.info("Wait. Syncing 'optionalParameters' for '{}' across all Platform pods (dedicated endpoint). {}", tenantId, postfix);
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getPlatformUrl() + "/tenants/" + tenantId + "/optionalParameters")
                .setCheckCondition(
                        uniqueResponses -> uniqueResponses.stream()
                                .map(response -> (Map<String, Object>) new Gson().fromJson(response, new TypeToken<Map<String, Object>>() {
                                }.getType()))
                                .allMatch(optionalParametersActual -> optionalParametersActual != null && optionalParametersActual.entrySet().containsAll(optionalParametersExpectedSubMap.entrySet())
                                ) ? null : "Not all Platform pods contains requested sub-map"
                )
                .waitSyncOfAllPods();
    }

    //-------------
    // Tenant - PhysConfig - "dataStorageConfig"

    /**
     * Wait for the repository version for spanner storage set to V2 in the tenant's config
     */
    public static void waitForRepositoryVersionSetToV2InConfig() {
        waitForTenantConfigurationUpdated();
        SmartWaiting.waitForValue(TenantManagementServiceForInteractions::isInteractionsRepositoryVersionSetToV2, true);
    }

    //-------------
    // Tenant - PhysConfig - "searchStorageConfiguration"

    /**
     * Waits for search storage configuration values from physical configuration to be as expected on all nodes
     */
    public static void waitForSearchConfigUpdated(String tenant, String setting, String value) {
        log.info("Waiting for search configuration updated for '{}' on all nodes", tenant);
        String url = Config.getPlatformUrl() + "/tenants/" + tenant;
        new WaitSyncOfAllPods()
                .setCheckedRequest(url)
                .setCheckCondition(
                        uniqueResponses -> uniqueResponses.stream()
                                .allMatch(
                                        response -> response.getAsJsonObject()
                                                .get("searchStorageConfiguration").getAsJsonObject()
                                                .get(setting).getAsString()
                                                .equals(value)
                                )
                                ? null : ("Search configuration was not updated \nUnique Responses: " + uniqueResponses)
                )
                .setFailAfterAll(false)
                .waitSyncOfAllPods();
    }

    //-------------
    // Tenant - PhysConfig - "exportConfig"

    /**
     * Waits for 'exportConfig' have exact setting into physical-configuration on all Platform pods
     */
    public static void waitExportSettingOnAllPlatformPods(String tenantId, String setting, String value) {
        SmartWaitingExport.waitExportSettingOnAllPlatformPods(tenantId, setting, value);
    }

    //-------------
    // Tenant - PhysConfig - "cleanseConfig"

    /**
     * Will wait when Tenant-Configuration is updated and after that</br>
     * will check cleanse config via '/cleanse' endpoint with cleanse-functions what was implemented during update
     *
     * @param tenantId         destination
     * @param cleanseFunctions
     */
    public static void waitUpdateCleanseViaEndpoint(String tenantId, String cleanseFunctions) {
        log.info("Wait. Updating of physical configuration of '{}' on all nodes via '/cleanse' endpoint", tenantId);
        SmartWaiting.RepeatedQueue rq;
        rq = () -> {
            try {
                waitForTenantConfigurationUpdated(tenantId);
                String cleanseFunctionsActual = CleanseService.getCleanseFunctionsAsString(tenantId);
                if (new JsonDiff(cleanseFunctions, cleanseFunctionsActual).isDifferent()) {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, "Applying of new 'cleanse' section failed");
                } else {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.SUCCESSFUL);
                }
            } catch (Exception e) {
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, e.getMessage());
            }
        };
        SmartWaiting.waitFor(rq);
    }

    //-------------
    // Tenant - PhysConfig - "messagingConfig"

    public static void waitForMessagingUpdated(String tenant, int hashCode) {
        log.info("Waiting for messaging configuration updated for '{}' on all nodes", tenant);
        SmartWaiting.waitForAllPlatformPodsSynchronized(
                new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenant + "/messaging"),
                uniqueResponses -> {
                    if (uniqueResponses.size() == 1 && uniqueResponses.toArray()[0].toString().hashCode() == hashCode) {
                        return null;
                    } else {
                        return DEFAULT_ERROR_MESSAGE_NOT_ALL_PODS_ARE_SYNC;
                    }
                }
        );
    }

    public static void waitForMessagingDestination(String messagingPrefix, String tenantName) {
        SmartWaiting.RepeatedQueue rq;
        rq = () -> {
            try {
                String response = new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantName + "/messaging").execute();
                List<MessagingModel> msgModels = GsonUtils.getGson().fromJson(response, new TypeToken<List<MessagingModel>>() {
                }.getType());
                if (msgModels.stream().anyMatch(model -> model.name.startsWith(messagingPrefix))) {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.SUCCESSFUL);
                } else {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, "Waiting for destination with prefix " + messagingPrefix);
                }
            } catch (Exception e) {
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, e.getMessage());
            }
        };
        SmartWaiting.waitFor(rq);
    }

    //-------------
    // Tenant - MetaConfig

    /**
     * Experimental waiters for check last level of config (_noInheritance) on all api nodes
     */
    public static void waitMetaConfigUpdatedWithNoInheritance(String tenantId) {
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getTenantUrl(tenantId) + "/configuration/_noInheritance")
                .setFailAfterAll(false)
                .waitSyncOfAllPods();
    }

    /**
     * Wait for configuration on all nodes is synchronized by Timestamps
     *
     * @param tenantId  destination
     * @param timestamp value of "description" at the config
     */
    public static void waitMetaConfigUpdated(String tenantId, String timestamp) {
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getTenantUrl(tenantId) + "/configuration") // https://reltio.jira.com/browse/RP-60092
                .setCheckCondition(
                        uniqueResponses -> uniqueResponses.stream()
                                .allMatch(response -> response.getAsJsonObject().get("description").getAsString().equals(timestamp))
                                ? null : DEFAULT_ERROR_MESSAGE_NOT_ALL_PODS_ARE_SYNC
                )
                .waitSyncOfAllPods();
    }

    /**
     * Wait for configuration on all nodes is synchronized by ETag
     *
     * @param tenantId destination
     * @param eTag     value of "ETag" of the config
     */
    public static void waitMetaConfigUpdatedByETag(String tenantId, String eTag) {
        new WaitSyncOfAllPods()
                .setCheckedRequest(Config.getTenantUrl(tenantId) + "/configuration/_etag")
                .setCheckCondition(
                        uniqueResponses -> {
                            List<String> values = uniqueResponses.stream()
                                    .map(response -> response.getAsJsonObject().get("hash").getAsString())
                                    .distinct().toList();
                            return values.size() == 1 && !values.get(0).equals(eTag) ? null : DEFAULT_ERROR_MESSAGE_NOT_ALL_PODS_ARE_SYNC;
                        }
                )
                .waitSyncOfAllPods();
    }

    //-------------
    // Tenant - MetaConfig - "ratings"

    public static void waitRatingConfigUpdated(String tenantId) {
        SmartWaiting.waitForAllPlatformPodsSynchronized(new Request(ADMIN_NAME, GET, Config.getTenantUrl(tenantId) + "/ratingConfiguration"));
    }

    //-------------
    // Tenant - MetaConfig - "matchGroups"

    public static void waitMatchGroupsUpdated(String tenantId, String entityType, JsonArray expectedMatchGroups) {
        SmartWaitingPlatformMatch.waitMatchGroupsUpdated(tenantId, entityType, expectedMatchGroups);
    }

    public static void waitHiddenMatchGroupsUpdated(String tenantId, String entityType, JsonArray expectedMatchGroups) {
        SmartWaitingPlatformMatch.waitHiddenMatchGroupsUpdated(tenantId, entityType, expectedMatchGroups);
    }

    //-------------
    // Tenant - Permissions

    public static void waitForPermissionsUpdated() {
        waitForPermissionsUpdated(Config.getTenantId());
    }

    public static void waitForPermissionsUpdated(String tenantId) {
        SmartWaiting.waitForAllPlatformPodsSynchronized(new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/permissions/" + tenantId));
    }

    public static void waitForPermissionsUpdated(String tenantId, String expectedResult) {
        SmartWaiting.waitForAllPlatformPodsSynchronized(
                new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/permissions/" + tenantId),
                uniqueResponses -> uniqueResponses.stream()
                        .allMatch(response -> expectedResult.equals(response.toString()))
                        ? null : DEFAULT_ERROR_MESSAGE_NOT_ALL_PODS_ARE_SYNC
        );
    }

    public static void waitForPermissionsUpdated(PermissionsConfigModel expectedPermissions) {
        log.info("Wait. For tenant permissions are updated");
        SmartWaiting.waitForAllPlatformPodsSynchronized(
                new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/permissions/" + Config.getTenantId()),
                uniqueResponses -> uniqueResponses.stream()
                        .map(JsonElement::getAsJsonArray)
                        .map(PermissionsConfigModel::new)
                        .allMatch(permission -> permission.equals(expectedPermissions))
                        ? null : "Not all nodes are synchronized on update tenant permissions"
        );
    }

    public static void waitForPermissionsRemoved() {
        SmartWaiting.waitForAllPlatformPodsSynchronized(
                new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/permissions/" + Config.getTenantId()),
                uniqueResponses -> uniqueResponses.stream()
                        .allMatch(response -> response.getAsJsonArray().isEmpty())
                        ? null : "Not all nodes are synchronized on remove tenant permissions"
        );
    }

    //-------------
    // Tenant - Lookups

    public static void waitLookupsUpdated(String tenant, String time) {
        SmartWaiting.RepeatedQueue rq;
        Request getLookups = new Request(ADMIN_NAME, GET, Config.getTenantUrl(tenant) + "/lookups");
        rq = () -> {
            try {
                String response = getLookups.execute();
                if (!response.contains(time)) {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, "Lookups haven't been updated yet.");
                }
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.SUCCESSFUL);
            } catch (Exception e) {
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, e.getMessage());
            }
        };
        //Wait for some of the nodes has actual lookups (this was added because lookups has cache which returns old data)
        SmartWaiting.waitFor(rq);
        //Wait for all nodes are synchronized
        SmartWaiting.waitForAllPlatformPodsSynchronized(getLookups);
    }

    public static void waitLookupsDeleted(String tenant) {
        SmartWaiting.RepeatedQueue rq;
        Request getLookups = new Request(ADMIN_NAME, GET, Config.getTenantUrl(tenant) + "/lookups");
        rq = () -> {
            try {
                String response = getLookups.execute();
                if (!response.equals("{}")) {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, "Lookups haven't been deleted yet.");
                }
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.SUCCESSFUL);
            } catch (Exception e) {
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, e.getMessage());
            }
        };
        //Wait for some of the nodes has actual lookups (this was added because lookups has cache which returns old data)
        SmartWaiting.waitFor(rq);
        //Wait for all nodes are synchronized
        SmartWaiting.waitForAllPlatformPodsSynchronized(getLookups);
    }

    //-------------
    // Environment - Generators

    public static void waitForGenerator(GeneratorModel generator, boolean exist) {
        log.info("Wait. Generator {}{}exist", generator, exist ? " " : " doesn't ");
        SmartWaiting.RepeatedQueue rq;
        rq = () -> {
            try {
                if (GeneratorService.isGeneratorExist(generator) ^ exist) {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, "Generator '" + generator.getName() + "' wasn't " + (exist ? "registered" : "removed"));
                } else {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.SUCCESSFUL);
                }
            } catch (Exception e) {
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, e.getMessage());
            }
        };
        SmartWaiting.waitFor(rq);
    }

    public static void waitForGenerators(Collection<GeneratorModel> generators, boolean exist) {
        log.info("Wait. Generators {}{}exist", generators, exist ? " " : " don't ");
        SmartWaiting.RepeatedQueue rq;
        rq = () -> {
            try {
                if (GeneratorService.isGeneratorsExist(generators) ^ exist) {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, "Not all generators were " + (exist ? "registered" : "removed"));
                } else {
                    return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.SUCCESSFUL);
                }
            } catch (Exception e) {
                return new SmartWaiting.QueueResult<>(SmartWaiting.QueueStatus.FAILED, null, e.getMessage());
            }
        };
        SmartWaiting.waitFor(rq);
    }

    //-------------
    // ??? - Others

}
