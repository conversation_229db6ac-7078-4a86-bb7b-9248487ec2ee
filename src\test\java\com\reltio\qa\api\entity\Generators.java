package com.reltio.qa.api.entity;

import com.google.common.collect.Sets;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.reltio.qa.Config;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.api.attributes.NestedAttributes;
import com.reltio.qa.enums.tasks.ExecutionType;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.exceptions.ReltioObjectException;
import com.reltio.qa.model.*;
import com.reltio.qa.request.Request;
import com.reltio.qa.service.retry.RetryAnalyzer;
import com.reltio.qa.service.retry.RetryAnalyzerWithClean;
import com.reltio.qa.services.*;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.utils.ExceptionUtils;
import com.reltio.qa.utils.RandomGenerator;
import com.reltio.qa.utils.ThreadUtils;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.reltio.qa.helpers.CommonStrings.*;
import static com.reltio.qa.helpers.TestNGAssertHelper.shouldHaveAttribute;
import static com.reltio.qa.services.BusinessConfigurationService.updateConfig;
import static com.reltio.qa.services.GeneratorService.*;
import static com.reltio.qa.services.TenantManagementService.cleanTenantInternal;
import static com.reltio.qa.services.TenantManagementService.getTenant;
import static org.testng.Assert.*;

@Log4j2
public class Generators extends TestNGBaseTest {

    private static final int TIME_BETWEEN_TASK_STATUS_REQUEST = 5; //5 seconds
    private static final int WAITING_TIME = 300; //5 minutes
    private static final String CROSSWALK_TYPE_HMS_NPI = CROSSWALK_PREFIX + "/HMS_NPI";
    private static final String ATTRIBUTE_HCP_PREFIX = "configuration/entityTypes/HCP/attributes/";
    private static final String PATTERN = "[0-9a-f-]{36}";
    private String nameAttrSimple = "FirstName";
    private String patternAttrSimple = "Simple_" + PATTERN;
    private String nameAttrNested = "License";
    private String patternAttrNested = "Nested_" + PATTERN;
    private String patternCrosswalk = "Source_" + PATTERN;

    private Set<String> generatorsUsed = new HashSet<>();

    public static String getUUID(String line) {
        return line.substring(line.length() - 36);
    }

    private RandomGenerator randomGenerator = RandomGenerator.getInstance();

    @BeforeClass(alwaysRun = true)
    public void bcGenerators() throws Exception {
        GeneratorService.removeAllGenerators("auto");
        TenantManagementService.cleanTenant(Config.getTenantId());

        //Creation of generator
        List<GeneratorModel> generators = new ArrayList<>();
        GeneratorModel generator1 = new GeneratorModel();
        generators.add(generator1);
        GeneratorModel generator2 = new GeneratorModel();
        generators.add(generator2);

        String generatorName = generator1.getName();
        registerGenerators(generators);

        //Vars
        setVar("GENERATOR_UUID", generatorName, true);
        setVar("GENERATOR_NAME", generatorName, true);
        setVar("GENERATOR_NAME_2", generator2.getName(), true);
        setVar("GENERATOR_TYPE", "UUID", true);
        setVar("UUID_VERSION", "4", true);
    }

    @AfterClass
    public void acGenerators() throws Exception {
        removeUsedGenerators();
    }

    @TmsLink("RP-TC-5810")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "autoGenerated true for simple with pattern")
    public void runRpTc5810() throws Exception {
        //update of tenant config
        BusinessConfigurationService.updateConfig(loadFile("TC-5810-biz-conf.json"));
        // #1
        //create HCP and check that it's name contains generated value
        setVar("RANDOM_VALUE", RandomGenerator.getInstance().getUniqueRandomNumbers(8), true);

        EntityModel em = new EntityModel(loadFile("TC-5810-HCP.json"));
        em.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em.post();
        SmartWaiting.waitForConsistency(em);

        // Checks
        String nameFromEntity = em.getAttribute(nameAttrSimple).getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, nameFromEntity);
    }

    @TmsLink("RP-TC-5811")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "autoGenerated true for crosswalk")
    public void runRpTc5811() throws Exception {
        BusinessConfigurationService.updateConfig(loadFile("TC-5811-biz-conf.json"));
        // #1
        //create HCP and check that it's name contains generated value
        setVar("RANDOM_VALUE", RandomGenerator.getInstance().getUniqueRandomNumbers(10), true);
        EntityModel em = new EntityModel(loadFile("TC-5811-HCP.json"));
        em.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em.post();
        SmartWaiting.waitForConsistency(em);
        List<CrosswalkModel> startObjectCrosswalksList = em.getCrosswalks();

        patternCrosswalk = "Source_[0-9a-f-]{36}";
        for (CrosswalkModel cr : startObjectCrosswalksList) {
            if (CROSSWALK_TYPE_HMS.equals(cr.getType())) {
                shouldMatchToPattern(patternCrosswalk, cr.getValue());
                return;
            }
        }
        fail("Can't find Crosswalk");
    }

    //cleanse should be enabled for this test
    @TmsLink("RP-TC-5829")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Cleanse of autogenerated values")
    public void runRpTc5829() throws Exception {
        String randomValue = RandomGenerator.getInstance().getUniqueRandomNumbers(8);
        setVar("RANDOM_VALUE", randomValue, true);
        BusinessConfigurationService.updateConfig(loadFile("TC-5829-biz-conf.json"));
        // #1
        //create Location
        EntityModel em = new EntityModel(loadFile("TC-5829-Location.json")).post();
        SmartWaiting.waitForConsistency(em);

        SmartWaiting.waitForCrosswalksNumber(em, 2);
        List<CrosswalkModel> crosswalkList = em.getCrosswalks();
        String SOURCE_TYPE1 = "configuration/sources/" + randomValue;
        String SOURCE_ReltioCleanser = "configuration/sources/ReltioCleanser";

        for (CrosswalkModel item : crosswalkList) {
            String itemType = item.getType();
            String itemValue = item.getValue();

            if (SOURCE_TYPE1.equals(itemType)) {
                shouldMatchToPattern(patternCrosswalk, itemValue);
            } else if (SOURCE_ReltioCleanser.equals(itemType)) {
                shouldMatchToPattern(patternCrosswalk, itemValue, false);
            }
        }
    }

    @TmsLink("RP-TC-5813")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Update business configuration with generator rules for nested attributes")
    public void runRpTc5813() throws Exception {
        try {
            BusinessConfigurationService.updateConfig(loadFile("TC-5813-biz-conf-nested.json"));
            // #1
            EntityModel e1 = new EntityModel(loadFile("TC-5813-HCP.json"));
            e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                    RandomGenerator.getInstance().getRandomAlphabeticString(16)));
            e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                    RandomGenerator.getInstance().getRandomAlphabeticString(16)));
            e1.post();
            SmartWaiting.waitForConsistency(e1);

            NestedAttributeModel attrNested = e1.getAttribute(nameAttrNested).getAsNested();
            String numberFromEntity = attrNested.getValue().get("Number").get(0).getValue().toString();
            String stateFromEntity = attrNested.getValue().get("State").get(0).getValue().toString();
            String statusFromEntity = attrNested.getValue().get("Status").get(0).getValue().toString();

            patternAttrNested = "[0-9a-f-]{36}";
            shouldMatchToPattern(patternAttrNested, numberFromEntity);
            patternAttrNested = "test_[0-9a-f-]{36}";
            shouldMatchToPattern(patternAttrNested, stateFromEntity);

            assertEquals(statusFromEntity, "test", "Status is incorrect");
        } finally {
            patternAttrNested = "Nested_" + PATTERN;
        }
    }

    @TmsLink("RP-TC-5994")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Crosswalk reusing if generated value")
    public void runRpTc5994() throws Exception {
        try {
            addRandomGenerator("GENERATOR_NAME_IN_TEST");
            BusinessConfigurationService.updateConfig(loadFile("TC-5994-biz-conf.json"));
            // #1
            EntityModel e1 = new EntityModel(loadFile("TC-5994-HCP.json")).post();
            SmartWaiting.waitForConsistency(e1);
            String initialValueFromEntity = e1.getCrosswalks().get(0).getValue();
            String initialUriFromEntity = e1.getCrosswalks().get(0).getUri();

            patternCrosswalk = "Simple_[0-9a-f-]{36}";
            shouldMatchToPattern(patternCrosswalk, initialValueFromEntity);
            // #2
            EntityModel e2 = new EntityModel(loadFile("TC-5994-HCP.json")).post();
            SmartWaiting.waitForConsistency(e2);

            String valueFromEntity2 = e2.getCrosswalks().get(0).getValue();
            String uriFromEntity2 = e2.getCrosswalks().get(0).getUri();
            CrosswalkModel cm = e2.getCrosswalks().get(0);
            assertNotEquals(valueFromEntity2, initialValueFromEntity, "Crosswalk from Step 2 value equals previous");
            assertNotEquals(uriFromEntity2, initialUriFromEntity, "Crosswalk URI from Step 2 equals previous");

            // #3
            EntityModel e3 = new EntityModel(loadFile("TC-5994-HCP.json"));
            e3.deleteAllCrosswalksFromModel();
            e3.addCrosswalk(cm);
            e3.post();
            SmartWaiting.waitForConsistency(e3);
            String valueFromEntity3 = e3.getCrosswalks().get(0).getValue();
            String uriFromEntity3 = e3.getCrosswalks().get(0).getUri();
            assertEquals(valueFromEntity3, valueFromEntity2, "Crosswalk value changed on step 3");
            assertEquals(uriFromEntity3, uriFromEntity2, "Crosswalk URI changed on step 3");
        } finally {
            patternCrosswalk = "Source_" + PATTERN;
        }
    }

    @TmsLink("RP-TC-5815")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "GenerateIfEmpty true, value was sent/not sent, value by crosswalk exist/not exist")
    public void runRpTc5815() throws Exception {
        BusinessConfigurationService.updateConfig(loadFile("TC-5815-biz-conf.json"));
        nameAttrSimple = "FirstName";
        patternAttrSimple = "Simple_[0-9a-f-]{36}";
        //step 1
        EntityModel e1 = new EntityModel(loadFile("TC-5815-HCP-Step2.json"));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        String attrSimpleValueE1 = e1.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, attrSimpleValueE1);
        // #2
        EntityModel e2 = new EntityModel(loadFile("TC-5815-HCP-Step3.json"));
        e2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e2.post();
        SmartWaiting.waitForConsistency(e2);

        // Checks
        String attrSimpleValueE2 = e2.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, attrSimpleValueE2);
        assertNotEquals(attrSimpleValueE2, attrSimpleValueE1, "Value of 'FirstName' isn't changed");
    }

    @TmsLink("RP-TC-5833")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Bulkupdate with autogenerated values")
    public void runRpTc5833() throws Exception {
        nameAttrSimple = "FirstName";
        patternAttrSimple = "Simple_[0-9a-f-]{36}";
        RandomGenerator rgInstance = RandomGenerator.getInstance();

        // Precondition
        BusinessConfigurationService.updateConfig(loadFile("TC-5810-biz-conf.json"));
        // #1
        setVar("RANDOM_VALUE", rgInstance.getUniqueRandomNumbers(8), true);
        EntityModel e1 = new EntityModel(loadFile("TC-5833-HCP.json"));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, rgInstance.getRandomAlphabeticString(16)));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, rgInstance.getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        String nameFromEntity = e1.getAttribute(nameAttrSimple).getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, nameFromEntity);
        // #2
        ArrayList<String> types = new ArrayList<>();
        types.add("Reltio");
        ArrayList<SingleBulkActionModel> actions = new ArrayList<>();
        actions.add(new SingleBulkActionModel(CommonBulkActionModel.ADD_ATRIBUTE_ACTION, types,
                ATTRIBUTE_HCP_PREFIX + nameAttrSimple, null,
                new SimpleAttributeModel("FirstName. Predicted"), null, null));
        String filter = "containsWordStartingWith(attributes,'Simple') and equals(type, 'configuration/entityTypes/HCP')";
        CommonBulkActionModel model = new CommonBulkActionModel(filter, null, null, actions);

        TaskModel task = TaskService.bulkOperation(new PostTaskRequestParameters().tenantId(Config.getTenantId()).sendEmail(true), JsonParser.parseString(model.toString())).get(0);
        task.waitWhileRunning(TIME_BETWEEN_TASK_STATUS_REQUEST, WAITING_TIME);
        SmartWaiting.waitForConsistency(e1);
        e1.refresh();
        // Checks
        for (AttributeModel firstName : e1.getAttributes(nameAttrSimple)) {
            shouldMatchToPattern(patternAttrSimple, firstName.getAsSimple().getValue());
        }

        assertEquals(e1.getAttributes(nameAttrSimple).size(), 8, "Count of values of '" + nameAttrSimple + "'");
    }

    @TmsLink("RP-TC-5828")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty and generatedValueUniqueForCrosswalk, values sent/not,crosswalk value exist/not")
    public void runRpTc5828() throws Exception {
        // Precondition
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5828_precondition.json"));
        nameAttrSimple = "FirstName";
        patternAttrSimple = "Simple_[0-9a-f-]{36}";

        //      Step 1
        // generateIfEmpty && generatedValueUniqueForCrosswalk=true,
        // value was sent = No, Value by crosswalk exists = No,
        // Reaction = Generate a value

        EntityModel e0 = new EntityModel(loadFile("entity_RP_TC_5828_st1.json"));
        CrosswalkModel hmsCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsCrosswalkModel);
        CrosswalkModel hmsNpiCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsNpiCrosswalkModel);
        e0.post();
        SmartWaiting.waitForConsistency(e0);

        // Checks
        List<AttributeModel> attrSimpleListE0 = e0.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE0.size(), 1, "Amount of values");
        attrSimpleListE0.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });

        //      Step 2
        // generateIfEmpty && generatedValueUniqueForCrosswalk=true,
        // value was sent = No,Value by crosswalk exists = Yes,
        //
        // Reaction = Reuse value

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_5828_st1.json"));
        e1.addCrosswalk(hmsCrosswalkModel);
        e1.addCrosswalk(hmsNpiCrosswalkModel);
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        List<AttributeModel> attrSimpleListE1 = e1.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE1.size(), 1, "Amount of values");
        assertEquals(attrSimpleListE1.stream()
                        .filter(attrSimpleListE0::contains)
                        .toList()
                        .size(), 1,
                "Values of '" + nameAttrSimple + "' after second POST should equals"
        );

        //      Step 3
        // generateIfEmpty  && generatedValueUniqueForCrosswalk=true,
        // value was sent = yes, Value by crosswalk exists = Yes,
        //
        // Reaction = Reuse value

        EntityModel e2 = new EntityModel(loadFile("entity_RP_TC_5828_st3.json"));
        e2.addCrosswalk(hmsCrosswalkModel);
        e2.addCrosswalk(hmsNpiCrosswalkModel);
        e2.post();
        SmartWaiting.waitForConsistency(e2);

        // Checks
        List<AttributeModel> attrSimpleListE2 = e2.getAttributes(nameAttrSimple);
        assertEquals(1, attrSimpleListE2.size(), "Amount of values");
        assertEquals(attrSimpleListE2.stream()
                        .filter(attrSimpleListE1::contains)
                        .toList().size(), 1,
                "Values of '" + nameAttrSimple + "' after second POST should equals"
        );

        //      Step 4
        // generateIfEmpty && generatedValueUniqueForCrosswalk=true,
        // value was sent = yes,Value by crosswalk exists = no,
        //
        // Reaction = Generate value

        EntityModel e3 = new EntityModel(loadFile("entity_RP_TC_5828_st4.json"));
        e3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e3.post();
        SmartWaiting.waitForConsistency(e3);

        // Checks
        List<AttributeModel> attrSimpleListE3 = e3.getAttributes(nameAttrSimple);
        assertEquals(1, attrSimpleListE3.size(), "Amount of values");
        assertEquals(attrSimpleListE3.stream()
                        .filter(attrSimpleListE2::contains)
                        .toList().size(), 0,
                "Values of '" + nameAttrSimple + "' after second POST shouldn't equals, because it is new entity"
        );
        attrSimpleListE3.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
    }

    @TmsLink("RP-TC-5818")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generatedValueUniqueForCrosswalk true/false, value sent/not sent, value by crosswalk exist/not exist")
    public void runRpTc5818() throws Exception {
        nameAttrSimple = "FirstName";
        patternAttrSimple = "Simple_[0-9a-f-]{36}";

        // Precondition
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5818_precondition.json"));
        //      Step 1
        //generatedValueUniqueForCrosswalk = true,
        //Value was sent = no, Value by crosswalk exists = no,
        //
        //Reaction - Don’t generate a value

        EntityModel e0 = new EntityModel(loadFile("entity_RP_TC_5818_st1.json"));
        CrosswalkModel hmsCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsCrosswalkModel);
        CrosswalkModel hmsNpiCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsNpiCrosswalkModel);
        e0.post();
        SmartWaiting.waitForConsistency(e0);

        // Checks
        AttributeModel attrSimple = e0.getAttribute(nameAttrSimple);
        assertNull(attrSimple, "Attribute '" + nameAttrSimple + "' shouldn't exist at the entity '" + e0.getId() + "'");

        //      Step 2
        //generatedValueUniqueForCrosswalk = true,
        //Value was sent = yes, Value by crosswalk exists = no,
        //
        //Reaction - Generate new value and use it instead of value that was sent

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_5818_st2.json"));
        e1.addCrosswalk(hmsCrosswalkModel);
        e1.addCrosswalk(hmsNpiCrosswalkModel);
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        List<AttributeModel> attrSimpleListE1 = e1.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE1.size(), 1, "Amount of values");
        attrSimpleListE1.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });

        //      Step 3
        //generatedValueUniqueForCrosswalk = true,
        //Value was sent = yes, Value by crosswalk exists = yes,
        //
        //Reaction - Reuse existing value

        EntityModel e2 = new
                EntityModel(loadFile("entity_RP_TC_5818_st2.json"));
        e2.addCrosswalk(hmsCrosswalkModel);
        e2.addCrosswalk(hmsNpiCrosswalkModel);
        e2.post();
        SmartWaiting.waitForConsistency(e2);

        // Checks
        List<AttributeModel> attrSimpleListE2 = e2.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE2.stream().filter(attrSimpleListE1::contains).toList().size(), 1,
                "Values of '" + nameAttrSimple + "' after second POST should equals");
    }

    @TmsLink("RP-TC-5812")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Use of two generator profiles(one for simple attribute and another for crosswalk), UUID generator")
    public void runRpTc5812() throws Exception {
        nameAttrSimple = "FirstName";
        patternAttrSimple = "Simple_" + PATTERN;
        patternCrosswalk = "Source_" + PATTERN;

        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        addRandomGenerator("GENERATOR_NAME_IN_TEST_2");

        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5812_precondition.json"));

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_5812_st1.json"));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        String valueAttrSimple = e1.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple);

        String valueCrosswalk = "";
        List<CrosswalkModel> tmpCrwlks = e1.getCrosswalkTree().getAllCrosswalks();
        for (CrosswalkModel tmpCrwlk : tmpCrwlks) {
            if (CROSSWALK_TYPE_HMS.equals(tmpCrwlk.getType())) {
                valueCrosswalk = tmpCrwlk.getValue();
                break;
            }
        }

        shouldMatchToPattern(patternCrosswalk, valueCrosswalk);

        assertNotEquals(getUUID(valueAttrSimple), getUUID(valueCrosswalk),
                "UUID at values of simple attribute and crosswalk shouldn't be equal.");
    }

    @TmsLink("RP-TC-5900")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Cumulative Update. Update entity with new value from new crosswalk")
    public void runRpTc5900() throws Exception {
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5900_precondition.json"));

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_5900_st1.json"));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);
        String valueAttrSimple1 = e1.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple1);

        CrosswalkModel cm1 = e1.getCrosswalkTree().getAllCrosswalks().get(0);
        CrosswalkModel cm2 = new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, "CASE011_5900");

        ArrayList<AttributeModel> attributes = new ArrayList<>();
        attributes.add(new SimpleAttributeModel("String Reltio"));
        CumulativeUpdateModel modelNonReltio = new CumulativeUpdateModel(CumulativeUpdateModel.UPDATE_ATTRIBUTE, e1.getAttribute(nameAttrSimple).getUri(), attributes, cm2);
        EntityService.postCumulativeUpdate(e1, modelNonReltio);
        e1.refresh();

        assertEquals(e1.getAttributes(nameAttrSimple, cm1).size(), 1);
        List<AttributeModel> valuesCr1 = e1.getAttributes(nameAttrSimple, cm1);
        assertEquals(valueAttrSimple1, valuesCr1.get(0).getValue().toString(), "Value should't be changed");
        assertFalse(valuesCr1.get(0).getOv(), "Value should be OV false");
        assertEquals(e1.getAttributes(nameAttrSimple, cm2).size(), 1);
        List<AttributeModel> valuesCr2 = e1.getAttributes(nameAttrSimple, cm2);
        shouldMatchToPattern(patternAttrSimple, valuesCr2.get(0).getValue().toString());
        assertTrue(valuesCr2.get(0).getOv(), "\"Value should be OV true");
    }

    @TmsLink("RP-TC-7228")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Add value at attribute with generator")
    public void runRpTc7228() throws Exception {
        // Precondition
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_6183_precondition.json"));

        // #1
        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6183_st1.json"));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        String valueAttrSimple = e1.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple);

        String nameAttrSimple2 = "Nickname";
        String valueAttrSimple2 = e1.getAttribute(nameAttrSimple2).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple2);

        // #2
        String url = Config.getTenantUrl() + "/" + e1.getAttribute(nameAttrSimple2).getUri();
        String body = "{\"value\": \"zzzzz\"}";
        new Request(Request.Type.PUT, url, null, body).executeJson();

        SmartWaiting.waitForCrosswalksNumber(e1, 3);
        e1.refresh();

        // Checks
        List<AttributeModel> attributes1 = e1.getAttributes(nameAttrSimple);
        List<AttributeModel> attributes2OVfalse = e1.getAttributes(nameAttrSimple2, false);
        List<AttributeModel> attributes2OVtrue = e1.getAttributes(nameAttrSimple2, true);

        assertEquals(attributes1.size(), 1, "Incorrect number of '" + nameAttrSimple + "' values");
        assertEquals(attributes2OVfalse.size(), 1, "Incorrect number of '" + nameAttrSimple2 + "' values");
        assertEquals(attributes2OVtrue.size(), 1, "Incorrect number of '" + nameAttrSimple2 + "' values. OV(true)");

        String valueAttrSimple1new = attributes1.get(0).getValue().toString();
        assertEquals(valueAttrSimple, valueAttrSimple1new,
                "Value '" + valueAttrSimple + "' of '" + nameAttrSimple + "' shouldn't be changed after PUT");
        assertEquals(valueAttrSimple2, attributes2OVfalse.get(0).getValue().toString(),
                "Value '" + valueAttrSimple2 + "' of '" + nameAttrSimple2 + "' shouldn't be changed after PUT");

        shouldMatchToPattern(patternAttrSimple, attributes2OVtrue.get(0).getValue().toString());
    }

    @TmsLink("RP-TC-6641")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Cleanse of autogenerated values, Sequential")
    public void runRpTc6641() throws Exception {
        TenantManagementService.cleanTenant(Config.getTenantId());
        //cleansing should be enabled for this test case
        Set<String> expectedCrosswalkTypes = new HashSet<>();
        expectedCrosswalkTypes.add("configuration/sources/AutoSeq");
        expectedCrosswalkTypes.add("configuration/sources/ReltioCleanser");

        // Precondition
        GeneratorModel generatorModel = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setRangeStart(10)
                .setStep(1);

        registerGenerator(generatorModel);

        String sequentialGeneratorName = generatorModel.getName();

        BusinessConfigurationService.updateConfig(Config.getTenantId(),
                setGeneratorForSourceByUri(loadFile("config_RP_TC_6641_precondition.json"),
                        "configuration/sources/AutoSeq", sequentialGeneratorName));

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6641.json")).post();
        SmartWaiting.waitForConsistency(e1);
        SmartWaiting.waitForValue(() -> {
            e1.refresh();
            Set<String> crosswalksTypesFromEntity = e1.getCrosswalks().stream()
                    .map(CrosswalkModel::getType)
                    .collect(Collectors.toSet());
            return Sets.difference(expectedCrosswalkTypes, crosswalksTypesFromEntity).size();
        }, 0, "Entity should have all expected crosswalks");
    }

    @TmsLink("RP-TC-6634")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "autoGenerated true for simple with pattern, Sequential generator")
    public void runRpTc6634() throws Exception {
        Integer rangeStartForGenerator = 10;
        String attributeTemplatePrefix = "Simple_";

        // Precondition
        GeneratorModel generatorModel = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setRangeStart(rangeStartForGenerator)
                .setStep(1);
        registerGenerator(generatorModel);

        String sequentialGeneratorName = generatorModel.getName();

        BusinessConfigurationService.updateConfig(Config.getTenantId(),
                setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6641_precondition.json"),
                        ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName));

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6634.json"));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        long numberOfFirstNameWithTemplate = e1.getAttributes("FirstName").stream()
                .map(AttributeModel::getValue)
                .filter(value -> value.toString().startsWith(attributeTemplatePrefix))
                .count();
        long totalNumberIfFirstNameAttributes = e1.getAttributes("FirstName").size();
        //check of attribute templates
        assertEquals(numberOfFirstNameWithTemplate, totalNumberIfFirstNameAttributes,
                "Not all FirstName attributes are generated with expected template");
        //check of generated values
        assertTrue(e1.getAttributes("FirstName").stream()
                        .map(item -> item.getValue().toString())
                        .map(item -> item.substring(attributeTemplatePrefix.length(), item.length()))
                        .map(Integer::parseInt)
                        .allMatch(item -> item >= rangeStartForGenerator),
                "generated values are not in expected range");

        //check if two posted entities doesn't have same generated value
        EntityModel e11 = new EntityModel(loadFile("entity_RP_TC_6634s.json")).post();
        EntityModel e12 = new EntityModel(loadFile("entity_RP_TC_6634s.json")).post();
        SmartWaiting.waitForConsistency(e11, e12);
        assertNotEquals(e11.getAttribute("FirstName").getValue(), e12.getAttribute("FirstName").getValue(),
                "Two entities have same generated values");
    }

    @TmsLink("RP-TC-6632")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "autoGenerated true for crosswalk, Sequential generator")
    public void runRpTc6632() throws Exception {
        Integer rangeStartForGenerator = 10;
        String crosswalkTemplatePrefix = "Source_";

        // Precondition
        GeneratorModel generatorModel = new GeneratorModel();
        generatorModel.setType("SEQUENTIAL");
        generatorModel.setRangeStart(rangeStartForGenerator);
        generatorModel.setStep(1);
        registerGenerator(generatorModel);

        BusinessConfigurationService.updateConfig(Config.getTenantId(),
                setGeneratorForSourceByUri(loadFile("config_RP_TC_6641_precondition.json"),
                        "configuration/sources/AutoSeq", generatorModel.getName()));

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6632.json")).post();
        SmartWaiting.waitForConsistency(e1);

        assertTrue(e1.getCrosswalks().stream()
                        .map(CrosswalkModel::getValue)
                        .allMatch(value -> value.startsWith(crosswalkTemplatePrefix)),
                "Crosswalk value does not have expected prefix");
        assertTrue(e1.getCrosswalks().stream()
                        .map(CrosswalkModel::getValue)
                        .map(item -> item.substring(crosswalkTemplatePrefix.length(), item.length()))
                        .map(Integer::parseInt)
                        .allMatch(item -> item >= rangeStartForGenerator),
                "Generated values are not in expected range");

        //check if two posted entities doesn't have same generated value
        EntityModel e11 = new EntityModel(loadFile("entity_RP_TC_6632.json")).post();
        SmartWaiting.waitForConsistency(e11);
        Set e11CrosswalkValues = e11.getCrosswalks().stream()
                .map(CrosswalkModel::getValue).collect(Collectors.toSet());
        EntityModel e12 = new EntityModel(loadFile("entity_RP_TC_6632.json")).post();
        SmartWaiting.waitForConsistency(e12);
        Set e12CrosswalkValues = e12.getCrosswalks().stream().map(CrosswalkModel::getValue).collect(Collectors.toSet());
        assertEquals(Sets.intersection(e11CrosswalkValues, e12CrosswalkValues).size(), 0);
        GeneratorService.removeGenerator(generatorModel);
    }

    @TmsLink("RP-TC-6659")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "usage of two independent generators of different type")
    public void runRpTc6659() throws Exception {
        Integer rangeStartForGenerator = 10;
        String attributeTemplatePrefix = "Simple_";
        String attributeWithUuidPattern = attributeTemplatePrefix + "[0-9a-f-]{36}";

        // Precondition
        GeneratorModel sequentialGeneratorModel = new GeneratorModel();
        sequentialGeneratorModel.setType("SEQUENTIAL");
        sequentialGeneratorModel.setRangeStart(rangeStartForGenerator);
        sequentialGeneratorModel.setStep(1);
        registerGenerator(sequentialGeneratorModel);

        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6659_precondition.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel.getName());
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "LastName", uuidGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6659.json")).post();
        SmartWaiting.waitForConsistency(e1);
        //check if expected fields have generated values
        assertTrue(e1.getAttributes("FirstName").stream()
                        .map(item -> item.getValue().toString())
                        .map(item -> Integer.parseInt(item.substring(attributeTemplatePrefix.length(), item.length())))
                        .allMatch(item -> (item >= rangeStartForGenerator)),
                "Generated values are not in expected range");
        assertTrue(e1.getAttributes("LastName").stream()
                        .map(item -> item.getValue().toString())
                        .allMatch(item -> isMatchToPattern(attributeWithUuidPattern, item)),
                "Generated values are not in expected format");
    }

    @TmsLink("RP-TC-6658")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "using of two independent sequential generators")
    public void runRpTc6658() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        Integer rangeStartForGenerator2 = 100;
        String attributeTemplatePrefix = "Simple_";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        GeneratorModel sequentialGeneratorModel2 = new GeneratorModel();
        sequentialGeneratorModel2.setType("SEQUENTIAL");
        sequentialGeneratorModel2.setRangeStart(rangeStartForGenerator2);
        sequentialGeneratorModel2.setStep(10);
        registerGenerator(sequentialGeneratorModel2);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6659_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "LastName", sequentialGeneratorModel2.getName());

        BusinessConfigurationService.updateConfig(l3config);

        Integer valueOfGenShouldBeNotLowerThan1 = rangeStartForGenerator1;
        Integer valueOfGenShouldBeNotLowerThan2 = rangeStartForGenerator2;

        for (int i = 0; i < 8; i++) {
            EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6659.json")).post();
            SmartWaiting.waitForConsistency(e1);
            //check if expected fields have generated values
            List<Integer> firstNameValuesList = e1.getAttributes("FirstName").stream()
                    .map(item -> item.getValue().toString())
                    .map(item -> Integer.parseInt(item.substring(attributeTemplatePrefix.length(), item.length())))
                    .toList();
            for (Integer firstNameValue : firstNameValuesList) {
                assertTrue(firstNameValue >= valueOfGenShouldBeNotLowerThan1,
                        "Generated values are not in expected range");
            }
            valueOfGenShouldBeNotLowerThan1 = Collections.max(firstNameValuesList);
            List<Integer> lastNameValuesList = e1.getAttributes("LastName").stream()
                    .map(item -> item.getValue().toString())
                    .map(item -> Integer.parseInt(item.substring(attributeTemplatePrefix.length(), item.length())))
                    .toList();
            for (Integer lastNameValue : lastNameValuesList) {
                assertTrue(lastNameValue >= valueOfGenShouldBeNotLowerThan2,
                        "Generated values are not in expected range");
            }
            valueOfGenShouldBeNotLowerThan2 = Collections.max(lastNameValuesList);
        }
    }

    @TmsLink("RP-TC-6786")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "check of \"step\" parameter of sequential generator")
    public void runRpTc6786() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        String attributeTemplatePrefix = "Simple_";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6786_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());

        BusinessConfigurationService.updateConfig(l3config);

        Integer valueOfGenShouldBeNotLowerThan1 = rangeStartForGenerator1;
        List<Integer> listOfGeneratedValues = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6786.json")).post();
            SmartWaiting.waitForConsistency(e1);
            //check if expected fields have generated values
            List<Integer> firstNameValuesList = e1.getAttributes("FirstName").stream()
                    .map(item -> item.getValue().toString())
                    .map(item -> Integer.parseInt(item.substring(attributeTemplatePrefix.length())))
                    .toList();
            listOfGeneratedValues.addAll(firstNameValuesList);
            Integer lowerBorder = valueOfGenShouldBeNotLowerThan1;
            assertTrue(firstNameValuesList.stream().allMatch(item -> item >= lowerBorder),
                    "Generated values are not in expected range");
            valueOfGenShouldBeNotLowerThan1 = Collections.max(firstNameValuesList);
        }
        for (int i = 0; i < listOfGeneratedValues.size() - 1; i++) {
            assertEquals(listOfGeneratedValues.get(i).intValue() + 1, listOfGeneratedValues.get(i + 1).intValue(),
                    "Generated values have unexpected step");
        }
    }

    @TmsLink("RP-TC-6657")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "usage of multiple generators for entity posting")
    public void runRpTc6657() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        Integer rangeStartForGenerator2 = 100;
        Integer rangeStartForGenerator3 = randomGenerator.getRandomNumberInRange(0, 10000);
        String attributeTemplatePrefix = "Simple_";
        String attributeTemplatePrefixNested = "Nested_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        GeneratorModel sequentialGeneratorModel2 = new GeneratorModel();
        sequentialGeneratorModel2.setType("SEQUENTIAL");
        sequentialGeneratorModel2.setRangeStart(rangeStartForGenerator2);
        sequentialGeneratorModel2.setStep(10);
        registerGenerator(sequentialGeneratorModel2);

        GeneratorModel sequentialGeneratorModel3 = new GeneratorModel();
        sequentialGeneratorModel3.setType("SEQUENTIAL");
        sequentialGeneratorModel3.setRangeStart(rangeStartForGenerator3);
        sequentialGeneratorModel3.setStep(10);
        registerGenerator(sequentialGeneratorModel3);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6657_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "License", sequentialGeneratorModel2.getName());
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq", sequentialGeneratorModel3.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6657.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        assertTrue(entityModel.getAttributes("FirstName").get(0).getAsSimple().getValue().startsWith(attributeTemplatePrefix),
                "Entity model's FirstName value does not have expected format");
        shouldHaveExpectedNumberOfAttributes(entityModel, "License", 1);

        assertTrue(entityModel.getAttributes("License").get(0).getAsNested()
                        .getAttribute("Number").getValue().toString().startsWith(attributeTemplatePrefixNested),
                "Entity model's License.Number value does not have expected format");
        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        assertEquals(entityModel.getCrosswalks().get(0).getType(), crosswalkType, "Crosswalk has unexpected type");
        assertTrue(entityModel.getCrosswalks().get(0).getValue().startsWith(crosswalkTemplatePrefix),
                "Entity crosswalk value does not have expected format");
    }

    @TmsLink("RP-TC-6656")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "rangeStart and step check for sequential generators (uniqueGeneratedValuePerEntityPerRequest = true)")
    public void runRpTc6656() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        String attributeTemplatePrefix = "Simple_";
        String attributeTemplatePrefixNested = "Nested_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        removeUsedGenerators();
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6656_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "License", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorModel1.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6657.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        assertTrue(entityModel.getAttributes("FirstName").get(0).getAsSimple().getValue().startsWith(attributeTemplatePrefix),
                "Entity model's FirstName value does not have expected format");

        Integer generatedValueFromFirstName = Integer.parseInt(entityModel.getAttributes("FirstName").get(0).getAsSimple()
                .getValue().substring(attributeTemplatePrefix.length()));
        assertTrue(generatedValueFromFirstName >= rangeStartForGenerator1, "Entity model's FirstName generated value is not in expected range");
        shouldHaveExpectedNumberOfAttributes(entityModel, "License", 1); //RP-30477
        assertTrue(entityModel.getAttributes("License").get(0).getAsNested()
                        .getAttribute("Number").getValue().toString().startsWith(attributeTemplatePrefixNested),
                "Entity model's License.Number value does not have expected format");
        Integer generatedValueFromLicenseNumber = getGeneratedValueIntFromString(entityModel.getAttributes("License").get(0).getAsNested()
                .getAttribute("Number").getValue().toString(), attributeTemplatePrefix);
        assertEquals(generatedValueFromLicenseNumber, generatedValueFromFirstName,
                "Entity model's License.Number not equal to generatedValueFromFirstName");
        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        assertEquals(entityModel.getCrosswalks().get(0).getType(), crosswalkType,
                "Crosswalk has unexpected type");
        assertTrue(entityModel.getCrosswalks().get(0).getValue().startsWith(crosswalkTemplatePrefix),
                "Entity crosswalk value does not have expected format");
        assertEquals(getGeneratedValueIntFromString(entityModel.getCrosswalks().get(0).getValue(), attributeTemplatePrefix),
                generatedValueFromFirstName,
                "Entity model's crosswalk value not equal to generatedValueFromFirstName"); //RP-30487
    }

    @TmsLink("RP-TC-6655")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "rangeStart and step check for sequential generators (uniqueGeneratedValuePerEntityPerRequest = false)")
    public void runRpTc6655() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        String attributeTemplatePrefix = "Simple_";
        String attributeTemplatePrefixNested = "Nested_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6655_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "License", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6657.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        assertTrue(entityModel.getAttributes("FirstName").get(0).getAsSimple().getValue().startsWith(attributeTemplatePrefix),
                "Entity model's FirstName value does not have expected format");

        Integer generatedValueFromFirstName = Integer.parseInt(entityModel.getAttributes("FirstName").get(0).getAsSimple()
                .getValue().substring(attributeTemplatePrefix.length()));
        listOfGeneratedValues.add(generatedValueFromFirstName);
        shouldHaveExpectedNumberOfAttributes(entityModel, "License", 1); //RP-30477
        assertTrue(entityModel.getAttributes("License").get(0).getAsNested()
                        .getAttribute("Number").getValue().toString().startsWith(attributeTemplatePrefixNested),
                "Entity model's License.Number value does not have expected format");

        Integer generatedValueFromLicenseNumber = Integer.parseInt(entityModel.getAttributes("License").get(0).getAsNested()
                .getAttribute("Number").getValue().toString().substring(attributeTemplatePrefix.length()));
        listOfGeneratedValues.add(generatedValueFromLicenseNumber);
        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        assertEquals(entityModel.getCrosswalks().get(0).getType(), crosswalkType,
                "Crosswalk has unexpected type");
        assertTrue(entityModel.getCrosswalks().get(0).getValue().startsWith(crosswalkTemplatePrefix),
                "Entity crosswalk value does not have expected format");
        Integer generatedValueFromCrosswalk = Integer.parseInt(entityModel.getCrosswalks().get(0).getValue()
                .substring(attributeTemplatePrefix.length()));
        listOfGeneratedValues.add(generatedValueFromCrosswalk);
        //checks for equality and range of generated values
        assertEquals(listOfGeneratedValues.stream().distinct().collect(Collectors.toSet()).size(), listOfGeneratedValues.size(),
                "Generated values are not unique for one entity");
        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForGenerator1),
                "Generated values are not in expected range");
    }

    @TmsLink("RP-TC-6639")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generatedValueUniqueForCrosswalk true/false,value sent/not sent, value by crosswalk exist/not,Seq")
    public void runRpTc6639() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        nameAttrSimple = "FirstName";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6639_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());
        BusinessConfigurationService.updateConfig(l3config);

        //      Step 1
        //generatedValueUniqueForCrosswalk = true,
        //Value was sent = no, Value by crosswalk exists = no,
        //
        //Reaction - Don’t generate a value

        EntityModel e0 = new EntityModel(loadFile("entity_RP_TC_5818_st1.json"));
        CrosswalkModel hmsCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsCrosswalkModel);
        CrosswalkModel hmsNpiCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsNpiCrosswalkModel);
        e0.post();
        SmartWaiting.waitForConsistency(e0);

        // Checks
        assertNull(e0.getAttribute(nameAttrSimple), "Attribute '" + nameAttrSimple + "' shouldn't exist at the entity '" + e0.getId() + "'");

        //      Step 2
        //generatedValueUniqueForCrosswalk = true,
        //Value was sent = yes, Value by crosswalk exists = no,
        //
        //Reaction - Generate new value and use it instead of value that was sent

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_5818_st2.json"));
        e1.addCrosswalk(hmsCrosswalkModel);
        e1.addCrosswalk(hmsNpiCrosswalkModel);
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        List<AttributeModel> attrSimpleListE1 = e1.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE1.size(), 1, "Amount of values");
        attrSimpleListE1.forEach(attr -> {
            try {
                shouldMatchToPattern("Simple_[0-9]+", attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });

        //      Step 3
        //generatedValueUniqueForCrosswalk = true,
        //Value was sent = yes, Value by crosswalk exists = yes,
        //
        //Reaction - Reuse existing value

        EntityModel e2 = new EntityModel(loadFile("entity_RP_TC_5818_st2.json"));
        e2.addCrosswalk(hmsCrosswalkModel);
        e2.addCrosswalk(hmsNpiCrosswalkModel);
        e2.post();
        SmartWaiting.waitForConsistency(e2);

        // Checks
        List<AttributeModel> attrSimpleListE2 = e2.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE2.stream().filter(attrSimpleListE1::contains).toList().size(), 1,
                "Values of '" + nameAttrSimple + "' after second POST should equals");
    }

    @TmsLink("RP-TC-6637")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Crosswalk reusing if generated value, Sequential generator")
    public void runRpTc6637() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 100000);
        String crosswalkTemplatePrefix = "Source_";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        String l3config = setGeneratorForSourceByUri(loadFile("config_RP_TC_6637_precondition.json"),
                CROSSWALK_TYPE_HMS, sequentialGeneratorModel1.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6637.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");

        String generatedCrosswalkValue = entityModel.getCrosswalks().get(0).getValue();
        assertTrue(generatedCrosswalkValue.startsWith(crosswalkTemplatePrefix),
                "Entity crosswalk value does not have expected format");
        assertTrue(getGeneratedValueIntFromString(generatedCrosswalkValue, crosswalkTemplatePrefix) >= rangeStartForGenerator1,
                "Entity crosswalk generated value is not in expected range");

        entityModel.getCrosswalks().get(0).setValue("qwerty");
        EntityModel updatedEntityModel = entityModel.post();
        assertEquals(updatedEntityModel.getCrosswalks().size(), 1, "Entity " + updatedEntityModel.getUri() + " has unexpected number of crosswalks");

        String generatedCrosswalkValueForUpdatedEntity = updatedEntityModel.getCrosswalks().get(0).getValue();
        assertTrue(generatedCrosswalkValueForUpdatedEntity.startsWith(crosswalkTemplatePrefix),
                "Entity crosswalk value does not have expected format");
        assertTrue(getGeneratedValueIntFromString(generatedCrosswalkValueForUpdatedEntity, crosswalkTemplatePrefix)
                        > getGeneratedValueIntFromString(generatedCrosswalkValue, crosswalkTemplatePrefix),
                "Entity crosswalk generated value is not in expected range");

        updatedEntityModel.getCrosswalks().get(0).setValue(generatedCrosswalkValueForUpdatedEntity);
        EntityModel updatedEntityModelWithExistingCrosswalk = updatedEntityModel.post().get();
        assertEquals(updatedEntityModelWithExistingCrosswalk.getUri(), updatedEntityModel.getUri(),
                "Entity crosswalk value is not reused");
    }

    @TmsLink("RP-TC-6636")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Use of two generators (one for simple attribute and another for crosswalk), Sequential generator")
    public void runRpTc6636() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        Integer rangeStartForGenerator2 = 100;
        String attributeTemplatePrefix = "Simple_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        GeneratorModel sequentialGeneratorModel2 = new GeneratorModel();
        sequentialGeneratorModel2.setType("SEQUENTIAL");
        sequentialGeneratorModel2.setRangeStart(rangeStartForGenerator2);
        sequentialGeneratorModel2.setStep(1);
        registerGenerator(sequentialGeneratorModel2);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6655_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorModel2.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6636.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);

        Integer valueFromFirstName = getGeneratedValueFromSimpleAttributeValue(entityModel,
                "FirstName", 0, attributeTemplatePrefix);
        Integer valueFromCrosswalk = getGeneratedValueFromCrosswalkValue(entityModel, crosswalkType, crosswalkTemplatePrefix);
        assertTrue(valueFromFirstName >= rangeStartForGenerator1,
                "FirstName generated value is not in expected range");
        assertTrue(valueFromCrosswalk >= rangeStartForGenerator2,
                "Entity crosswalk generated value is not in expected range");
    }

    @TmsLink("RP-TC-6638")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty true, value was sent/not sent")
    public void runRpTc6638() throws Exception {
        Integer rangeStartForGenerator1 = 10;
        String attributeTemplatePrefix = "Simple_";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6638_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6638_1.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);

        Integer valueFromFirstName = getGeneratedValueFromSimpleAttributeValue(entityModel,
                "FirstName", 0, attributeTemplatePrefix);

        assertTrue(valueFromFirstName >= rangeStartForGenerator1,
                "FirstName generated value is not in expected range");

        entityModel = new EntityModel(loadFile("entity_RP_TC_6638_2.json")).post().get();

        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);

        Integer valueFromFirstName2 = getGeneratedValueFromSimpleAttributeValue(entityModel,
                "FirstName", 0, attributeTemplatePrefix);
        assertTrue(valueFromFirstName2 >= rangeStartForGenerator1,
                "FirstName generated value is not in expected range");
        assertNotEquals(valueFromFirstName, valueFromFirstName2, "Generated values are the same for two entities");
    }

    @TmsLink("RP-TC-6652")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "multitenant usage of sequential generator")
    public void runRpTc6652() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";
        String attributeNestedTemplatePrefix = "Nested_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setRangeStart(rangeStartForGenerator1)
                .setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6652_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        String secondTenantId = createTmpTenant();
        AccountService.updateAccount(AccountService.getDefaultAccount().addTenants(Config.getTenantId(), secondTenantId));
        try {
            String secondTenantUrl = Config.getApiUrl() + secondTenantId;

            BusinessConfigurationService.updateConfig(l3config);
            BusinessConfigurationService.updateConfig(secondTenantId, l3config);

            EntityModel entityModelAtTenant1 = new EntityModel(loadFile("entity_RP_TC_6652.json"), Config.getTenantUrl())
                    .post();
            Integer valueFirstNameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModelAtTenant1,
                    "FirstName", 0, attributeTemplatePrefix);
            Integer valueLicenseNumberNameEntity1 = getGeneratedValueFromNestedAttributeValue(entityModelAtTenant1,
                    "License", "Number", attributeNestedTemplatePrefix);
            Integer valueCrosswalkEntity1 = getGeneratedValueFromCrosswalkValue(entityModelAtTenant1, crosswalkType, crosswalkTemplatePrefix);
            listOfGeneratedValues.addAll(Arrays.asList(valueFirstNameEntity1, valueLicenseNumberNameEntity1, valueCrosswalkEntity1));

            EntityModel entityModelAtTenant2 = new EntityModel(loadFile("entity_RP_TC_6652.json"),
                    secondTenantUrl).post().get();
            Integer valueFirstNameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModelAtTenant2,
                    "FirstName", 0, attributeTemplatePrefix);
            Integer valueLicenseNumberNameEntity2 = getGeneratedValueFromNestedAttributeValue(entityModelAtTenant2,
                    "License", "Number", attributeNestedTemplatePrefix);
            Integer valueCrosswalkEntity2 = getGeneratedValueFromCrosswalkValue(entityModelAtTenant2, crosswalkType, crosswalkTemplatePrefix);
            listOfGeneratedValues.addAll(Arrays.asList(valueFirstNameEntity2, valueLicenseNumberNameEntity2, valueCrosswalkEntity2));

            assertEquals(entityModelAtTenant1.getCrosswalks().size(), 1, "Entity " + entityModelAtTenant1.getUri() + " has unexpected number of crosswalks");
            shouldHaveExpectedNumberOfAttributes(entityModelAtTenant1, "FirstName", 1);
            shouldHaveExpectedNumberOfAttributes(entityModelAtTenant1, "License", 1); //RP-30477

            assertEquals(entityModelAtTenant2.getCrosswalks().size(), 1, "Entity " + entityModelAtTenant2.getUri() + " has unexpected number of crosswalks");
            shouldHaveExpectedNumberOfAttributes(entityModelAtTenant2, "FirstName", 1);
            shouldHaveExpectedNumberOfAttributes(entityModelAtTenant2, "License", 1); //RP-30477
            assertEquals(listOfGeneratedValues.stream().distinct().collect(Collectors.toSet()).size(), listOfGeneratedValues.size(),
                    "Generated values are not unique for one entity");

            assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForGenerator1), "generated values are not in expected range");
        } finally {
            TenantManagementService.deleteTenantUnchecked(secondTenantId);
        }
    }

    @TmsLink("RP-TC-6628")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "update entity with new value from new crosswalk, Sequential generator")
    public void runRpTc6628() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6628_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6628.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        Integer valueFirstNameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueCrosswalkEntity1 = getGeneratedValueFromCrosswalkValue(entityModel, crosswalkType, crosswalkTemplatePrefix);

        assertEquals(entityModel.getCrosswalks().size(), 1, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);

        //cumulative update
        CrosswalkModel cm = new CrosswalkModel(crosswalkType, randomGenerator.getRandomNumberInRange(10000, 20000).toString());

        ArrayList<AttributeModel> attributes = new ArrayList<>();
        attributes.add(new SimpleAttributeModel("String Reltio"));

        CumulativeUpdateModel modelAutoSeq = new CumulativeUpdateModel(CumulativeUpdateModel.UPDATE_ATTRIBUTE,
                entityModel.getAttribute("FirstName").getUri(), attributes, cm);

        EntityService.postCumulativeUpdate(entityModel, modelAutoSeq);
        entityModel.get();

        assertEquals(entityModel.getCrosswalks().size(), 2, "Entity " + entityModel.getUri() + " has unexpected number of crosswalks");
        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 2);

        //collecting generated values from FirstName attrbiteValues
        for (AttributeModel firstNameAttributeModel : entityModel.getAttributes("FirstName")) {
            listOfGeneratedValues.add(getGeneratedValueIntFromString(
                    firstNameAttributeModel.getAsSimple().getValue(), attributeTemplatePrefix));
        }

        assertTrue(listOfGeneratedValues.contains(valueFirstNameEntity1),
                "Entity does not have all expected attributes after cumulative update");

        //collecting generated values from crosswalks
        for (CrosswalkModel crosswalkModel : entityModel.getCrosswalks()) {
            listOfGeneratedValues.add(getGeneratedValueIntFromString(crosswalkModel.getValue(), crosswalkTemplatePrefix));
        }

        assertFalse(entityModel.getCrosswalks().stream()
                        .map(CrosswalkModel::getValue)
                        .noneMatch(item -> item.endsWith(valueCrosswalkEntity1.toString())),
                "Entity does not have expected crosswalks after cumulative update");
        assertEquals(listOfGeneratedValues.stream().distinct().collect(Collectors.toSet()).size(), listOfGeneratedValues.size(),
                "Generated values are not unique for one entity");
        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForGenerator1),
                "Generated values are not in expected range");
    }

    @TmsLink("RP-TC-6627")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "update attribute for which no generators rules exist, Sequential generator")
    public void runRpTc6627() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";
        String nicknameValue = "RP-TC-6627-nickname";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6627_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6627.json")).post();
        SmartWaiting.waitForConsistency(entityModel);
        Integer valueFirstNameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName",
                0, attributeTemplatePrefix);

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        assertTrue(valueFirstNameEntity1 >= rangeStartForGenerator1,
                "FirstName generated value is not in expected range");

        updateattributeOfEntity(entityModel.getAttributes("Nickname").get(0).getUri(), nicknameValue);
        entityModel.get();
        Integer valueFirstNameEntityUpdated = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName",
                0, attributeTemplatePrefix);

        shouldHaveExpectedNumberOfAttributes(entityModel, "Nickname", 1);
        assertEquals(entityModel.getAttributes("Nickname").get(0).getAsSimple().getValue(), nicknameValue,
                "Updated value has unexpected value, Nickname");
        assertEquals(valueFirstNameEntityUpdated, valueFirstNameEntity1,
                "Updated value has unexpected value, FirstName");
    }

    @TmsLink("RP-TC-6630")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Add value at attribute with generator, Sequential generator")
    public void runRpTc6630() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";
        String nicknameValue = "RP-TC-6627-nickname-updated";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6630_precondition.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "Nickname", sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6630.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        Integer valueFirstNameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel, "Nickname", 1);
        assertTrue(valueFirstNameEntity1 >= rangeStartForGenerator1,
                "FirstName generated value is not in expected range");
        assertTrue(valueNicknameEntity1 >= rangeStartForGenerator1,
                "NickName generated value is not in expected range");

        updateattributeOfEntity(entityModel.getAttributes("Nickname").get(0).getUri(), nicknameValue);
        entityModel.get();
        Integer valueFirstNameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel, "Nickname", 1);

        assertEquals(valueFirstNameEntity1, valueFirstNameEntity2, "FirstName value changed");

        listOfGeneratedValues.addAll(Arrays.asList(valueFirstNameEntity1, valueNicknameEntity1, valueFirstNameEntity2, valueNicknameEntity2));
        assertEquals(listOfGeneratedValues.stream().distinct().toList().size(), 3,
                "Not all generated values are unique");
        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForGenerator1),
                "Generated values are not in expected range");
    }

    @TmsLink("RP-TC-6626")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "update entity with generatedValueUniqueForCrosswalk=true, Sequential")
    public void runRpTc6626() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6626_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "Nickname", sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6626.json"));
        CrosswalkModel crosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS,
                randomGenerator.getRandomNumberInRange(5000000, 6000000).toString());
        entityModel.addCrosswalk(crosswalkModel);
        entityModel.post();

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel, "Nickname", 1);
        Integer valueFirstNameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);

        entityModel = new EntityModel(entityModel.getPostBody()).post().get();
        Integer valueFirstNameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);
        assertNotEquals(valueFirstNameEntity1, valueFirstNameEntity2, "Firstname value should be changed");
        assertEquals(valueNicknameEntity1, valueNicknameEntity2, "Nickname should be the same");
    }

    @TmsLink("RP-TC-6624")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Bulkupdate with autogenerated values for Sequential generator")
    public void runRpTc6624() throws Exception { //may be unstable due to bulkupdate
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";
        String firstNameAttributeUri = ATTRIBUTE_HCP_PREFIX + "FirstName";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_6624_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorModel1.getName());

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel1 = new EntityModel(loadFile("entity_RP_TC_6624.json")).post();
        SmartWaiting.waitForConsistency(entityModel1);
        Long entityModel1UpdatedTime = entityModel1.getUpdatedTime();
        EntityModel entityModel2 = new EntityModel(loadFile("entity_RP_TC_6624_2.json")).post();
        SmartWaiting.waitForConsistency(entityModel2);
        Long entityModel2UpdatedTime = entityModel2.getUpdatedTime();

        SmartWaiting.waitForConsistency(entityModel1, entityModel2);

        shouldHaveExpectedNumberOfAttributes(entityModel1, "FirstName", 1);
        Integer valueFirstNameEntityBeforeBulk1 = getGeneratedValueFromSimpleAttributeValue(entityModel1,
                "FirstName", 0, attributeTemplatePrefix);
        listOfGeneratedValues.add(valueFirstNameEntityBeforeBulk1);
        Integer valueFirstNameEntityBeforeBulk2 = getGeneratedValueFromSimpleAttributeValue(entityModel2,
                "FirstName", 0, attributeTemplatePrefix);
        listOfGeneratedValues.add(valueFirstNameEntityBeforeBulk2);
        log.info(valueFirstNameEntityBeforeBulk1 + "\t" + valueFirstNameEntityBeforeBulk2); //test is unstable sometimes, so we have to keep some logging

        SingleBulkActionModel singleBulkActionModel = new SingleBulkActionModel(
                CommonBulkActionModel.UPDATE_ATRIBUTE_ACTION, Arrays.asList("Reltio"),
                firstNameAttributeUri, null,
                new SimpleAttributeModel("BulkGenerationTest"), null, null);
        CommonBulkActionModel model = new CommonBulkActionModel("equals(type, 'configuration/entityTypes/HCP')",
                new ArrayList<>(), null, Arrays.asList(singleBulkActionModel));
        TaskModel task = TaskService.bulkOperation(new PostTaskRequestParameters().tenantId(Config.getTenantId()).sendEmail(false), JsonParser.parseString(model.toString())).get(0);
        task = task.waitWhileRunning(5, 300);
        assertTrue(task.getStatus().equalsIgnoreCase("COMPLETED"), "BulkOperation task has status other than COMPLETED");

        PostTaskRequestParameters params = new PostTaskRequestParameters()
                .tenantId(Config.getTenantId())
                .fixInconsistency(true)
                .fixVersionConflicts(true);
        TaskService.esCassandraConsistencyCheck(ExecutionType.TENANT, params).waitWhileRunning(5, 300);

        SmartWaiting.waitForEntityIndexed(entityModel1, entityModel1UpdatedTime, Config.getTenantId());
        SmartWaiting.waitForEntityIndexed(entityModel2, entityModel2UpdatedTime, Config.getTenantId());

        Integer valueFirstNameEntityAfterBulk1 = getGeneratedValueFromSimpleAttributeValue(entityModel1.get(),
                "FirstName", 0, attributeTemplatePrefix);
        listOfGeneratedValues.add(valueFirstNameEntityAfterBulk1);
        Integer valueFirstNameEntityAfterBulk2 = getGeneratedValueFromSimpleAttributeValue(entityModel2.get(),
                "FirstName", 0, attributeTemplatePrefix);
        listOfGeneratedValues.add(valueFirstNameEntityAfterBulk2);
        log.info(entityModel1.getUri() + "\t" + entityModel2.getUri()); //test is unstable sometimes, so we have to keep some logging
        log.info(valueFirstNameEntityAfterBulk1 + "\t" + valueFirstNameEntityAfterBulk2); //test is unstable sometimes, so we have to keep some logging

        shouldBeNonDecreasing(Arrays.asList(
                        valueFirstNameEntityBeforeBulk1, valueFirstNameEntityBeforeBulk2, valueFirstNameEntityAfterBulk1),
                "List of values should be non decreasing");
        shouldBeNonDecreasing(Arrays.asList(
                        valueFirstNameEntityBeforeBulk1, valueFirstNameEntityBeforeBulk2, valueFirstNameEntityAfterBulk2),
                "List of values should be non decreasing");
        assertEquals(listOfGeneratedValues.stream().distinct().collect(Collectors.toSet()).size(), listOfGeneratedValues.size(),
                "Generated values are not unique for one entity");
    }

    @TmsLink("RP-TC-7090")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "update entity's attribute with generatedValueUniqueForCrosswalk=true, Sequential")
    public void runRpTc7090() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";
        String crosswalkType = CROSSWALK_TYPE_HMS;

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7090_precondition.json"),
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "Nickname", sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_7090.json"));
        CrosswalkModel crosswalkModel = new CrosswalkModel(crosswalkType,
                randomGenerator.getRandomNumberInRange(5000000, 8000000).toString());
        entityModel.addCrosswalk(crosswalkModel);
        entityModel.post().get();

        shouldHaveExpectedNumberOfAttributes(entityModel, "FirstName", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel, "Nickname", 1);
        Integer valueFirstNameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity1 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);

        String crosswalkValue = entityModel.getCrosswalkByType(crosswalkType).getValue();
        updateAttributeOfEntityByCrosswalk(entityModel.getAttributes("FirstName").get(0).getUri(),
                crosswalkType, crosswalkValue, "zzz");
        entityModel.get();

        Integer valueFirstNameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity2 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);

        assertNotEquals(valueFirstNameEntity1, valueFirstNameEntity2, "Firstname value should be changed");
        assertEquals(valueNicknameEntity1, valueNicknameEntity2, "Nickname should be the same");

        updateAttributeOfEntityByCrosswalk(entityModel.getAttributes("Nickname").get(0).getUri(),
                crosswalkType, crosswalkValue, "zzz");
        entityModel.get();

        Integer valueFirstNameEntity3 = getGeneratedValueFromSimpleAttributeValue(entityModel, "FirstName", 0, attributeTemplatePrefix);
        Integer valueNicknameEntity3 = getGeneratedValueFromSimpleAttributeValue(entityModel, "Nickname", 0, attributeTemplatePrefix);

        assertEquals(valueFirstNameEntity2, valueFirstNameEntity3, "Firstname value should be the same");
        assertEquals(valueNicknameEntity2, valueNicknameEntity3, "Nickname should be the same");
    }

    @TmsLink("RP-TC-5809")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Business configuration with generators rules validation")
    public void runRpTc5809() throws Exception {
        // Precondition
        // #1
        // TODO rework whole test, with full error check and move it to Integration level t all
        String step1ErrMsg = "Invalid combination of 'generator' and 'autoGenerationPattern' settings for auto-generated attribute configuration/entityTypes/HCP/attributes/FirstName";
        boolean wasError = false;
        try {
            BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5809_stp1.json"));
        } catch (CommandException e) {
            wasError = true;
            // Check
            assertTrue(e.getCause().getMessage().contains(step1ErrMsg),
                    "Configuration update response does not contain correct error message. But we have: " + e.getCause().getMessage());
        }
        assertTrue(wasError, "No error in response, when expected ");
        // #2

        wasError = false;
        String step2ErrMsg = "Invalid value for setting 'autoGenerationPattern' for auto-generated attribute configuration/entityTypes/HCP/attributes/License/attributes/ExpirationDate";
        try {
            BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5809_stp2.json"));
        } catch (CommandException e) {
            wasError = true;
            // Check
            assertTrue(e.getCause().getMessage().contains(step2ErrMsg),
                    "Configuration update response does not contain correct error message. But we have: " + e.getCause().getMessage());
        }
        assertTrue(wasError, "No error in response, when expected ");

        // #3
        wasError = false;
        String step3ErrMsg = "Generator should be applied to either attributes of referenced entity or to attributes of relation";
        try {
            BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_5809_stp3.json"));
        } catch (CommandException e) {
            wasError = true;
            // Check
            assertTrue(e.getCause().getMessage().contains(step3ErrMsg),
                    "Configuration update response does not contain correct error message. But we have: " + e.getCause().getMessage());
        }
        assertTrue(wasError, "No error in response, when expected ");
    }

    @TmsLink("RP-TC-6125")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Merge entities with autogenerators by automatic matchGroup")
    public void runRpTc6125() throws Exception {
        //tenant should have "matchingStrategy": "INCREMENTAL"
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_6125_precondition.json"));

        AttributeModel matchAttribute = new SimpleAttributeModel(
                RandomGenerator.getInstance().getRandomAlphabeticString(16)
        );
        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_6125_st1.json"));
        e1.addAttributeToModel("MatchAttribute", matchAttribute);
        e1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        String valueAttrSimple1 = e1.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple1);

        EntityModel e2 = new EntityModel(loadFile("entity_RP_TC_6125_st2.json"));
        e2.addAttributeToModel("MatchAttribute", matchAttribute);
        e2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e2.post();
        SmartWaiting.waitForConsistency(e2);
        String valueAttrSimple2 = e2.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple2);
        SmartWaiting.waitForEntitiesToBeMerged(e1, e2,
                "not able to wait for e1 and e2 to be merged");

        e2.get();
        List<String> listOfAttributeValues = new ArrayList<>();
        for (AttributeModel attributeModel : e2.getAttributes(nameAttrSimple)) {
            listOfAttributeValues.add(attributeModel.getAsSimple().getValue());
        }
        List<AttributeModel> nameAttrs2 = e2.getAttributes(nameAttrSimple);
        assertEquals(nameAttrs2.size(), 2, "Incorrect number of '" + nameAttrSimple + "' values");
        assertTrue(listOfAttributeValues.containsAll(Arrays.asList(valueAttrSimple1, valueAttrSimple2)), "Entities are not merged.");
    }

    @TmsLink("RP-TC-7104")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression", "sanity", "smoke"}, description = "Create entity with reference attribute that has autogenerated fields, Sequential")
    public void runRpTc7104() throws Exception {
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);
        String sequentialGeneratorName = sequentialGeneratorModel.getName();
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7104_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoGenerated",
                sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_7104_st1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        Integer generatedValue = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated values is not in expected range");

        String uriOfReferencedEntity = em.getAttribute("Reference").getAsReference().getEndObjectUri();
        EntityModel referencedEntity = new EntityModel(uriOfReferencedEntity, Config.getTenantUrl()).get();
        //check of generated value in referenced entity
        generatedValue = getGeneratedValueIntFromString(
                referencedEntity.getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated values is not in expected range");
    }

    @TmsLink("RP-TC-7110")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Create entity with reference attribute that has autogenerated fields, UUID")
    public void runRpTc7110() throws Exception {
        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7104_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_7104_st1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value
        String valueWithGeneratedUUID = em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, valueWithGeneratedUUID);
        String uriOfReferencedEntity = em.getAttribute("Reference").getAsReference().getEndObjectUri();
        EntityModel referencedEntity = new EntityModel(uriOfReferencedEntity, Config.getTenantUrl()).get();
        //check of generated value in referenced entity
        valueWithGeneratedUUID = referencedEntity.getAttribute("RefString").getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, valueWithGeneratedUUID);
    }

    @TmsLink("RP-TC-7113")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Check for relation Simple attribute autogeneration, Sequential")
    public void runRpTc7113() throws Exception {
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);

        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_7113_precondition.json"),
                "configuration/relationTypes/Ref", "configuration/relationTypes/Ref/attributes/Status",
                sequentialGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel objectEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e1.json")).post();
        SmartWaiting.waitForConsistency(objectEntityModel);

        EntityModel referencedEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e2.json")).post();
        SmartWaiting.waitForConsistency(referencedEntityModel);

        RelationModel relationModel = new RelationModel(loadFile("relation_RP_TC_7113.json"),
                Config.getTenantUrl());
        relationModel.setStartObjectUri(objectEntityModel.getUri());
        relationModel.setEndObjectUri(referencedEntityModel.getUri());
        relationModel.post();
        Integer generatedValue = getGeneratedValueIntFromString(
                relationModel.getAttribute("Status").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated value is not in expected range");
    }

    @TmsLink("RP-TC-7119")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Check for relation Simple attribute autogeneration, UUID")
    public void runRpTc7119() throws Exception {
        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);

        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_7113_precondition.json"),
                "configuration/relationTypes/Ref", "configuration/relationTypes/Ref/attributes/Status",
                uuidGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel objectEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e1.json")).post();
        SmartWaiting.waitForConsistency(objectEntityModel);

        EntityModel referencedEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e2.json")).post();
        SmartWaiting.waitForConsistency(referencedEntityModel);

        RelationModel relationModel = new RelationModel(loadFile("relation_RP_TC_7113.json"),
                Config.getTenantUrl());
        relationModel.setStartObjectUri(objectEntityModel.getUri());
        relationModel.setEndObjectUri(referencedEntityModel.getUri());
        relationModel.post();
        String generatedValue = relationModel.getAttribute("Status").getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, generatedValue);
    }

    @TmsLink("RP-TC-7121")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Create relation with nested attribute that has autogenerated fields, Sequential")
    public void runRpTc7121() throws Exception {
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);

        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_7121_precondition.json"),
                "configuration/relationTypes/Ref", "configuration/relationTypes/Ref/attributes/Nested",
                sequentialGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel objectEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e1.json")).post();
        SmartWaiting.waitForConsistency(objectEntityModel);

        EntityModel referencedEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e2.json")).post();
        SmartWaiting.waitForConsistency(referencedEntityModel);

        RelationModel relationModel = new RelationModel(loadFile("relation_RP_TC_7121.json"),
                Config.getTenantUrl());
        relationModel.setStartObjectUri(objectEntityModel.getUri());
        relationModel.setEndObjectUri(referencedEntityModel.getUri());
        relationModel.post();
        Integer generatedValue = getGeneratedValueIntFromString(
                relationModel.getAttribute("Nested").getAsNested()
                        .getAttribute("NestedString").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated value is not in expected range");
    }

    @TmsLink("RP-TC-7132")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Create relation with nested attribute that has autogenerated fields, UUID")
    public void runRpTc7132() throws Exception {
        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);

        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_7121_precondition.json"),
                "configuration/relationTypes/Ref", "configuration/relationTypes/Ref/attributes/Nested",
                uuidGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel objectEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e1.json")).post();
        SmartWaiting.waitForConsistency(objectEntityModel);

        EntityModel referencedEntityModel = new EntityModel(loadFile("entity_RP_TC_7113_e2.json")).post();
        SmartWaiting.waitForConsistency(referencedEntityModel);

        RelationModel relationModel = new RelationModel(loadFile("relation_RP_TC_7121.json"),
                Config.getTenantUrl());
        relationModel.setStartObjectUri(objectEntityModel.getUri());
        relationModel.setEndObjectUri(referencedEntityModel.getUri());
        relationModel.post();
        String generatedValue = relationModel.getAttribute("Nested").getAsNested()
                .getAttribute("NestedString").getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, generatedValue);
    }

    @TmsLink("RP-TC-7133")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "GenerateIfEmpty=true/false, reference attribute, Sequential")
    public void runRpTc7133() throws Exception {
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);
        String sequentialGeneratorName = sequentialGeneratorModel.getName();
        //generateIfEmpty=true, entity has value in post request body
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_7133_st1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertTrue(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity does not have Referenced.RefString attribute, step 1");
        Integer generatedValue = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated value is not in expected range. Step 1");

        //generateIfEmpty=true, entity has not value in post request body
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7133_st2.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertTrue(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity does not have Referenced.RefString attribute, step 2");
        generatedValue = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated value is not in expected range. Step 2");

        //generateIfEmpty=false, entity has value in post request body
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st2.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7133_st1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertTrue(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity does not have Referenced.RefString attribute, step 3");
        generatedValue = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValue >= rangeStartForSeqGenerator, "Generated value is not in expected range. Step 3");

        //generateIfEmpty=false, entity has no value in post request body
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st2.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7133_st2.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertFalse(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity has Referenced.RefString attribute, step 4");
    }

    @TmsLink("RP-TC-7165")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "GenerateIfEmpty=true/false, reference attribute, UUID")
    public void runRpTc7165() throws Exception {
        String attributeTemplatePrefix = "Simple_";

        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);
        String uuidGeneratorName = uuidGeneratorModel.getName();

        //generateIfEmpty=true, entity has value in post request body
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_7133_st1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertTrue(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity does not have Referenced.RefString attribute, step 1");
        String generatedValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue();
        assertTrue(generatedValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");

        //generateIfEmpty=true, entity has not value in post request body
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7133_st2.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertTrue(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity does not have Referenced.RefString attribute, step 2");
        generatedValue = em.getAttribute("Reference").getAsReference().getAttribute("RefString").getAsSimple().getValue();
        assertTrue(generatedValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");

        //generateIfEmpty=false, entity has value in post request body
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st2.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7133_st1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertTrue(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity does not have Referenced.RefString attribute, step 3");
        generatedValue = em.getAttribute("Reference").getAsReference().getAttribute("RefString").getAsSimple().getValue();
        assertTrue(generatedValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");

        //generateIfEmpty=false, entity has no value in post request body
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7133_precondition_st2.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7133_st2.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        assertFalse(em.getAttribute("Reference").getAsReference().getAttributes().keySet().contains("RefString"),
                "Entity has Referenced.RefString attribute, step 4");
    }

    @TmsLink("RP-TC-7134")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "uniqueGeneratedValuePerEntityPerRequest=true/false, multiple fields for reference, Sequential")
    public void runRpTc7134() throws Exception {
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeTemplatePrefix = "Simple_";

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);
        String sequentialGeneratorName = sequentialGeneratorModel.getName();

        //uniqueGeneratedValuePerEntityPerRequest = false
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7134_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString2", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoGenerated",
                sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_7134.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        Integer generatedValueRefString = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        Integer generatedValueRefString2 = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString2").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValueRefString >= rangeStartForSeqGenerator, "Generated value for Reference.RefString is not in expected range");
        assertTrue(generatedValueRefString2 >= rangeStartForSeqGenerator, "Generated value for Reference.RefString2 is not in expected range");
        assertEquals(generatedValueRefString, generatedValueRefString2,
                "Generated values should be equal for uniqueGeneratedValuePerEntityPerRequest=true");

        //uniqueGeneratedValuePerEntityPerRequest = false
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7134_precondition_st2.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString2", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoGenerated",
                sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7134.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        generatedValueRefString = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue(), attributeTemplatePrefix);
        generatedValueRefString2 = getGeneratedValueIntFromString(em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString2").getAsSimple().getValue(), attributeTemplatePrefix);
        assertTrue(generatedValueRefString >= rangeStartForSeqGenerator,
                "Generated value for Reference.RefString is not in expected range");
        assertTrue(generatedValueRefString2 >= rangeStartForSeqGenerator,
                "Generated value for Reference.RefString is not in expected range");
        assertNotEquals(generatedValueRefString, generatedValueRefString2,
                "Generated values should be unique for uniqueGeneratedValuePerEntityPerRequest=false");
    }

    @TmsLink("RP-TC-7135")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "uniqueGeneratedValuePerEntityPerRequest=true/false, multiple fields for reference, UUID")
    public void runRpTc7135() throws Exception {
        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);
        String uuidGeneratorName = uuidGeneratorModel.getName();

        //uniqueGeneratedValuePerEntityPerRequest = false
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7134_precondition_st1.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString2", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoGenerated",
                uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_7134.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        String generatedValueRefString = em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue();
        String generatedValueRefString2 = em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString2").getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, generatedValueRefString);
        shouldMatchToPattern(patternAttrSimple, generatedValueRefString2);
        assertEquals(generatedValueRefString, generatedValueRefString2,
                "Generated values should be equal for uniqueGeneratedValuePerEntityPerRequest=true");

        //uniqueGeneratedValuePerEntityPerRequest = false
        l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_7134_precondition_st2.json"),
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfEntityType(l3config,
                "configuration/entityTypes/Referenced",
                "configuration/entityTypes/Referenced/attributes/RefString2", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoGenerated",
                uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_7134.json")).post();
        SmartWaiting.waitForConsistency(em);

        //check of generated value in parent entity
        generatedValueRefString = em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString").getAsSimple().getValue();
        generatedValueRefString2 = em.getAttribute("Reference").getAsReference()
                .getAttribute("RefString2").getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, generatedValueRefString);
        shouldMatchToPattern(patternAttrSimple, generatedValueRefString2);
        assertNotEquals(generatedValueRefString, generatedValueRefString2,
                "Generated values should be unique for uniqueGeneratedValuePerEntityPerRequest=false");
    }

    @TmsLink("RP-TC-7186")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Create entity with reference attribute that has many autogenerated fields(simple+nested+cr), Seq")
    public void runRpTc7186() throws Exception {
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000);
        String attributeTemplatePrefix = "Simple_";
        String nestedAttributeTemplatePrefix = "Nested_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);
        String sequentialGeneratorName = sequentialGeneratorModel.getName();

        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation1.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_refRelation1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //checks
        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        assertTrue(simpleRelationAttributeValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(simpleRelationAttributeValue, attributeTemplatePrefix));
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        assertTrue(nestedRelationAttributeValue.startsWith(nestedAttributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(nestedRelationAttributeValue, nestedAttributeTemplatePrefix));

        assertEquals(em.getCrosswalks().size(), 1, "Entity " + em.getUri() + " has unexpected number of crosswalks");
        assertTrue(em.getCrosswalkByType(crosswalkType).getValue().startsWith(crosswalkTemplatePrefix), "Entity has unexpected crosswalk value format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(
                em.getCrosswalkByType(crosswalkType).getValue(), crosswalkTemplatePrefix));

        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForSeqGenerator),
                "Generated values are not in expected range");
    }

    @TmsLink("RP-TC-7185")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Create entity with reference attribute that has many autogenerated fields(simple+nested+cr), UUID")
    public void runRpTc7185() throws Exception {
        String attributeTemplatePrefix = "Simple_";
        String nestedAttributeTemplatePrefix = "Nested_";
        String crosswalkTemplatePrefix = "Source_";
        String crosswalkType = "configuration/sources/AutoSeq";
        List<String> listOfGeneratedValues = new ArrayList<>();

        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);
        String uuidGeneratorName = uuidGeneratorModel.getName();

        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation1.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                uuidGeneratorName);
        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_refRelation1.json")).post();
        SmartWaiting.waitForConsistency(em);

        //checks
        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");

        String simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        shouldMatchToPattern(attributeTemplatePrefix + PATTERN, simpleRelationAttributeValue, true);
        listOfGeneratedValues.add(simpleRelationAttributeValue);


        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        shouldMatchToPattern(nestedAttributeTemplatePrefix + PATTERN, nestedRelationAttributeValue, true);
        listOfGeneratedValues.add(nestedRelationAttributeValue);

        assertEquals(em.getCrosswalks().size(), 1, "Entity " + em.getUri() + " has unexpected number of crosswalks");
        String crosswalkValue = em.getCrosswalkByType(crosswalkType).getValue();
        shouldMatchToPattern(crosswalkTemplatePrefix + PATTERN, crosswalkValue, true);
        listOfGeneratedValues.add(crosswalkValue);
        assertEquals(listOfGeneratedValues.stream().distinct().toList().size(), listOfGeneratedValues.size(),
                "Not all generated values are unique");

        listOfGeneratedValues.forEach(log::info);
    }

    @TmsLink("RP-TC-7188")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "GenerateIfEmpty=true/false, simple field in nested reference attribute, Sequential")
    public void runRpTc7188() throws Exception { //fails due to RP-30897
        Integer rangeStartForSeqGenerator = randomGenerator.getRandomNumberInRange(0, 1000);
        String attributeTemplatePrefix = "Simple_";
        String nestedAttributeTemplatePrefix = "Nested_";
        List<Integer> listOfGeneratedValues = new ArrayList<>();

        GeneratorModel sequentialGeneratorModel = new GeneratorModel()
                .setType("SEQUENTIAL").setRangeStart(rangeStartForSeqGenerator).setStep(1);
        registerGenerator(sequentialGeneratorModel);
        String sequentialGeneratorName = sequentialGeneratorModel.getName();
        //step 1, generateIfEmpty = true, no values in post body
        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation2.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_refRelation2.json")).post();
        SmartWaiting.waitForConsistency(em);

        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        assertTrue(simpleRelationAttributeValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(simpleRelationAttributeValue, attributeTemplatePrefix));


        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        assertTrue(nestedRelationAttributeValue.startsWith(nestedAttributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(nestedRelationAttributeValue, nestedAttributeTemplatePrefix));
        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForSeqGenerator),
                "Generated values are not in expected range");
        listOfGeneratedValues.clear();

        //step 2, generateIfEmpty = true, post body contains values
        l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation2.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_refRelation1.json")).post();
        SmartWaiting.waitForConsistency(em);

        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        assertTrue(simpleRelationAttributeValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(simpleRelationAttributeValue, attributeTemplatePrefix));
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        assertTrue(nestedRelationAttributeValue.startsWith(nestedAttributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(nestedRelationAttributeValue, nestedAttributeTemplatePrefix));
        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForSeqGenerator),
                "Generated values are not in expected range");
        listOfGeneratedValues.clear();

        //step 3, generateIfEmpty = false, post body does not contain values
        l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation1.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_refRelation2.json")).post();
        SmartWaiting.waitForConsistency(em);

        assertEquals(em.getAttributes().size(), 0, "Entity " + em.getUri() + " has unexpected number of attributes");

        //step 4, generateIfEmpty = false, post body contains values
        l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation1.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", sequentialGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                sequentialGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_refRelation1.json")).post();
        SmartWaiting.waitForConsistency(em);

        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        assertTrue(simpleRelationAttributeValue.startsWith(attributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(simpleRelationAttributeValue, attributeTemplatePrefix));
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        assertTrue(nestedRelationAttributeValue.startsWith(nestedAttributeTemplatePrefix), "Attribute value does not have expected format");
        listOfGeneratedValues.add(getGeneratedValueIntFromString(nestedRelationAttributeValue, nestedAttributeTemplatePrefix));
        assertTrue(listOfGeneratedValues.stream().allMatch(item -> item >= rangeStartForSeqGenerator),
                "Generated values are not in expected range");
        listOfGeneratedValues.clear();
    }

    @TmsLink("RP-TC-7187")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "GenerateIfEmpty=true/false, simple field in nested reference attribute, UUID")
    public void runRpTc7187() throws Exception { //fails due to RP-30897
        String attributeTemplatePrefix = "Simple_";
        String nestedAttributeTemplatePrefix = "Nested_";
        List<String> listOfGeneratedValues = new ArrayList<>();

        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);
        String uuidGeneratorName = uuidGeneratorModel.getName();
        //generateIfEmpty = true, no values in post body
        String l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation2.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                uuidGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        EntityModel em = new EntityModel(loadFile("entity_RP_TC_refRelation2.json")).post();
        SmartWaiting.waitForConsistency(em);
        SmartWaiting.waitForEntityHasNumberOfAttributeValues(em, "Reference", 1);

        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        shouldMatchToPattern(attributeTemplatePrefix + PATTERN, simpleRelationAttributeValue, true);
        listOfGeneratedValues.add(simpleRelationAttributeValue);

        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        String nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        shouldMatchToPattern(nestedAttributeTemplatePrefix + PATTERN, nestedRelationAttributeValue, true);
        listOfGeneratedValues.add(nestedRelationAttributeValue);

        assertEquals(listOfGeneratedValues.stream().distinct().toList().size(), listOfGeneratedValues.size(),
                "Not all generated values are unique");
        listOfGeneratedValues.clear();

        //generateIfEmpty = true, post body contains values
        l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation2.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                uuidGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_refRelation1.json")).post();
        SmartWaiting.waitForConsistency(em);
        SmartWaiting.waitForEntityHasNumberOfAttributeValues(em, "Reference", 1);

        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        shouldMatchToPattern(attributeTemplatePrefix + PATTERN, simpleRelationAttributeValue, true);
        listOfGeneratedValues.add(simpleRelationAttributeValue);

        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        shouldMatchToPattern(nestedAttributeTemplatePrefix + PATTERN, nestedRelationAttributeValue, true);
        listOfGeneratedValues.add(nestedRelationAttributeValue);

        assertEquals(listOfGeneratedValues.stream().distinct().toList().size(), listOfGeneratedValues.size(),
                "Not all generated values are unique");
        listOfGeneratedValues.clear();

        //generateIfEmpty = false, post body does not contain values
        l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation1.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                uuidGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_refRelation2.json")).post();
        SmartWaiting.waitForConsistency(em);
        SmartWaiting.waitForEntityHasNumberOfAttributeValues(em, "Reference", 0);

        assertEquals(em.getAttributes().size(), 0, "Entity " + em.getUri() + " has unexpected number of attributes");

        //generateIfEmpty = false, post body contains values
        l3config = setGeneratorForAttributeOfRelation(loadFile("config_RP_TC_refRelation1.json"),
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/Status", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/SimpleRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForAttributeOfRelation(l3config,
                "configuration/relationTypes/Ref",
                "configuration/relationTypes/Ref/attributes/NestedRelationAttribute", uuidGeneratorName);
        l3config = setGeneratorForSourceByUri(l3config, "configuration/sources/AutoSeq",
                uuidGeneratorName);

        BusinessConfigurationService.updateConfig(l3config);

        em = new EntityModel(loadFile("entity_RP_TC_refRelation1.json")).post();
        SmartWaiting.waitForConsistency(em);
        SmartWaiting.waitForEntityHasNumberOfAttributeValues(em, "Reference", 1);

        shouldHaveExpectedNumberOfAttributes(em, "Reference", 1);
        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("SimpleRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        simpleRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("SimpleRelationAttribute").getAsSimple().getValue();
        shouldMatchToPattern(attributeTemplatePrefix + PATTERN, simpleRelationAttributeValue, true);
        listOfGeneratedValues.add(simpleRelationAttributeValue);

        assertEquals(em.getAttributes("Reference").get(0).getAsReference().getAttributes("NestedRelationAttribute").size(), 1,
                "Unexpected number of attributes");
        nestedRelationAttributeValue = em.getAttribute("Reference").getAsReference()
                .getAttribute("NestedRelationAttribute").getAsNested().getAttribute("NestedString").getAsSimple().getValue();
        shouldMatchToPattern(nestedAttributeTemplatePrefix + PATTERN, nestedRelationAttributeValue, true);
        listOfGeneratedValues.add(nestedRelationAttributeValue);

        assertEquals(listOfGeneratedValues.stream().distinct().toList().size(), listOfGeneratedValues.size(),
                "Not all generated values are unique");
        listOfGeneratedValues.clear();
    }

    @TmsLink("RP-TC-6001")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "update tenant(with entity without autogenerated) with generated entities(use unique crosswalk)")
    public void runRpTc6001() throws Exception {
        // Precondition
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_6001_precondition.json"));

        // #1
        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6001.json")).post();
        SmartWaiting.waitForConsistency(entityModel);
        CrosswalkModel cm = entityModel.getCrosswalks().get(0);

        String valueAttrSimple1 = entityModel.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple1);

        String url = entityModel.getAttribute("FirstName").getAsSimple().getUri();
        List<AttributeModel> ar1 = new ArrayList<>();
        ar1.add(new SimpleAttributeModel("zzz"));
        CumulativeUpdateModel modelReltio = new CumulativeUpdateModel(CumulativeUpdateModel.UPDATE_ATTRIBUTE, url, ar1);

        EntityService.postCumulativeUpdate(entityModel, modelReltio);
        SmartWaiting.waitForObjectUpdated(entityModel, entityModel.getUpdatedTime(), "EntityModel was not updated");

        String valueAttrSimple2Reltio = entityModel.getAttribute(nameAttrSimple, new CrosswalkModel(CROSSWALK_TYPE_RELTIO, entityModel.getId())).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple2Reltio);
        shouldMatchToPattern(valueAttrSimple2Reltio, valueAttrSimple1, false);

        ar1 = new ArrayList<>();
        ar1.add(new SimpleAttributeModel("zzz"));
        CumulativeUpdateModel model = new CumulativeUpdateModel(CumulativeUpdateModel.UPDATE_ATTRIBUTE, url, ar1, cm);

        EntityService.postCumulativeUpdate(entityModel, model);
        SmartWaiting.waitForObjectUpdated(entityModel, entityModel.getUpdatedTime(), "EntityModel was not updated");

        String valueAttrSimpleCM2 = entityModel.getAttribute("FirstName", cm).getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimpleCM2);
        shouldMatchToPattern(valueAttrSimple1, valueAttrSimpleCM2, false);

        url = entityModel.getAttribute("Nickname").getAsSimple().getUri();
        String valueAttrSimpleNick = entityModel.getAttribute("Nickname").getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimpleNick);
        ar1 = new ArrayList<>();
        ar1.add(new SimpleAttributeModel("zzz"));
        model = new CumulativeUpdateModel(CumulativeUpdateModel.UPDATE_ATTRIBUTE, url, ar1, cm);
        EntityService.postCumulativeUpdate(entityModel, model);

        SmartWaiting.waitForObjectUpdated(entityModel, entityModel.getUpdatedTime(), "EntityModel was not updated");

        String valueAttrSimpleNick2 = entityModel.getAttribute("Nickname", cm).getAsSimple().getValue();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimpleNick2);
        shouldMatchToPattern(valueAttrSimpleNick, valueAttrSimpleNick2, true);
    }

    @TmsLink("RP-TC-6028")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "update attribute for which no generators rules exist duringUpdate generated attr value not generated")
    public void runRpTc6028() throws Exception {
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_6028_precondition.json"));

        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6028.json")).post();
        SmartWaiting.waitForConsistency(entityModel);

        String valueAttrSimple1 = entityModel.getAttribute(nameAttrSimple).getValue().toString();
        shouldMatchToPattern(patternAttrSimple, valueAttrSimple1);

        List<AttributeModel> ar1 = new ArrayList<>();
        ar1.add(new SimpleAttributeModel("zzz"));
        CumulativeUpdateModel insertModel = new CumulativeUpdateModel(CumulativeUpdateModel.INSERT_ATTRIBUTE, entityModel.getUri() +
                "/attributes/Nickname", ar1);

        EntityService.postCumulativeUpdate(entityModel, insertModel);
        SmartWaiting.waitForObjectUpdated(entityModel, entityModel.getUpdatedTime(), "EntityModel was not updated");
        String valueAttrSimple2 = entityModel.getAttribute("Nickname").getValue().toString();
        shouldMatchToPattern("zzz", valueAttrSimple2);
        String valueAttrSimple3 = entityModel.getAttribute("FirstName").getValue().toString();
        shouldMatchToPattern(valueAttrSimple3, valueAttrSimple1);
    }

    @TmsLink("RP-TC-6491")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Override simple with few values")
    public void runRpTc6491() throws Exception {
        // Precondition
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_6491_precondition.json"));

        // #1
        EntityModel entityModel = new EntityModel(loadFile("entity_RP_TC_6491_st1.json"));
        String randomCross = randomGenerator.getRandomNumberInRange(0, 10000).toString();
        entityModel.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, randomCross)).post();
        SmartWaiting.waitForConsistency(entityModel);

        // Checks
        List<String> old_values = new ArrayList<>();
        for (AttributeModel firstNameAttribute : entityModel.getAttributes("FirstName")) {
            old_values.add(firstNameAttribute.getValue().toString());
        }

        // #2
        EntityModel entityModel2 = new EntityModel(loadFile("entity_RP_TC_6491_st1.json"));
        entityModel2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, randomCross)).post();
        SmartWaiting.waitForConsistency(entityModel2);

        // Checks
        List<String> new_values = new ArrayList<>();
        for (AttributeModel firstNameAttribute : entityModel2.getAttributes("FirstName")) {
            new_values.add(firstNameAttribute.getValue().toString());
        }
        new_values.removeAll(old_values);
        assertTrue(new_values.isEmpty(), "Old values not equal new ones");

        // #3
        EntityModel entityModel3 = new EntityModel(loadFile("entity_RP_TC_6491_st3.json")).post();
        SmartWaiting.waitForConsistency(entityModel3);

        // Checks
        for (AttributeModel firstNameAttribute : entityModel3.getAttributes("FirstName")) {
            new_values.add(firstNameAttribute.getValue().toString());
        }

        List<String> new_values_st3 = new_values;
        new_values.removeAll(old_values);
        assertEquals(new_values.size(), 1, "New values count is incorrect");

        //#4
        EntityModel entityModel4 = new EntityModel(loadFile("entity_RP_TC_6491_st4.json")).post();
        SmartWaiting.waitForConsistency(entityModel4);

        // Checks
        List<String> new_values_st4 = new ArrayList<>();
        for (AttributeModel firstNameAttribute : entityModel4.getAttributes("FirstName")) {
            new_values.add(firstNameAttribute.getValue().toString());
        }
        new_values_st3.removeAll(new_values_st4);
        assertEquals(new_values_st3.size(), 1, "New values count is incorrect");
    }

    @TmsLink("RP-TC-7389")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "[HP] Auto-generated ID is not working for nested attribute")
    public void runRpTc7389() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeNestedTemplatePrefix = "ID_Simple_";
        String crosswalkType = "configuration/sources/AutoSeq";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setRangeStart(rangeStartForGenerator1)
                .setStep(1);
        registerGenerator(sequentialGeneratorModel1);

        String l3config = setGeneratorForAttributeInsideNestedOfEntityType(loadFile("config_RP_31525_generateIfEmpty.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License", ATTRIBUTE_HCP_PREFIX + "License/attributes/ID",
                sequentialGeneratorModel1.getName());
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorModel1.getName());
        BusinessConfigurationService.updateConfig(l3config);

        //case 1: Create entity without nested attribute.
        EntityModel entityModel = new EntityModel(loadFile("entity_RP_31525_noNestedValue.json"),
                Config.getTenantUrl());
        entityModel.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel.post();

        shouldHaveExpectedNumberOfAttributes(entityModel, "License", 0);

        //case 2: Create entity with nested attribute present
        entityModel = new EntityModel(loadFile("entity_RP_31525_noIdValue.json"),
                Config.getTenantUrl());
        entityModel.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel.post();

        assertEquals(entityModel.getAttributes("License").get(0).getAsNested().getAttributes("ID").size(), 1,
                "Entity " + entityModel.getUri() + " has unexpected number of attributes License.ID");
        assertTrue(entityModel.getAttributes("License").get(0).getAsNested()
                        .getAttribute("ID").getValue().toString().startsWith(attributeNestedTemplatePrefix),
                "Entity model's License.ID value does not have expected format");
    }

    @TmsLink("RP-TC-7390")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "[HP] Auto-generated ID is not working for nested attribute, update")
    public void runRpTc7390() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String attributeNestedTemplatePrefix = "ID_Simple_";
        String crosswalkType = "configuration/sources/AutoSeq";
        String crosswalkTypeFB = "configuration/sources/FB";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        String l3config = setGeneratorForAttributeInsideNestedOfEntityType(loadFile("config_RP_31525_generateIfEmpty.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License", ATTRIBUTE_HCP_PREFIX + "License/attributes/ID",
                sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        //case 1: entity exists without nested attribute. Post entity without nested attribute.
        String randomValueForCrosswalk = RandomGenerator.getInstance().getRandomAlphabeticString(8);
        EntityModel entityModel1 = new EntityModel(loadFile("entity_RP_31525_noNestedValue.json"),
                Config.getTenantUrl());
        entityModel1.addCrosswalk(new CrosswalkModel(crosswalkTypeFB, randomValueForCrosswalk));
        entityModel1.post();

        EntityModel entityModel2 = new EntityModel(loadFile("entity_RP_31525_noNestedValue.json"),
                Config.getTenantUrl());
        entityModel2.addCrosswalk(new CrosswalkModel(crosswalkTypeFB, randomValueForCrosswalk));
        entityModel2.post();

        shouldHaveExpectedNumberOfAttributes(entityModel1, "License", 0);
        shouldHaveExpectedNumberOfAttributes(entityModel2, "License", 0);

        //case 2: entity exists without nested attribute. Post entity with nested attribute.
        randomValueForCrosswalk = RandomGenerator.getInstance().getRandomAlphabeticString(8);
        entityModel1 = new EntityModel(loadFile("entity_RP_31525_noNestedValue.json"), Config.getTenantUrl());
        entityModel1.addCrosswalk(new CrosswalkModel(crosswalkTypeFB, randomValueForCrosswalk));
        entityModel1.post();

        entityModel2 = new EntityModel(loadFile("entity_RP_31525_noIdValue.json"), Config.getTenantUrl());
        entityModel2.addCrosswalk(new CrosswalkModel(crosswalkTypeFB, randomValueForCrosswalk));
        entityModel2.post();

        shouldHaveExpectedNumberOfAttributes(entityModel1, "License", 0);
        shouldHaveExpectedNumberOfAttributes(entityModel2, "License", 1);
        assertEquals(entityModel2.getAttributes("License").get(0).getAsNested().getAttributes("ID").size(), 1,
                "Entity " + entityModel2.getUri() + " has unexpected number of attributes License.ID");
        assertTrue(entityModel2.getAttributes("License").get(0).getAsNested()
                        .getAttribute("ID").getValue().toString().startsWith(attributeNestedTemplatePrefix),
                "Entity model's License.ID value does not have expected format");
    }

    @TmsLink("RP-TC-7391")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "[HP] Auto-generated ID is not working for nested attribute, generateIfNotEmpty")
    public void runRpTc7391() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String crosswalkType = "configuration/sources/AutoSeq";
        String idValueFromPostBody = "**********";
        String idValueFromPostBodySimple = "**********firstName";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        //step 1: generateOfNotEmpty = true
        String l3config = setGeneratorForAttributeInsideNestedOfEntityType(
                loadFile("config_RP_31525_generateIfNotEmptyTrue.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License",
                ATTRIBUTE_HCP_PREFIX + "License/attributes/ID", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config, ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        //case 1: Create entity with nested attribute present (ID and Category)
        EntityModel entityModel = new EntityModel(loadFile("entity_RP_31525_IdValue.json"),
                Config.getTenantUrl());
        entityModel.addCrosswalk(new CrosswalkModel(crosswalkType,
                RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel.post();

        assertNotEquals(idValueFromPostBody, entityModel.getAttribute("License").getAsNested().getAttribute("ID").getAsSimple().getValue(),
                "License.ID value should be generated if post body contains value in case of generateOfNotEmpty = true");
        assertNotEquals(idValueFromPostBodySimple, entityModel.getAttribute("FirstName").getAsSimple().getValue(),
                "FirstName value should be generated if post body contains value in case of generateOfNotEmpty = true");

        //step 2: generateOfNotEmpty = false
        l3config = setGeneratorForAttributeInsideNestedOfEntityType(
                loadFile("config_RP_31525_generateIfNotEmptyFalse.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License",
                ATTRIBUTE_HCP_PREFIX + "License/attributes/ID", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config, ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        entityModel = new EntityModel(loadFile("entity_RP_31525_IdValue.json"),
                Config.getTenantUrl());
        entityModel.addCrosswalk(new CrosswalkModel(crosswalkType,
                RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel.post();

        assertEquals(idValueFromPostBody, entityModel.getAttribute("License").getAsNested().getAttribute("ID").getAsSimple().getValue(),
                "License.ID value should be not generated if post body contains value in case of generateOfNotEmpty = false");
        assertEquals(idValueFromPostBodySimple, entityModel.getAttribute("FirstName").getAsSimple().getValue(),
                "FirstName value should be not generated if post body contains value in case of generateOfNotEmpty = false");
    }

    @TmsLink("RP-TC-7392")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "[HP] Auto-generated ID is not working for nested attribute, generateIfNotEmpty, update")
    public void runRpTc7392() throws Exception {
        Integer rangeStartForGenerator1 = randomGenerator.getRandomNumberInRange(0, 1000000);
        String crosswalkType = "configuration/sources/AutoSeq";
        String crosswalkTypeFB = "configuration/sources/FB";

        // Precondition
        GeneratorModel sequentialGeneratorModel1 = new GeneratorModel();
        sequentialGeneratorModel1.setType("SEQUENTIAL");
        sequentialGeneratorModel1.setRangeStart(rangeStartForGenerator1);
        sequentialGeneratorModel1.setStep(1);
        registerGenerator(sequentialGeneratorModel1);
        String sequentialGeneratorName1 = sequentialGeneratorModel1.getName();
        setVar("GENERATOR_NAME_IN_TEST", sequentialGeneratorName1, true);
        //step 1: entity exists with nested attribute present (ID="102", Name="XYZX"). Post entity with existing nested attribute.
        //generateIfNotEmpty=false
        String l3config = setGeneratorForAttributeInsideNestedOfEntityType(loadFile("config_RP_31525_generateIfNotEmptyFalse.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License",
                ATTRIBUTE_HCP_PREFIX + "License/attributes/ID", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config, ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        String randomValueForCrosswalk = RandomGenerator.getInstance().getRandomAlphabeticString(8);
        EntityModel entityModel1 = new EntityModel(loadFile("entity_RP_31525_IdValue.json"), Config.getTenantUrl());
        entityModel1.addCrosswalk(new CrosswalkModel(crosswalkTypeFB, randomValueForCrosswalk));
        entityModel1.post();

        String idFromEntity1 = entityModel1.getAttribute("License").getAsNested()
                .getAttribute("ID").getAsSimple().getValue();
        String idFromEntity1updated = idFromEntity1 + "1";
        String firstNameFromEntity1 = entityModel1.getAttribute("FirstName").getAsSimple().getValue();
        String firstNameFroMEntityUpdated = firstNameFromEntity1 + "1";

        EntityModel entityModel2 = new EntityModel(entityModel1.getPostBody(), Config.getTenantUrl());
        entityModel2.getAttribute("License").getAsNested()
                .getAttribute("ID").getAsSimple()
                .setValue(idFromEntity1updated);
        entityModel2.getAttribute("FirstName").setValue(firstNameFroMEntityUpdated);
        entityModel2.post();

        String idFromEntity2 = entityModel2.getAttribute("License").getAsNested()
                .getAttribute("ID").getAsSimple().getValue();
        String firstNameFromEntity2 = entityModel2.getAttribute("FirstName").getAsSimple().getValue();

        assertEquals(idFromEntity2, idFromEntity1updated,
                "License.ID value should not be generated if post body contains value in case of generateIfNotEmpty = false");
        assertEquals(firstNameFromEntity2, firstNameFroMEntityUpdated,
                "FirstName value should not be generated if post body contains value in case of generateIfNotEmpty = false");

        shouldHaveExpectedNumberOfAttributes(entityModel1, "License", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel1, "FirstName", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel2, "License", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel2, "FirstName", 1);

        //step 2: entity exists with nested attribute present (ID="102", Name="XYZX"). Post entity with existing nested attribute.
        //generateIfNotEmpty=true
        l3config = setGeneratorForAttributeInsideNestedOfEntityType(loadFile("config_RP_31525_generateIfNotEmptyTrue.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + "License",
                ATTRIBUTE_HCP_PREFIX + "License/attributes/ID", sequentialGeneratorName1);
        l3config = setGeneratorForAttributeOfEntityType(l3config, ENTITY_TYPE_HCP,
                ATTRIBUTE_HCP_PREFIX + "FirstName", sequentialGeneratorName1);
        l3config = setGeneratorForSourceByUri(l3config, crosswalkType, sequentialGeneratorName1);

        BusinessConfigurationService.updateConfig(l3config);

        randomValueForCrosswalk = RandomGenerator.getInstance().getRandomAlphabeticString(8);
        entityModel1 = new EntityModel(loadFile("entity_RP_31525_IdValue.json"), Config.getTenantUrl());
        entityModel1.addCrosswalk(new CrosswalkModel(crosswalkTypeFB, randomValueForCrosswalk));
        entityModel1.post();

        idFromEntity1 = entityModel1.getAttribute("License").getAsNested()
                .getAttribute("ID").getAsSimple().getValue();
        firstNameFromEntity1 = entityModel1.getAttribute("FirstName").getAsSimple().getValue();

        entityModel2 = new EntityModel(entityModel1.getPostBody(), Config.getTenantUrl());
        entityModel2.getAttribute("License").getAsNested()
                .getAttribute("ID").getAsSimple()
                .setValue(idFromEntity1 + "1");
        entityModel2.getAttribute("FirstName").setValue(firstNameFromEntity1 + "1");
        entityModel2.post();

        idFromEntity2 = entityModel2.getAttribute("License").getAsNested()
                .getAttribute("ID").getAsSimple().getValue();
        firstNameFromEntity2 = entityModel2.getAttribute("FirstName").getAsSimple().getValue();

        assertNotEquals(idFromEntity2, idFromEntity1,
                "License.ID value should be generated if post body contains value in case of generateIfNotEmpty = true");
        assertNotEquals(firstNameFromEntity2, firstNameFromEntity1,
                "FirstName value should be generated if post body contains value in case of generateIfNotEmpty = true");

        shouldHaveExpectedNumberOfAttributes(entityModel1, "License", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel1, "FirstName", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel2, "License", 1);
        shouldHaveExpectedNumberOfAttributes(entityModel2, "FirstName", 1);
    }

    @TmsLink("RP-TC-5832")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Merge/Unmerge simple attributes with autogenerated values")
    public void runRpTc5832() throws Exception {
        // Precondition
        String crosswalkType = CROSSWALK_TYPE_HMS;
        //Create a generator gen\d\d\d\d5832u1
        GeneratorModel uuidGeneratorModel = new GeneratorModel();
        registerGenerator(uuidGeneratorModel);
        //get 5802 config as etalon and edit generator for HCP.FirstName
        String l3config = setGeneratorForAttributeOfEntityType(loadFile("config_RP_TC_5802_precondition.json"),
                ENTITY_TYPE_HCP, ATTRIBUTE_HCP_PREFIX + nameAttrSimple, uuidGeneratorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        //Test
        // #1 Create 1st entity
        EntityModel entityModel1 = new EntityModel(loadFile("entity_RP_TC_5832_st1.json"));
        entityModel1.addCrosswalk(new CrosswalkModel(crosswalkType, RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel1.addAttributeToModel("LastName", new SimpleAttributeModel(RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel1.post();
        SmartWaiting.waitForConsistency(entityModel1);
        //store some data for future checks
        String firstNameFromEntity1 = entityModel1.getAttribute(nameAttrSimple).getAsSimple().getValue();
        // Checks
        shouldMatchToPattern(patternAttrSimple, firstNameFromEntity1);

        // #1 Create 2nd entity
        EntityModel entityModel2 = new EntityModel(loadFile("entity_RP_TC_5832_st2.json"));
        entityModel2.addCrosswalk(new CrosswalkModel(crosswalkType, RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel2.addAttributeToModel("LastName", new SimpleAttributeModel(RandomGenerator.getInstance().getRandomAlphabeticString(8)));
        entityModel2.post();
        SmartWaiting.waitForConsistency(entityModel2);
        //store some data for future checks
        String firstNameFromEntity2 = entityModel2.getAttribute(nameAttrSimple).getAsSimple().getValue();
        // Checks
        shouldMatchToPattern(patternAttrSimple, firstNameFromEntity2);
        assertNotEquals(firstNameFromEntity1, firstNameFromEntity2, "Value of 'FirstName' isn't changed");

        // #2 Merge 1st and 2nd entities
        EntityService.mergeEntities(entityModel1, entityModel2);
        SmartWaiting.waitForEntitiesToBeMerged(entityModel1, entityModel2,
                "Not able to wait for entityModel1 and entityModel2 to be merged");
        // Checks
        //The names should be the same for both entities
        assertEquals(entityModel1.getAttribute(nameAttrSimple).getAsSimple().getValue(), entityModel2.getAttribute(nameAttrSimple).getAsSimple().getValue(),
                "Values of 'FirstNames' are different.");
        //The name of 1st entity should be in [name1,name2] array
        assertTrue(firstNameFromEntity1.equals(entityModel1.getAttribute(nameAttrSimple).getAsSimple().getValue()) ||
                        firstNameFromEntity2.equals(entityModel1.getAttribute(nameAttrSimple).getAsSimple().getValue()),
                "Value of 'FirstName' is changed for first entity after merge");

        // #3 UnMerge 1st and 2nd entities
        EntityService.unmergeEntities(entityModel1, entityModel2);
        SmartWaiting.waitForEntitiesToBeUnMerged(entityModel1, entityModel2,
                "not able to wait for entityModel1 and entityModel2 to be unmerged");
        // Checks
        //The name of 2nd entity should be in [name1,name2] array
        assertTrue(firstNameFromEntity1.equals(entityModel2.getAttribute(nameAttrSimple).getAsSimple().getValue()) ||
                        firstNameFromEntity2.equals(entityModel2.getAttribute(nameAttrSimple).getAsSimple().getValue()),
                "Value of 'FirstName' is changed for first entity after unmerge");
    }

    @TmsLink("RP-TC-5803")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Add/update generators validation/incorrect input")
    public void runRpTc5803() throws Exception {
        //Precondition. Create a sequential generator
        GeneratorModel seqGen1_5803 = new GeneratorModel();
        seqGen1_5803.setType("SEQUENTIAL");
        seqGen1_5803.setRangeStart(-100);
        seqGen1_5803.setStep(5);
        registerGenerator(seqGen1_5803);

        //Test
        // #1 Try to create generator with invalid type.
        GeneratorModel gen1_5803 = new GeneratorModel();
        gen1_5803.setType("UUIDz");
        try {
            registerGenerator(gen1_5803);
            Assert.fail("Invalid generator's type request should fail");
        } catch (Exception exception_result) {
            Assert.assertTrue(exception_result.getMessage().contains("not one of the values accepted for Enum class"),
                    "Invalid generator type error message contains reference to an enum");
        } //Catch errors for the negative case of creation of a generator
        // Checks
        assertFalse(GeneratorService.isGeneratorExist(gen1_5803), "Generator Gen1_5803 with invalid type UUIDz was created");

        // #2 Try to create valid generator
        gen1_5803.setType("UUID");
        registerGenerator(gen1_5803);
        // Checks
        assertTrue(GeneratorService.isGeneratorExist(gen1_5803), "Generator Gen1_5803 with valid type UUID wasn't created");

        // #3 Try to update the generator with invalid uuidVersion
        gen1_5803.setUUIDVersion(2);
        try {
            registerGenerator(gen1_5803);
            Assert.fail("Generator override request should fail");
        } catch (Exception exception_result) {
            //Catch errors for the negative case of update of the generator
            Assert.assertTrue(exception_result.getMessage().contains("Can't override existing generator"),
                    "Generator override message is valid");
        }
        // Checks
        assertNotEquals(GeneratorService.getGeneratorByName(gen1_5803.getName()).getUUIDVersion(), 5,
                "Generator with invalid type UUID=5 was updated");

        // #4 Try to update sequential generator
        seqGen1_5803.setStep(10);
        try {
            registerGenerator(seqGen1_5803);
        } catch (Exception exception_result) {
        } //Catch errors for the negative case of creation of a generator
        // Checks
        assertNotEquals(GeneratorService.getGeneratorByName(seqGen1_5803.getName()).getStep(), "10",
                "Generator SeqGen1_5803 was updated");
    }

    @TmsLink("RP-TC-5804")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Get generator by name")
    public void runRpTc5804() throws Exception {
        //Precondition. Create a generator
        GeneratorModel gen1_5804 = new GeneratorModel();
        registerGenerator(gen1_5804);

        //Test
        // #1 Try to get existed generator
        GeneratorModel gen1_5804_test = GeneratorService.getGeneratorByName(gen1_5804.getName());
        // Checks
        assertEquals(gen1_5804_test, gen1_5804, "The generator was not received correctly");

        // #2 Try to get not existed generator
        GeneratorModel gen1_5804_test2 = new GeneratorModel();
        assertFalse(GeneratorService.isGeneratorExist(gen1_5804_test2), "Not existed generator (" + gen1_5804_test2.getName() + ") was received");

        // #3 Try to remove existed generator
        GeneratorService.removeGenerator(gen1_5804);
        assertFalse(GeneratorService.isGeneratorExist(gen1_5804), "The existed generator was not removed");
    }

    @TmsLink("RP-TC-7585")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "RP-32659 [HP]Generating new sequence number when updating nested attribute")
    public void runRpTc7585() throws Exception {
        //Precondition. Create an UUID generator
        GeneratorModel seqGen1_32659 = new GeneratorModel();
        seqGen1_32659.setType("UUID");
        registerGenerator(seqGen1_32659);
        setVar("GENERATOR_NAME_IN_TEST", seqGen1_32659.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP-32659.json"));

        EntityModel entityModel = new EntityModel(loadFile("entity_RP-32659.json")).post();
        String entityUriAfterFirstPost = entityModel.getUri();
        String simpleValueAfterFirstPost = entityModel.getAttribute("AutoGeneratedSimple").getAsSimple().getValue();
        String idValueAfterFirstPost = entityModel.getAttribute("AutoGeneratedNested").getAsNested()
                .getAttribute("ID").getValue().toString();

        entityModel = new EntityModel(loadFile("entity_RP-32659.json")).post();
        String entityUriAfterSecondPost = entityModel.getUri();
        String simpleValueAfterSecondPost = entityModel.getAttribute("AutoGeneratedSimple").getAsSimple().getValue();
        String idValueAfterSecondPost = entityModel.getAttribute("AutoGeneratedNested").getAsNested()
                .getAttribute("ID").getValue().toString();

        //checks
        assertEquals(entityUriAfterSecondPost, entityUriAfterFirstPost, "entity URIs should be the same");
        assertEquals(idValueAfterSecondPost, idValueAfterFirstPost, "ID values should be the same");
        assertEquals(simpleValueAfterSecondPost, simpleValueAfterFirstPost, "AutoGeneratedSimple values should be the same");
    }

    @TmsLink("RP-TC-7716")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Generator and generator pattern from nested attribute overlap parameters from inner attr")
    public void runRpTc7716() {
        //Try to update tenant with overlapping generator configurations
        try {
            BusinessConfigurationService.updateConfig(loadFile("RP-31754-L3.json"));
        } catch (Exception e) {
            //depends on line 352 of TenantManagementService.java from common code
            //should be updated when ErrorModel will be added to QA automation framework
            assertTrue(e.getMessage().contains("An error occurred on updating business-configuration"),
                    "There was no expected error message on attempt to update tenant with invalid L3");
        }
    }

    @TmsLink("RP-TC-7725")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "[HP] Attribute update on cumulative update")
    public void runRpTc7725() throws Exception {
        //update of L3
        GeneratorModel generatorModel = new GeneratorModel();
        generatorModel.setType("SEQUENTIAL");
        generatorModel.setRangeStart(10);
        generatorModel.setStep(1);
        registerGenerator(generatorModel);

        String l3config = setGeneratorForAttributeOfEntityType(loadFile("RP-33014-cu-config.json"),
                ENTITY_TYPE_PREFIX + "/SalesTerritoryRule", ENTITY_TYPE_PREFIX + "/SalesTerritoryRule/attributes/ID", generatorModel.getName());
        BusinessConfigurationService.updateConfig(l3config);

        //SalesTerritoryRule
        CrosswalkModel firstMigrationCrosswalk = new CrosswalkModel("configuration/sources/MIGRATION",
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        EntityModel entityModel0 = new EntityModel(loadFile("RP-33014-cu-entity.json"));
        entityModel0.addCrosswalk(firstMigrationCrosswalk);
        entityModel0.post();
        SmartWaiting.waitForConsistency(entityModel0);
        log.info("entityModel0: " + entityModel0);

        //SalesTerritoryRule override
        CrosswalkModel secondReltioCrosswalk = new CrosswalkModel("configuration/sources/Reltio",
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        EntityModel entityModel00 = new EntityModel(loadFile("RP-33014-cu-entity2.json"));
        entityModel00.addCrosswalk(secondReltioCrosswalk);
        entityModel00.post();
        SmartWaiting.waitForConsistency(entityModel00);
        //SalesTerritory
        CrosswalkModel firstReltioCrosswalk = new CrosswalkModel("configuration/sources/Reltio",
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        EntityModel entityModel1 = new EntityModel(loadFile("RP-33014-cu-entity2.json"));
        entityModel1.addCrosswalk(firstReltioCrosswalk);
        entityModel1.post();
        SmartWaiting.waitForConsistency(entityModel1);
        //posting relation
        RelationModel relationModel = new RelationModel("configuration/relationTypes/STtoSTR");
        relationModel.setStartObjectUri(entityModel1.getUri());
        relationModel.setEndObjectUri(entityModel0.getUri());
        relationModel.post();
        SmartWaiting.waitForConsistency(entityModel0);
        entityModel0.refresh();
        String beforeUpdate = entityModel0.get().getAttribute("ID").getAsSimple().getValue();

        //post of cumulative update
        CumulativeUpdateModel ignoreAttribute = new CumulativeUpdateModel(
                CumulativeUpdateModel.IGNORE_ATTRIBUTE,
                entityModel0.getAttribute("Name").getUri());
        ignoreAttribute.setNewValue("true");
        SmartWaiting.waitForEmptyMatchEventsQueue();
        CumulativeUpdateModel insertAttribute = new CumulativeUpdateModel(
                CumulativeUpdateModel.INSERT_ATTRIBUTE,
                entityModel0.getUri() + "/attributes/Name");
        insertAttribute.newValue(new SimpleAttributeModel("John Doe"));
        SmartWaiting.waitForEmptyMatchEventsQueue();
        EntityService.postCumulativeUpdate(entityModel0,
                Arrays.asList(ignoreAttribute, insertAttribute));
        SmartWaiting.waitForConsistency(entityModel0);
        entityModel0.refresh();
        log.info("entityModel0: " + entityModel0);

        //checks
        List<String> listOfIDs = new ArrayList<>();
        for (AttributeModel attributeModel : entityModel0.get().getAttributes("ID")) {
            listOfIDs.add(attributeModel.getAsSimple().getValue());
        }
        List<String> listOfNames = new ArrayList<>();
        for (AttributeModel attributeModel : entityModel0.get().getAttributes("Name")) {
            listOfNames.add(attributeModel.getAsSimple().getValue());
        }

        assertEquals(listOfIDs.size(), 2, "entity should have 2 IDs after cumulative update");
        assertEquals(listOfNames.size(), 2, "entity should have 2 Names after cumulative update");
        assertFalse(listOfNames.stream().noneMatch(name -> name.equals("John Doe")), "Name field should be updated");

        assertTrue(
                listOfIDs.stream()
                        .map(Integer::parseInt)
                        .max(Integer::compare)
                        .orElse(0)
                        .intValue() >= Integer.parseInt(beforeUpdate) + 2,
                "Generator should be called 2 times"
        );
    }

    @TmsLink("RP-TC-8124")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Auto Generated ID should not be regenerated on multiple posting of entity")
    public void runRpTc8124() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        generator.setType("SEQUENTIAL");
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_Lilly.json"));

        EntityModel entityModel = new EntityModel(loadFile("entity_RP-35996.json")).post();
        String originalValue = entityModel.getAttribute("HCPUniqueId").getAsSimple().getValue();
        for (int i = 0; i < 16; i++) {
            EntityModel entityModelForPost = new EntityModel(loadFile("entity_RP-35996.json")).post();
            assertEquals(originalValue, entityModelForPost.getAttribute("HCPUniqueId").getAsSimple().getValue());
        }
    }

    @TmsLink("RP-TC-8221")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generatedValueUniqueForCrosswalk create generated value for each crosswalk (Case 1)")
    public void runRpTc8221() throws Exception {
        String HCPUniqueId = "HCPUniqueId";
        String sourceTable = "CODS.HCO";
        String cwType = "configuration/sources/CODS";

        GeneratorModel generator_Lilly = new GeneratorModel();
        generator_Lilly.setType("SEQUENTIAL");
        generator_Lilly.setRangeStart(1);
        generator_Lilly.setStep(1);
        registerGenerator(generator_Lilly);
        setVar("GENERATOR_NAME_IN_TEST", generator_Lilly.getName(), true);


        GeneratorModel generator_Location = new GeneratorModel();
        generator_Location.setType("SEQUENTIAL");
        generator_Location.setStep(1);
        generator_Location.setRangeStart(40000000);
        registerGenerator(generator_Location);
        BusinessConfigurationService.updateConfig(loadFile("L3_Lilly.json"));

        String cw1 = "CW1." + RandomGenerator.getInstance().getRandomString(5);
        String cw2 = "CW2." + RandomGenerator.getInstance().getRandomString(5);

        setVar("CW1", cw1, true);
        EntityModel eHCP1 = new EntityModel(loadFile("Create_34077.json")).post();
        String HCPMDMID1 = eHCP1.getAttribute(HCPUniqueId).getAsSimple().getValue();
        CrosswalkModel cwHCP1 = eHCP1.getCrosswalk(cwType, cw1, sourceTable);

        EntityService.postEntities(loadFile("Update_34077.json"));
        assertEquals(HCPMDMID1, eHCP1.get().getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue());

        setVar("CW1", cw2, true);
        EntityModel eHCP2 = new EntityModel(loadFile("Create_34077.json")).post();
        String HCPMDMID2 = eHCP2.getAttribute(HCPUniqueId).getAsSimple().getValue();
        CrosswalkModel cwHCP2 = eHCP2.getCrosswalk(cwType, cw2, sourceTable);

        setVar("CW1", cw1, true);
        setVar("CW2", cw2, true);
        new EntityModel(loadFile("Crosswalk_Merge1_34077.json")).post();

        SmartWaiting.waitForEntitiesToBeMerged(eHCP1, eHCP2);

        assertEquals(HCPMDMID1, eHCP1.get().getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue());
        assertEquals(HCPMDMID2, eHCP1.get().getAttribute(HCPUniqueId, cwHCP2).getAsSimple().getValue());

        EntityService.unmergeEntities(eHCP1, eHCP2);
        SmartWaiting.waitForEntitiesToBeUnMerged(eHCP1, eHCP2);

        assertEquals(HCPMDMID1, eHCP1.get().getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue());
        assertEquals(HCPMDMID2, eHCP2.get().getAttribute(HCPUniqueId, cwHCP2).getAsSimple().getValue());
    }

    @TmsLink("RP-TC-8223")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generatedValueUniqueForCrosswalk create generated value for each crosswalk (Case 2)")
    public void runRpTc8223() throws Exception {
        String HCPUniqueId = "HCPUniqueId";
        String sourceTable = "CODS.HCO";
        String cwType = "configuration/sources/CODS";

        GeneratorModel generator_Lilly = new GeneratorModel();
        generator_Lilly.setType("SEQUENTIAL");
        generator_Lilly.setRangeStart(1);
        generator_Lilly.setStep(1);
        registerGenerator(generator_Lilly);
        setVar("GENERATOR_NAME_IN_TEST", generator_Lilly.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_Lilly.json"));

        String cw1 = "CW1." + RandomGenerator.getInstance().getRandomString(5);
        String cw2 = "CW2." + RandomGenerator.getInstance().getRandomString(5);

        setVar("CW1", cw1, true);
        EntityModel eHCP1 = new EntityModel(loadFile("Create_34077.json")).post();
        String HCPMDMID1 = eHCP1.getAttribute(HCPUniqueId).getAsSimple().getValue();
        CrosswalkModel cwHCP1 = eHCP1.getCrosswalk(cwType, cw1, sourceTable);

        setVar("CW1", cw1, true);
        setVar("CW2", cw2, true);
        new EntityModel(loadFile("Crosswalk_Merge1_34077.json")).post();
        CrosswalkModel cwHCP2 = eHCP1.get().getCrosswalk(cwType, cw2, sourceTable);
        assertEquals(HCPMDMID1, eHCP1.get().getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue());

        eHCP1.getAttribute("FirstName").update("FN_simple_" + RandomGenerator.getInstance().getRandomString(5), cwHCP2);

        assertEquals(eHCP1.get().getAttributes("FirstName").size(), 2, String.format("Entity '%s' must have 2 values of FirstName attribute", eHCP1.getUri()));
        assertEquals(eHCP1.getAttributes(HCPUniqueId).size(), 2, String.format("Entity '%s' must have 2 values of HCPUniqueId attribute", eHCP1.getUri()));
        assertNotNull(eHCP1.getAttribute(HCPUniqueId, cwHCP2));
        assertNotEquals(eHCP1.getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue(), eHCP1.getAttribute(HCPUniqueId, cwHCP2).getAsSimple().getValue());
    }

    @TmsLink("RP-TC-8224")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generatedValueUniqueForCrosswalk create generated value for each crosswalk (Case 3)")
    public void runRpTc8224() throws Exception {
        String HCPUniqueId = "HCPUniqueId";
        String sourceTable = "CODS.HCO";
        String cwType = "configuration/sources/CODS";

        GeneratorService.removeAllGenerators("Lilly_");

        GeneratorModel generator_Lilly = new GeneratorModel();
        generator_Lilly.setType("SEQUENTIAL");
        generator_Lilly.setRangeStart(1);
        generator_Lilly.setStep(1);
        registerGenerator(generator_Lilly);
        setVar("GENERATOR_NAME_IN_TEST", generator_Lilly.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_Lilly.json"));

        String cw1 = "CW1." + RandomGenerator.getInstance().getRandomString(5);
        String cw2 = "CW2." + RandomGenerator.getInstance().getRandomString(5);

        setVar("CW1", cw1, true);
        EntityModel eHCP1 = new EntityModel(loadFile("Create_34077.json")).post();
        String HCPMDMID1 = eHCP1.getAttribute(HCPUniqueId).getAsSimple().getValue();
        CrosswalkModel cwHCP1 = eHCP1.getCrosswalk(cwType, cw1, sourceTable);

        setVar("CW1", cw2, true);
        setVar("CW2", cw1, true);
        new EntityModel(loadFile("Crosswalk_Merge1_34077_2.json")).post();
        CrosswalkModel cwHCP2 = eHCP1.get().getCrosswalk(cwType, cw2, sourceTable);
        assertEquals(HCPMDMID1, eHCP1.get().getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue());
        assertEquals(eHCP1.getAttributes(HCPUniqueId).size(), 2, String.format("Entity '%s' must have 2 values of HCPUniqueId attribute", eHCP1.getUri()));
        assertNotNull(eHCP1.getAttribute(HCPUniqueId, cwHCP2));
        assertNotEquals(eHCP1.getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue(), eHCP1.getAttribute(HCPUniqueId, cwHCP2).getAsSimple().getValue());

        String HCPMDMID2 = eHCP1.getAttribute(HCPUniqueId, cwHCP2).getAsSimple().getValue();

        EntityService.unmergeEntities(eHCP1, eHCP1);
        EntityModel eHCP2 = new EntityModel(cwHCP2).get();
        SmartWaiting.waitForEntitiesToBeUnMerged(eHCP1, eHCP2);

        assertEquals(HCPMDMID1, eHCP1.get().getAttribute(HCPUniqueId, cwHCP1).getAsSimple().getValue());
        assertEquals(HCPMDMID2, eHCP2.get().getAttribute(HCPUniqueId, cwHCP2).getAsSimple().getValue());
    }

    @TmsLink("RP-TC-8324")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Create autogenerated attributes that needs to be validated")
    public void runRpTc8324() throws Exception {
        GeneratorService.removeAllGenerators("HP_");

        GeneratorModel generator1 = new GeneratorModel();
        generator1.setType("SEQUENTIAL");
        generator1.setStep(1);
        generator1.setRangeStart(1111111111);
        registerGenerator(generator1);

        GeneratorModel generator2 = new GeneratorModel();
        generator2.setType("SEQUENTIAL");
        generator2.setStep(1);
        generator2.setRangeStart(1111111111);
        registerGenerator(generator2);

        GeneratorModel generator3 = new GeneratorModel();
        generator3.setType("SEQUENTIAL");
        generator3.setStep(1);
        generator3.setRangeStart(1111111111);
        registerGenerator(generator3);
        setVar("GENERATOR_NAME_IN_TEST", generator1.getName(), true);
        setVar("GENERATOR_NAME_IN_TEST_2", generator2.getName(), true);
        setVar("GENERATOR_NAME_IN_TEST_3", generator3.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_HP.json"));

        EntityModel e = new EntityModel(loadFile("entity_38146.json")).post();
        assertTrue(e.isExist());
    }

    @TmsLink("RP-TC-8336")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Auto-generated IDs in Crosswalks on posting several entities in one post")
    public void runRpTc8336() throws Exception {
        GeneratorService.removeAllGenerators("SEQ_");

        GeneratorModel generator1 = new GeneratorModel();
        generator1.setType("SEQUENTIAL");
        generator1.setStep(1);
        generator1.setRangeStart(1);
        registerGenerator(generator1);
        setVar("GENERATOR_NAME_IN_TEST", generator1.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_RP-35258.json"));

        List<EntityModel> entities = EntityService.postEntities(loadFile("entities_RP-35258.json"));

        assertEquals(entities.size(), 2);
        assertNotEquals(entities.get(0).getUri(), entities.get(1).getUri());
    }

    @TmsLink("RP-TC-8422")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty and generatedValueUniqueForCrosswalk,values sent/not,crosswalk exist/not(case 2)")
    public void runRpTc8422() throws Exception {
        patternAttrNested = "Nested_[0-9a-f-]{36}";
        // Precondition
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8422.json"));

        //      Step 1
        // generateIfEmpty && generatedValueUniqueForCrosswalk=true, generateIfNotEmpty = true by default
        // value was sent = No, Value by crosswalk exists = No,
        // Reaction = Generate a value

        EntityModel e0 = new EntityModel(loadFile("entity_RP_TC_8422_1.json"));
        CrosswalkModel hmsCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsCrosswalkModel);
        CrosswalkModel hmsNpiCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsNpiCrosswalkModel);
        e0.post();
        SmartWaiting.waitForConsistency(e0);

        // Checks
        List<AttributeModel> attrListE0 = e0.getAttributes(nameAttrNested);
        assertEquals(attrListE0.size(), 1, "Amount of values");
        for (AttributeModel attr : attrListE0) {
            shouldMatchToPattern(patternAttrNested, attr.getAsNested().getAttribute("Number").getAsSimple().getValue());
        }

        //      Step 2
        // generateIfEmpty && generatedValueUniqueForCrosswalk=true, generateIfNotEmpty = true by default
        // value was sent = No,Value by crosswalk exists = Yes,
        //
        // Reaction = Reuse value

        EntityModel e1 = new EntityModel(loadFile("entity_RP_TC_8422_1.json"));
        e1.addCrosswalk(hmsCrosswalkModel);
        e1.addCrosswalk(hmsNpiCrosswalkModel);
        e1.post();
        SmartWaiting.waitForConsistency(e1);

        // Checks
        List<AttributeModel> attrListE1 = e1.getAttributes(nameAttrNested);
        assertEquals(attrListE1.size(), 1, "Amount of values");
        for (AttributeModel attr : attrListE1) {
            shouldHaveAttribute(attrListE0, attr, "Value of attribute after second POST is not as expected");
        }

        //      Step 3
        // generateIfEmpty  && generatedValueUniqueForCrosswalk=true, generateIfNotEmpty = true by default
        // value was sent = yes, Value by crosswalk exists = Yes,
        //
        // Reaction = Reuse value of License autogenerated subattributes, other subattrs that send, should be saved

        EntityModel e2 = new EntityModel(loadFile("entity_RP_TC_8422_2.json"));
        e2.addCrosswalk(hmsCrosswalkModel);
        e2.addCrosswalk(hmsNpiCrosswalkModel);
        e2.post();
        SmartWaiting.waitForConsistency(e2);

        // Checks
        List<AttributeModel> attrListE2 = e2.getAttributes(nameAttrNested);
        assertEquals(attrListE2.size(), 1, "Amount of values");
        NestedAttributeModel attributeModelE0 = (NestedAttributeModel) attrListE0.get(0);
        NestedAttributeModel attributeModelE2 = (NestedAttributeModel) attrListE2.get(0);
        Assert.assertEquals(attributeModelE0.getAttribute("State"), attributeModelE2.getAttribute("State"));
        Assert.assertEquals(attributeModelE0.getAttribute("Number"), attributeModelE2.getAttribute("Number"));
        Assert.assertEquals("RTT", attributeModelE2.getAttribute("WorkType").getAsSimple().getValue());

        //      Step 4
        // generateIfEmpty && generatedValueUniqueForCrosswalk=true,
        // value was sent = yes,Value by crosswalk exists = no,
        //
        // Reaction = Reuse value

        EntityModel e3 = new EntityModel(loadFile("entity_RP_TC_8422_3.json"));
        e3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI,
                RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        e3.post();
        SmartWaiting.waitForConsistency(e3);

        // Checks
        List<AttributeModel> attrListE3 = e3.getAttributes(nameAttrNested);
        assertEquals(attrListE3.size(), 1, "Amount of values");
        for (AttributeModel attr : attrListE3) {
            shouldMatchToPattern(patternAttrNested, attr.getAsNested().getAttribute("Number").getAsSimple().getValue());
        }
        for (AttributeModel attr3 : attrListE3) {
            for (AttributeModel attr0 : attrListE0) {
                assertNotEquals(attr0.getAsNested().getAttribute("Number"), attr3.getAsNested().getAttribute("Number"), "Generated values shouldn't equals");
            }
        }
    }

    @TmsLink("RP-TC-8417")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Shouldn't generate value for Cleanser crosswalks (generateIfEmpty/generatedValueUniqueForCrosswalk=true)(case 1)")
    public void runRpTc8417() throws Exception {
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8417.json"));

        // generateIfEmpty && generatedValueUniqueForCrosswalk=true,
        // value was sent = No, Value by crosswalk exists = No,
        // Reaction = Generate a value

        EntityModel e0 = new EntityModel(loadFile("entity_RP_TC_8422_1.json"));
        CrosswalkModel hmsCrosswalkModel = new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16));
        e0.addCrosswalk(hmsCrosswalkModel);
        e0.post();
        SmartWaiting.waitForConsistency(e0);

        assertNotNull(e0.getCrosswalkByType("ReltioCleanser"), "Entity isn't cleansed");
        List<AttributeModel> attrSimpleListE0 = e0.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE0.size(), 1, "Amount of values");
        shouldMatchToPattern(patternAttrSimple, attrSimpleListE0.get(0).getAsSimple().getValue());
        assertNotNull(e0.getAttribute(nameAttrSimple, hmsCrosswalkModel), "Attribute with HMS crosswalk is not existed");
    }

    @TmsLink("RP-TC-8418")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Shouldn't generate value for Cleanser crosswalks (generateIfEmpty/generatedValueUniqueForCrosswalk=true)(case 2)")
    public void runRpTc8418() throws Exception {
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8418.json"));

        // generateIfEmpty && generatedValueUniqueForCrosswalk=true,
        // value was sent = No, Value by crosswalk exists = No,
        // Reaction = Generate a value

        EntityModel e0 = new EntityModel(loadFile("entity_RP_TC_8418.json")).post();
        SmartWaiting.waitForConsistency(e0);
        CrosswalkModel cw = e0.getCrosswalk("configuration/sources/SITECORE_OAP_USA", "RP_TC_8418", "OAP-USA");

        assertNotNull(e0.getCrosswalkByType("ReltioCleanser"), "Entity isn't cleansed");
        List<AttributeModel> attrSimpleListE0 = e0.getAttributes(nameAttrSimple);
        assertEquals(attrSimpleListE0.size(), 1, "Amount of values");
        shouldMatchToPattern(patternAttrSimple, attrSimpleListE0.get(0).getAsSimple().getValue());
        assertNotNull(e0.getAttribute(nameAttrSimple, cw), "Attribute with HMS crosswalk is not existed");
    }

    @TmsLink("RP-TC-8261")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Generators are generating new values and not losing old values of Reltio crosswalk")
    public void runRpTc8261() throws Exception {
        final String CMDID = "CMDID";

        GeneratorModel generator_Location = new GeneratorModel();
        generator_Location.setType("SEQUENTIAL");
        generator_Location.setStep(1);
        generator_Location.setRangeStart(210000000);
        registerGenerator(generator_Location);
        setVar("GENERATOR_NAME_IN_TEST", generator_Location.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_Scholastic.json"));

        Map<String, List<AttributeModel>> crosswalksAndValues1 = new HashMap<>();
        Map<String, List<AttributeModel>> crosswalksAndValues2 = new HashMap<>();
        Map<String, List<AttributeModel>> crosswalksAndValues3 = new HashMap<>();

        EntityModel e1 = new EntityModel(loadFile("entity_8261_1.json")).post();
        SmartWaiting.waitForConsistency(e1);

        SmartWaiting.waitForValue(() -> {
            e1.refresh();
            return e1.getAttributes(CMDID).size();
        }, 1, "Number of attributes " + CMDID + " is not equal 1");

        SmartWaiting.waitForValue(() -> {
            e1.refresh();
            return e1.getCrosswalks().size();
        }, 2, "Number of crosswalks is not equal 2");

        for (CrosswalkModel cw : e1.getCrosswalks()) {
            List<AttributeModel> cmdidValues = e1.getAttributes(CMDID, cw);
            crosswalksAndValues1.put(cw.getType(), cmdidValues);
        }

        EntityModel e2 = new EntityModel(loadFile("entity_8261_2.json")).post();
        SmartWaiting.waitForConsistency(e2);

        SmartWaiting.waitForValue(() -> {
            e2.refresh();
            return e2.getAttributes(CMDID).size();
        }, 2, "Number of attributes " + CMDID + " is not equal 2");

        SmartWaiting.waitForValue(() -> {
            e2.refresh();
            return e2.getCrosswalks().size();
        }, 3, "Number of crosswalks is not equal 3");

        for (CrosswalkModel cw : e2.getCrosswalks()) {
            List<AttributeModel> cmdidValues = e2.getAttributes(CMDID, cw);
            crosswalksAndValues2.put(cw.getType(), cmdidValues);
        }

        for (Map.Entry<String, List<AttributeModel>> entry : crosswalksAndValues1.entrySet()) {
            List<String> cmdidValues1 = entry.getValue().stream().map(this::getStringValueFromSimpleAttribute).collect(Collectors.toList());
            List<String> cmdidValues2 = crosswalksAndValues2.get(entry.getKey()).stream().map(this::getStringValueFromSimpleAttribute).collect(Collectors.toList());
            assertTrue(cmdidValues2.containsAll(cmdidValues1));
            cmdidValues2.removeAll(cmdidValues1);
            assertTrue(cmdidValues2.isEmpty(), "New CMDID values appears for attribute");
        }

        EntityModel e3 = new EntityModel(loadFile("entity_8261_3.json")).post();
        SmartWaiting.waitForConsistency(e3);

        SmartWaiting.waitForValue(() -> {
            e3.refresh();
            return e3.getAttributes(CMDID).size();
        }, 3, "Number of attributes " + CMDID + " is not equal 3");

        SmartWaiting.waitForValue(() -> {
            e3.refresh();
            return e3.getCrosswalks().size();
        }, 4, "Number of crosswalks is not equal 4");

        for (CrosswalkModel cw : e3.getCrosswalks()) {
            List<AttributeModel> cmdidValues = e3.getAttributes(CMDID, cw);
            crosswalksAndValues3.put(cw.getType(), cmdidValues);
        }

        for (Map.Entry<String, List<AttributeModel>> entry : crosswalksAndValues2.entrySet()) {
            List<String> cmdidValues1 = entry.getValue().stream().map(this::getStringValueFromSimpleAttribute).collect(Collectors.toList());
            List<String> cmdidValues2 = crosswalksAndValues3.get(entry.getKey()).stream().map(this::getStringValueFromSimpleAttribute).collect(Collectors.toList());
            assertTrue(cmdidValues2.containsAll(cmdidValues1));
            cmdidValues2.removeAll(cmdidValues1);
            assertTrue(cmdidValues2.isEmpty(), "New CMDID values appears for attribute when should not");
        }
    }

    @TmsLink("RP-TC-8476")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generateIfNotEmpty = true,simple/nested attribute, Value is sent/not sent")
    public void runRpTc8476() throws Exception {
        patternAttrNested = "Nested_" + PATTERN;

        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8476.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8476_1.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        shouldMatchToPattern(patternAttrSimple, em1.getAttribute(nameAttrSimple).getAsSimple().getValue());
        shouldMatchToPattern(patternAttrNested, em1.getAttribute(nameAttrNested).getAsNested().getAttribute("Number").getAsSimple().getValue());

        //value is sent
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8476_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        shouldMatchToPattern(patternAttrSimple, em2.getAttribute(nameAttrSimple).getAsSimple().getValue());
        shouldMatchToPattern(patternAttrNested, em2.getAttribute(nameAttrNested).getAsNested().getAttribute("Number").getAsSimple().getValue());
    }

    @TmsLink("RP-TC-8478")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfNotEmpty/generatedValueUniqueForCrosswalk = true,Value is sent/not sent,One/Several crosswalks,One/Several values")
    public void runRpTc8478() throws Exception {
        String caseId = "RP_TC_8478";
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8478.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8478_1.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        assertNull(em1.getAttribute(nameAttrSimple), "FirstName is not null");

        //value is sent
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8478_1.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.addSimpleAttributeToModel("FirstName", "FirstName_" + caseId);
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        shouldMatchToPattern(patternAttrSimple, em2.getAttribute(nameAttrSimple).getAsSimple().getValue());

        //value is sent, several crosswalks
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8478_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.addSimpleAttributeToModel("FirstName", "FirstName_" + caseId);
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        List<AttributeModel> attrList = em3.getAttributes(nameAttrSimple);
        assertEquals(attrList.size(), 1, "Amount of values");
        attrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });

        //several values,several crosswalks
        EntityModel em4 = new EntityModel(loadFile("entity_RP_TC_8478_1.json"));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.addSimpleAttributeToModel("FirstName", "FirstName_1" + caseId);
        em4.addSimpleAttributeToModel("FirstName", "FirstName_2" + caseId);
        em4.post();
        SmartWaiting.waitForConsistency(em4);

        attrList = em4.getAttributes(nameAttrSimple);
        assertEquals(attrList.size(), 2, "Amount of values");
        attrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
    }

    @TmsLink("RP-TC-8490")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/uniqueGeneratedValuePerEntityPerRequest = true,Value is sent/not sent,One/Several values,Simple/Nested attribute/Crosswalk")
    public void runRpTc8490() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8490.json"));

        //value is not sent
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        List<AttributeModel> simpleAttrList = em2.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em2.getAttributes(nameAttrNested);
        List<CrosswalkModel> cw = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //value is sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_3.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        simpleAttrList = em1.getAttributes(nameAttrSimple);
        nestedAttrList = em1.getAttributes(nameAttrNested);
        cw = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue2 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue2, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue2, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //several values are sent
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8490_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        simpleAttrList = em3.getAttributes(nameAttrSimple);
        nestedAttrList = em3.getAttributes(nameAttrNested);
        cw = em3.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue3 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue3, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue3, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");
    }

    @TmsLink("RP-TC-8491")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfNotEmpty/uniqueGeneratedValuePerEntityPerRequest = true,One/Several values,Simple/Nested attribute/Crosswalk")
    public void runRpTc8491() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8491.json"));

        //value is sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8491_1.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cw = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //several values are sent
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8491_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        simpleAttrList = em2.getAttributes(nameAttrSimple);
        nestedAttrList = em2.getAttributes(nameAttrNested);
        cw = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue2 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue2, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue2, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");
    }

    @TmsLink("RP-TC-8492")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generatedValueUniqueForCrosswalk/uniqueGeneratedValuePerEntityPerRequest = true,Value is sent/not sent,One/Several values,Simple/Nested attribute/Crosswalk")
    public void runRpTc8492() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8492.json"));

        //value is not sent
        EntityModel em0 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em0.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em0.post();
        SmartWaiting.waitForConsistency(em0);

        assertNull(em0.getAttribute(nameAttrSimple));
        assertNull(em0.getAttribute(nameAttrNested));

        //value is sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8491_1.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cwList.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //value is sent, several crosswalks
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8491_1.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        simpleAttrList = em2.getAttributes(nameAttrSimple);
        nestedAttrList = em2.getAttributes(nameAttrNested);
        cwList = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue2 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue2, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        cwList.forEach(cw ->
                assertEquals(generatedValue2, cw.getValue().substring("Crosswalk_".length()), "Generated value for crosswalk")
        );

        //Several values are sent, one crosswalks
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8491_2.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        simpleAttrList = em3.getAttributes(nameAttrSimple);
        nestedAttrList = em3.getAttributes(nameAttrNested);
        cwList = em3.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(1).getAsSimple().getValue());
        String generatedValue3 = simpleAttrList.get(1).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue3, nestedAttrList.get(1).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue3, cwList.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //Several values are sent, several crosswalks
        EntityModel em4 = new EntityModel(loadFile("entity_RP_TC_8491_2.json"));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.post();
        SmartWaiting.waitForConsistency(em4);

        simpleAttrList = em4.getAttributes(nameAttrSimple);
        nestedAttrList = em4.getAttributes(nameAttrNested);
        cwList = em4.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue4 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue4, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        cwList.forEach(cw ->
                assertEquals(generatedValue4, cw.getValue().substring("Crosswalk_".length()), "Generated value for crosswalk")
        );
    }

    @TmsLink("RP-TC-8493")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generateIfNotEmpty/generatedValueUniqueForCrosswalk = true,Value is sent/not sent,One/Several values/crosswalks,Simple attribute")
    public void runRpTc8493() throws Exception {
        String caseId = "RP_TC_8493";

        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8493.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());

        //value is not sent, sseveral crosswalks
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);
        simpleAttrList = em2.getAttributes(nameAttrSimple);

        assertEquals(simpleAttrList.size(), 2, "Amount of FirstName's values");
        assertEquals(em2.getCrosswalks().size(), 2, "Amount of crosswalks");
        simpleAttrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });

        //values is sent, one crosswalks
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.addSimpleAttributeToModel("FirstName", caseId + "_FirstName");
        em3.post();
        SmartWaiting.waitForConsistency(em3);
        simpleAttrList = em3.getAttributes(nameAttrSimple);

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());

        //Several values are sent, one crosswalk
        EntityModel em4 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.addSimpleAttributeToModel("FirstName", caseId + "_FirstName1");
        em4.addSimpleAttributeToModel("FirstName", caseId + "_FirstName2");
        em4.post();
        SmartWaiting.waitForConsistency(em4);
        simpleAttrList = em4.getAttributes(nameAttrSimple);

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(em4.getCrosswalks().size(), 1, "Amount of crosswalks");
        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());

        //value is not sent, sseveral crosswalks
        EntityModel em5 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em5.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em5.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em5.addSimpleAttributeToModel("FirstName", caseId + "_FirstName3");
        em5.addSimpleAttributeToModel("FirstName", caseId + "_FirstName4");
        em5.post();
        SmartWaiting.waitForConsistency(em5);
        simpleAttrList = em2.getAttributes(nameAttrSimple);

        assertEquals(simpleAttrList.size(), 2, "Amount of FirstName's values");
        assertEquals(em5.getCrosswalks().size(), 2, "Amount of crosswalks");
        simpleAttrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
    }

    @TmsLink("RP-TC-8494")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generateIfNotEmpty/uniqueGeneratedValuePerEntityPerRequest = true,Value is sent/not sent,One/Several values,Simple/Nested attribute/Crosswalk")
    public void runRpTc8494() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8494.json"));

        //value is not sent
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        List<AttributeModel> simpleAttrList = em2.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em2.getAttributes(nameAttrNested);
        List<CrosswalkModel> cw = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //value is sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_3.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        simpleAttrList = em1.getAttributes(nameAttrSimple);
        nestedAttrList = em1.getAttributes(nameAttrNested);
        cw = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue2 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue2, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue2, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //several values are sent
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8490_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        simpleAttrList = em3.getAttributes(nameAttrSimple);
        nestedAttrList = em3.getAttributes(nameAttrNested);
        cw = em3.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cw.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue3 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue3, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue3, cw.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");
    }

    @TmsLink("RP-TC-8495")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generatedValueUniqueForCrosswalk/uniqueGeneratedValuePerEntityPerRequest = true,Value is not sent,Simple/Nested attribute/Crosswalk")
    public void runRpTc8495() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8495.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cwList.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //value is not sent, several crosswalks
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        simpleAttrList = em2.getAttributes(nameAttrSimple);
        nestedAttrList = em2.getAttributes(nameAttrNested);
        cwList = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue2 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue2, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        cwList.forEach(cw ->
                assertEquals(generatedValue2, cw.getValue().substring("Crosswalk_".length()), "Generated value for crosswalk")
        );
    }

    @TmsLink("RP-TC-8496")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generateIfNotEmpty/generatedValueUniqueForCrosswalk/uniqueGeneratedValuePerEntityPerRequest = true,Value is sent/not sent,One/Several values,Simple/Nested attribute/Crosswalk")
    public void runRpTc8496() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8496.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cwList.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //value is not sent, several crosswalks
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        simpleAttrList = em2.getAttributes(nameAttrSimple);
        nestedAttrList = em2.getAttributes(nameAttrNested);
        cwList = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue2 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue2, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        cwList.forEach(cw ->
                assertEquals(generatedValue2, cw.getValue().substring("Crosswalk_".length()), "Generated value for crosswalk")
        );

        //value is sent
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8491_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        simpleAttrList = em3.getAttributes(nameAttrSimple);
        nestedAttrList = em3.getAttributes(nameAttrNested);
        cwList = em3.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cwList.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //value is sent, several crosswalks
        EntityModel em4 = new EntityModel(loadFile("entity_RP_TC_8491_1.json"));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.post();
        SmartWaiting.waitForConsistency(em4);

        simpleAttrList = em4.getAttributes(nameAttrSimple);
        nestedAttrList = em4.getAttributes(nameAttrNested);
        cwList = em4.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue3 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue3, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        cwList.forEach(cw ->
                assertEquals(generatedValue3, cw.getValue().substring("Crosswalk_".length()), "Generated value for crosswalk")
        );

        //Several values are sent, one crosswalks
        EntityModel em5 = new EntityModel(loadFile("entity_RP_TC_8491_2.json"));
        em5.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em5.post();
        SmartWaiting.waitForConsistency(em5);

        simpleAttrList = em5.getAttributes(nameAttrSimple);
        nestedAttrList = em5.getAttributes(nameAttrNested);
        cwList = em5.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(1).getAsSimple().getValue());
        String generatedValue4 = simpleAttrList.get(1).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue4, nestedAttrList.get(1).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue4, cwList.get(0).getValue().substring("Crosswalk_".length()), "Generated value for crosswalk");

        //Several values are sent, several crosswalks
        EntityModel em6 = new EntityModel(loadFile("entity_RP_TC_8491_2.json"));
        em6.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em6.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em6.post();
        SmartWaiting.waitForConsistency(em6);

        simpleAttrList = em6.getAttributes(nameAttrSimple);
        nestedAttrList = em6.getAttributes(nameAttrNested);
        cwList = em6.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue5 = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue5, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        cwList.forEach(cw ->
                assertEquals(generatedValue5, cw.getValue().substring("Crosswalk_".length()), "Generated value for crosswalk")
        );
    }

    @TmsLink("RP-TC-8497")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfNotEmpty=false,generatedValueUniqueForCrosswalk=true,Value is sent/not sent,One/Several crosswalks,One/Several values")
    public void runRpTc8497() throws Exception {
        String caseId = "RP_TC_8497";
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8497.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        assertNull(em1.getAttribute(nameAttrSimple), "FirstName is not null");
        assertNull(em1.getAttribute(nameAttrNested), "License is not null");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());

        //value is sent
        setVar("firstname", "FirstName_" + caseId, true);
        setVar("number", "Number_" + caseId, true);
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8497_1.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        assertEquals("FirstName_" + caseId, em2.getAttribute(nameAttrSimple).getAsSimple().getValue(), "Value of FirstName");
        assertEquals("Number_" + caseId, em2.getAttribute(nameAttrNested).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Value of License/Number");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());

        //value is sent, several crosswalks
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8497_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        List<AttributeModel> simpleAttrList = em3.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em3.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em3.getCrosswalks();
        assertEquals(simpleAttrList.size(), 1, "Amount of values");
        assertEquals(nestedAttrList.size(), 1, "Amount of values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");
        assertEquals("FirstName_" + caseId, simpleAttrList.get(0).getAsSimple().getValue(), "Value of FirstName");
        assertEquals("Number_" + caseId, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Value of License/Number");
        cwList.forEach(cw -> assertTrue(isMatchToPattern(patternCrosswalk, cw.getValue()), "Value of crosswalk is not as expected"));

        //several values,several crosswalks
        setVar("firstname1", "FirstName_1" + caseId, true);
        setVar("firstname2", "FirstName_2" + caseId, true);
        setVar("number1", "Number_1" + caseId, true);
        setVar("number2", "Number_2" + caseId, true);
        EntityModel em4 = new EntityModel(loadFile("entity_RP_TC_8497_2.json"));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em4.post();
        SmartWaiting.waitForConsistency(em4);

        simpleAttrList = em4.getAttributes(nameAttrSimple);
        nestedAttrList = em4.getAttributes(nameAttrNested);
        cwList = em4.getCrosswalks();
        assertEquals(simpleAttrList.size(), 2, "Amount of values FirstName");
        assertEquals(nestedAttrList.size(), 2, "Amount of values License");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");
        simpleAttrList.forEach(attr -> {
            try {
                assertTrue(attr.getAsSimple().getValue().startsWith("FirstName"), "Value of FirstName is not as expected");
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
        nestedAttrList.forEach(attr -> {
            try {
                assertTrue(attr.getAsNested().getAttribute("Number").getAsSimple().getValue().startsWith("Number"), "Value of License is not as expected");
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
        cwList.forEach(cw -> assertTrue(isMatchToPattern(patternCrosswalk, cw.getValue()), "Value of crosswalk is not as expected"));
    }

    @TmsLink("RP-TC-8498")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfNotEmpty=false,uniqueGeneratedValuePerEntityPerRequest=true,Value is sent/not sent")
    public void runRpTc8498() throws Exception {
        String caseId = "RP_TC_8498";
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8498.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        assertNull(em1.getAttribute(nameAttrSimple), "FirstName is not null");
        assertNull(em1.getAttribute(nameAttrNested), "License is not null");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());

        //value is sent
        setVar("firstname", "FirstName_" + caseId, true);
        setVar("number", "Number_" + caseId, true);
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8497_1.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        assertEquals("FirstName_" + caseId, em2.getAttribute(nameAttrSimple).getAsSimple().getValue(), "Value of FirstName");
        assertEquals("Number_" + caseId, em2.getAttribute(nameAttrNested).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Value of License/Number");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());
    }

    @TmsLink("RP-TC-8499")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfNotEmpty=false,generateIfEmpty/uniqueGeneratedValuePerEntityPerRequest=true,Value is sent/not sent")
    public void runRpTc8499() throws Exception {
        String caseId = "RP_TC_8499";
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8499.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        String generatedValue = simpleAttrList.get(0).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue().substring("Nested_".length()), "Generated value for nested attribute");
        assertEquals(generatedValue, cwList.get(0).getValue().substring("Source_".length()), "Generated value for crosswalk");

        //value is sent
        setVar("firstname", "FirstName_" + caseId, true);
        setVar("number", "Number_" + caseId, true);
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8497_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        assertEquals("FirstName_" + caseId, em3.getAttribute(nameAttrSimple).getAsSimple().getValue(), "Value of FirstName");
        assertEquals("Number_" + caseId, em3.getAttribute(nameAttrNested).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Value of License/Number");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());
    }

    @TmsLink("RP-TC-8501")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfNotEmpty=false,generateIfEmpty/generatedValueUniqueForCrosswalk=true,Value is sent/not sent,One/Several crosswalks")
    public void runRpTc8501() throws Exception {
        String caseId = "RP_TC_8501";
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8501.json"));

        //value is not sent
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        shouldMatchToPattern(patternAttrSimple, simpleAttrList.get(0).getAsSimple().getValue());
        shouldMatchToPattern(patternAttrNested, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue());
        shouldMatchToPattern(patternCrosswalk, cwList.get(0).getValue());

        //value is not sent, sseveral crosswalks
        EntityModel em2 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em2.post();
        SmartWaiting.waitForConsistency(em2);

        simpleAttrList = em2.getAttributes(nameAttrSimple);
        nestedAttrList = em2.getAttributes(nameAttrNested);
        cwList = em2.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 2, "Amount of crosswalks");

        simpleAttrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
        nestedAttrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrNested, attr.getAsNested().getAttribute("Number").getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
        cwList.forEach(cw -> {
            try {
                shouldMatchToPattern(patternCrosswalk, cw.getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });

        //value is sent
        setVar("firstname", "FirstName_" + caseId, true);
        setVar("number", "Number_" + caseId, true);
        EntityModel em3 = new EntityModel(loadFile("entity_RP_TC_8497_1.json"));
        em3.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em3.post();
        SmartWaiting.waitForConsistency(em3);

        assertEquals("FirstName_" + caseId, em3.getAttribute(nameAttrSimple).getAsSimple().getValue(), "Value of FirstName");
        assertEquals("Number_" + caseId, em3.getAttribute(nameAttrNested).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Value of License/Number");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());
    }

    @TmsLink("RP-TC-8502")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "uniqueGeneratedValuePerEntityPerRequest=true,Several crosswalks")
    public void runRpTc8502() throws Exception {
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8502.json"));

        //two crosswalk with the same type
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        assertEquals(em1.getCrosswalks().size(), 1, "Amount of crosswalks is incorrect");
        shouldMatchToPattern(patternCrosswalk, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());
    }

    @TmsLink("RP-TC-8503")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "uniqueGeneratedValuePerEntityPerRequest=true,Generated crosswalk in reference")
    public void runRpTc8503() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8503.json"));

        //two crosswalk with the same type
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8503.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        assertEquals(em1.getCrosswalks().size(), 1, "Amount of crosswalks");
        assertEquals(em1.getAttributes(nameAttrSimple).size(), 1, "Amount of FirstName's value");
        shouldMatchToPattern(patternAttrSimple, em1.getAttribute(nameAttrSimple).getAsSimple().getValue());
        String generatedValue = em1.getAttribute(nameAttrSimple).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(generatedValue, em1.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue().substring("Source_".length()), "Generated value for crosswalk");

        EntityModel loc = new EntityModel(em1.getAllRefEntities().get(0).getObjectURI()).get();
        assertEquals(loc.getCrosswalks().size(), 1, "Amount of crosswalks");
        shouldMatchToPattern(patternCrosswalk, loc.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());

        RelationModel rel = new RelationModel(em1.getAllRefRelations().get(0).getObjectURI()).get();
        assertEquals(rel.getCrosswalks().size(), 1, "Amount of crosswalks");
        shouldMatchToPattern(patternCrosswalk, rel.getCrosswalkByType(CROSSWALK_TYPE_HMS).getValue());
    }

    @TmsLink("RP-TC-8504")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generatedValueUniqueForCrosswalk=true,Several crosswalks,Simple")
    public void runRpTc8504() throws Exception {
        addRandomGenerator("GENERATOR_NAME_IN_TEST");
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8504.json"));

        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS_NPI, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> attrList = em1.getAttributes(nameAttrSimple);
        List<CrosswalkModel> cwList = em1.getCrosswalks();
        assertEquals(cwList.size(), 2, "Amount of crosswalks is incorrect");
        assertEquals(attrList.size(), 1, "Amount of FirstName's is incorrect");

        attrList.forEach(attr -> {
            try {
                shouldMatchToPattern(patternAttrSimple, attr.getAsSimple().getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
        cwList.forEach(cw -> {
            try {
                shouldMatchToPattern(patternCrosswalk, cw.getValue());
            } catch (Exception e) {
                fail(e.getMessage());
            }
        });
    }

    @TmsLink("RP-TC-8505")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "generateIfEmpty/generatedValueUniqueForCrosswalk=true, Nested of reference")
    public void runRpTc8505() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8505.json"));

        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8503.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        AttributeModel nestedAttr = em1.getAttribute("Address/Unit");
        assertNotNull(nestedAttr, "Attribute Address/Unit doesn't exist");
        shouldMatchToPattern("RefNested_" + PATTERN, nestedAttr.getAsNested().getAttribute("UnitName").getAsSimple().getValue());
    }

    private String getStringValueFromSimpleAttribute(AttributeModel attribute) {
        try {
            return attribute.getAsSimple().getValue();
        } catch (ReltioObjectException e) {
            throw new AssertionError("An error appears on retrieving simple value from attribute");
        }
    }

    @TmsLink("RP-TC-8500")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Generators in entity with non data provider crosswalks")
    public void runRpTc8500() throws Exception {
        String entityType = "configuration/entityTypes/Object";
        String sourceType = "configuration/sources/Normal";

        GeneratorModel generator = new GeneratorModel();
        generator.setType("UUID");
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("L3_RP-39410.json"));

        CrosswalkModel cwNonDataProvider = new CrosswalkModel(sourceType, RandomGenerator.getInstance().getRandomString(10));
        cwNonDataProvider.setDataProvider(false);
        EntityModel eNonDataProvider = new EntityModel(cwNonDataProvider);
        eNonDataProvider.setType(entityType);
        eNonDataProvider.addSimpleAttributeToModel("Name", RandomGenerator.getInstance().getRandomString(5));
        eNonDataProvider.post();

        assertTrue(eNonDataProvider.get().isExist());
        assertTrue(eNonDataProvider.getAttributes().isEmpty());

        CrosswalkModel cw1 = new CrosswalkModel(sourceType, RandomGenerator.getInstance().getRandomString(10));
        EntityModel e1 = new EntityModel(cw1);
        e1.setType(entityType);
        e1.addSimpleAttributeToModel("Name", RandomGenerator.getInstance().getRandomString(5));
        e1.post();

        CrosswalkModel cw2 = new CrosswalkModel(sourceType, RandomGenerator.getInstance().getRandomString(10));
        cw1.setDataProvider(false);
        EntityModel e2 = new EntityModel(entityType);
        e2.addCrosswalks(Arrays.asList(cw1, cw2));
        e2.addSimpleAttributeToModel("Name", RandomGenerator.getInstance().getRandomString(5));
        e2.post();

        assertTrue(e1.get().isExist());
        assertEquals(e1.get().getUri(), e2.get().getUri());
        assertTrue(e1.getAttributes().containsKey("AutoGeneratedSimple"));
        assertTrue(e1.getAttributes().containsKey("AutoGeneratedNested"));
        assertTrue(e1.getAttributes().containsKey("Name"));
        assertEquals(e1.getAttributes("Name").size(), 2);
        assertEquals(e1.getAttributes("AutoGeneratedSimple").size(), 2);
        assertEquals(e1.getAttributes("AutoGeneratedNested").size(), 2);
    }

    @TmsLink("RP-TC-8756")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "SEQUENTIAL generator with very large rangeStart value.")
    public void runRpTc8756() throws Exception {
        Long rangeStart = 4500000000000000000L;

        GeneratorModel generator = new GeneratorModel();
        generator.setType("SEQUENTIAL");
        generator.setStep(1);
        generator.setRangeStart(rangeStart);
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);

        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8756.json"));

        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + 1));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (rangeStart + 2));
    }

    @TmsLink("RP-TC-8757")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "SEQUENTIAL generator with very large step value.")
    public void runRpTc8757() throws Exception {
        Long step = 922337203685477580L;

        GeneratorModel generator = new GeneratorModel();
        generator.setType("SEQUENTIAL");
        generator.setStep(step);
        generator.setRangeStart(1);
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);

        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8756.json"));

        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        assertEquals(cwList.get(0).getValue(), "Source_" + 1);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (1 + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (1 + 2 * step));
    }

    @TmsLink("RP-TC-8760")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Several generated attributes with very long(max permitted length) value")
    public void runRpTc8760() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);

        String veryLongString = RandomGenerator.getInstance().getRandomAlphabeticString(TenantManagementService.getTenant().getObjectSizeLimits().getMaxAttributeValueSize() - 37);
        setVar("very_long_string", veryLongString, true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_8760.json"));

        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_8490_2.json"));
        em1.addCrosswalk(new CrosswalkModel(CROSSWALK_TYPE_HMS, RandomGenerator.getInstance().getRandomAlphabeticString(16)));
        em1.post();
        SmartWaiting.waitForConsistency(em1);

        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        simpleAttrList.addAll(em1.getAttributes("SuffixName"));
        simpleAttrList.addAll(em1.getAttributes("PreferredName"));
        simpleAttrList.addAll(em1.getAttributes("ProfDesignation"));
        simpleAttrList.addAll(em1.getAttributes("Nickname"));
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        for (AttributeModel attr : simpleAttrList) {
            shouldMatchToPattern(veryLongString + "_" + PATTERN, attr.getAsSimple().getValue());
        }
        shouldMatchToPattern(veryLongString + "_" + PATTERN, nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue());
        shouldMatchToPattern(patternCrosswalk, cwList.get(0).getValue());
    }

    @TmsLink("RP-TC-5805")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Get Generator Value by Generator Name")
    public void runRpTc5805() throws Exception {
        GeneratorModel uuidGenModel = new GeneratorModel();
        GeneratorService.registerGenerator(uuidGenModel);
        GeneratorModel seqGenModel = new GeneratorModel();
        GeneratorService.registerGenerator(seqGenModel
                .setType("SEQUENTIAL")
                .setStep(1)
                .setRangeStart(100)
        );
        String uuidGen = uuidGenModel.getName();
        int genValue = Integer.parseInt(GeneratorService.generateNextValue(seqGenModel.getName()));
        log.info(GeneratorService.generateNextValue(uuidGenModel));
        log.info(genValue);
        assertNotNull(GeneratorService.generateNextValue(uuidGenModel), "Issue with generating uuid using uuid generator");
        assertEquals(genValue, 100);
    }

    @TmsLink("RP-TC-5806")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Delete Generator By Name.")
    public void runRpTc5806() throws Exception {
        GeneratorModel uuidGenModel = new GeneratorModel();
        GeneratorService.registerGenerator(uuidGenModel);
        String uuidGen = uuidGenModel.getName();
        assertNotNull(GeneratorService.generateNextValue(uuidGen), "Issue with generating uuid using uuid generator");
        GeneratorService.removeGenerator(uuidGen);
        assertFalse(GeneratorService.isGeneratorExistByName(uuidGen), "Generator exists even after deleting it");
        Exception e = expectThrows(Exception.class, () -> GeneratorService.removeGenerator("xqwads"));
        assertTrue(e.getMessage().contains("Generator not found"), e.getMessage());

    }

    @TmsLink("RP-TC-9031")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Generate different sub-attribute's values of several reference attributes with surrogate crosswalks")
    public void runRpTc9031() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9031.json"));
        EntityModel em = new EntityModel(loadFile("entity_RP_TC_9031.json"));
        AttributeModel attr = em.getAttribute("Address");
        em.removeAttributeFromModelByPath("Address");
        em.addAttributeToModel("Address", attr);
        em.post();
        SmartWaiting.waitForConsistency(em);

        em = new EntityModel(loadFile("entity_RP_TC_9031.json")).post();
        List<AttributeModel> attrs = em.getAttributes("Address");
        assertNotEquals(attrs.get(0).getAsReference().getAttribute("AddressMDMID").getAsSimple().getValue(), attrs.get(1).getAsReference().getAttribute("AddressMDMID").getAsSimple().getValue(), "Generated values are equal");
        assertNotEquals(attrs.get(0).getAsReference().getAttribute("AddressMDMID").getAsSimple().getValue(), attrs.get(2).getAsReference().getAttribute("AddressMDMID").getAsSimple().getValue(), "Generated values are equal");
        assertNotEquals(attrs.get(1).getAsReference().getAttribute("AddressMDMID").getAsSimple().getValue(), attrs.get(2).getAsReference().getAttribute("AddressMDMID").getAsSimple().getValue(), "Generated values are equal");
    }

    @TmsLink("RP-TC-9383")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Deep nested: generateIfNotEmpty=false,generateIfEmpty/uniqueGeneratedValuePerEntityPerRequest=true,Value is sent/not sent")
    public void runRpTc9383() throws Exception {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("deepNesting_9383.json"));
        String prefix = "NestedTest";
        int nesting = 5;
        int middle = 3;
        String simple = "String";
        String sentValue = RandomGenerator.getInstance().getRandomString(5);

        //the deepest value is sent
        EntityModel em = new EntityModel(ENTITY_TYPE_PRODUCT);
        em.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(nesting, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em.post();

        String generatedValue = em.getAttribute(simple).getAsSimple().getValue().substring("Simple_".length());
        for (int i = 1; i < nesting; i++) {
            assertEquals(em.getAttributes(NestedAttributes.getSimplePath(i, prefix, simple)).size(), 1);
            assertEquals(em.getAttribute(NestedAttributes.getSimplePath(i, prefix, simple)).getAsSimple().getValue().substring("Nested_".length()), generatedValue);
        }
        assertEquals(em.getAttribute(NestedAttributes.getSimplePath(nesting, prefix, simple)).getAsSimple().getValue(), sentValue);

        //the deepest value is not sent
        EntityModel em1 = new EntityModel(ENTITY_TYPE_PRODUCT);
        em1.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(middle, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em1.post();

        assertNotEquals(generatedValue, em1.getAttribute(simple).getAsSimple().getValue().substring("Simple_".length()));
        generatedValue = em1.getAttribute(simple).getAsSimple().getValue().substring("Simple_".length());
        assertEquals(em1.getAttribute(NestedAttributes.getSimplePath(nesting, prefix, simple)).getAsSimple().getValue().substring("Nested_".length()), generatedValue);
        assertEquals(em1.getAttribute(NestedAttributes.getSimplePath(middle, prefix, simple)).getAsSimple().getValue(), sentValue);
    }

    @TmsLink("RP-TC-9384")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Deep nested: generateIfEmpty=true,Value is sent/not sent")
    public void runRpTc9384() throws Exception {
        GeneratorModel generatorModel = new GeneratorModel();
        generatorModel.setType("SEQUENTIAL");
        generatorModel.setRangeStart(10);
        generatorModel.setStep(1);
        registerGenerator(generatorModel);
        setVar("GENERATOR_NAME_IN_TEST", generatorModel.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("deepNesting_9384.json"));
        String prefix = "NestedTest";
        int nesting = 5;
        int middle = 3;
        String simple = "String";
        String sentValue = RandomGenerator.getInstance().getRandomString(5);

        //the deepest value is sent
        EntityModel em = new EntityModel(ENTITY_TYPE_PRODUCT);
        em.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(nesting, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em.post();

        checkAttributes9384(em, prefix, nesting, simple, sentValue);

        //middle value is sent, while the deepest isn't
        EntityModel em1 = new EntityModel(ENTITY_TYPE_PRODUCT);
        em1.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(middle, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em1.post();

        checkAttributes9384(em1, prefix, nesting, simple, sentValue);
    }

    @TmsLink("RP-TC-9385")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Deep nested: generatedValueUniqueForCrosswalk = true, Value is exists/not exists")
    public void runRpTc9385() throws Exception {
        BusinessConfigurationService.updateConfig(loadFile("deepNesting_9385.json"));
        String prefix = "NestedTest";
        int nesting = 5;
        int middle = 3;
        String simple = "String";
        String sentValue = RandomGenerator.getInstance().getRandomString(5);

        //the deepest value is sent
        EntityModel em = new EntityModel(ENTITY_TYPE_PRODUCT);
        em.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(nesting, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em.post();
        for (int i = 1; i < nesting; i++) {
            String simplePath = NestedAttributes.getSimplePath(i, prefix, simple);
            assertEquals(em.getAttributes(simplePath).size(), 1, simplePath);
        }
        String simplePath = NestedAttributes.getSimplePath(nesting, prefix, simple);
        assertNotEquals(em.getAttribute(simplePath).getAsSimple().getValue(), sentValue, simplePath);
        shouldMatchToPattern(patternAttrNested, em.getAttribute(NestedAttributes.getSimplePath(nesting, prefix, simple)).getAsSimple().getValue());

        //middle value is sent, while the deepest isn't
        EntityModel em1 = new EntityModel(ENTITY_TYPE_PRODUCT);
        em1.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(middle, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em1.post();
        for (int i = 1; i < middle; i++) {
            String simplePath2 = NestedAttributes.getSimplePath(i, prefix, simple);
            assertEquals(em1.getAttributes(simplePath2).size(), 1);
        }
        assertNotEquals(em1.getAttribute(NestedAttributes.getSimplePath(middle, prefix, simple)).getAsSimple().getValue(), sentValue);
        shouldMatchToPattern(patternAttrNested, em1.getAttribute(NestedAttributes.getSimplePath(middle, prefix, simple)).getAsSimple().getValue());
    }

    @TmsLink("RP-TC-9387")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Deep nested: generateIfNotEmpty=false, Value is sent/not sent")
    public void runRpTc9387() throws Exception {
        GeneratorModel generatorModel = new GeneratorModel();
        generatorModel.setType("SEQUENTIAL");
        generatorModel.setRangeStart(10);
        generatorModel.setStep(1);
        registerGenerator(generatorModel);
        setVar("GENERATOR_NAME_IN_TEST", generatorModel.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("deepNesting_9387.json"));
        String prefix = "NestedTest";
        int nesting = 5;
        int middle = 3;
        String simple = "String";
        String sentValue = RandomGenerator.getInstance().getRandomString(5);

        //the deepest value is sent
        EntityModel em = new EntityModel(ENTITY_TYPE_PRODUCT);
        em.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(nesting, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em.post();

        for (int i = 1; i < nesting; i++) {
            assertEquals(em.getAttributes(NestedAttributes.getSimplePath(i, prefix, simple)).size(), 0);
        }
        assertEquals(em.getAttribute(NestedAttributes.getSimplePath(nesting, prefix, simple)).getAsSimple().getValue(), sentValue);

        //middle value is sent, while the deepest isn't
        EntityModel em1 = new EntityModel(ENTITY_TYPE_PRODUCT);
        em1.addAttributeToModel("NestedTest1", NestedAttributes.generateDeepNested(middle, prefix, new AbstractMap.SimpleEntry<>(simple, new SimpleAttributeModel(sentValue))));
        em1.post();

        for (int i = 1; i < middle; i++) {
            assertEquals(em1.getAttributes(NestedAttributes.getSimplePath(i, prefix, simple)).size(), 0);
        }
        assertEquals(em1.getAttribute(NestedAttributes.getSimplePath(middle, prefix, simple)).getValue(), sentValue);
    }

    @TmsLink("RP-TC-9674")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "SEQUENTIAL generator with rangeEnd. overflowConfig is missed")
    public void runRpTc9674() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 11L;
        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(1)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9675", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + 1));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (rangeStart + 2));
    }

    @TmsLink("RP-TC-9675")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "SEQUENTIAL generator with overflowConfig and rangeEnd")
    public void runRpTc9675() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 11L;
        Long overflowRangeStart = 20L;
        Long step = 1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9675", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + overflowRangeStart);
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart + step));
    }

    @TmsLink("RP-TC-9676")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "SEQUENTIAL generator with very large overflowRangeStart")
    public void runRpTc9676() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 11L;
        Long overflowRangeStart = 4500000000000000000L;
        Long step = 1L;
        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9676", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + overflowRangeStart);
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart + step));
    }

    @TmsLink("RP-TC-9677")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Sum of Current SEQ ID and step is more than rangeEnd")
    public void runRpTc9677() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 15L;
        Long overflowRangeStart = 20L;
        Long step = 6L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9677", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (overflowRangeStart));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart + step));
    }

    @TmsLink("RP-TC-9679")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "rangeEnd is missed. overflowConfig is specified. rangeStart is 9223372036854775807L")
    public void runRpTc9679() throws Exception {
        Long rangeStart = 4500000000000000000L;
        Long overflowRangeStart = 20L;
        Long step = 1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9679", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");

        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (rangeStart + 2 * step));
    }

    @TmsLink("RP-TC-9680")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Generated value exceeds rangeEnd by update attibute")
    public void runRpTc9680() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 13L;
        Long overflowRangeStart = 20L;
        Long step = 1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9680", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();

        CrosswalkModel cw = new CrosswalkModel(CROSSWALK_TYPE_HMS, "new_crosswalk");
        em1.getAttribute(nameAttrSimple).update("New value", cw);
        em1.refresh();

        assertEquals(em1.getAttributes(nameAttrSimple).size(), 2, "Amount of FirstName's values");
        assertEquals(em1.getAttribute(nameAttrSimple, cw).getAsSimple().getValue(), "Simple_" + (overflowRangeStart));
    }

    @TmsLink("RP-TC-9681")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Negative step with rangeEnd and overflowConfig")
    public void runRpTc9681() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 8L;
        Long overflowRangeStart = 0L;
        Long step = -1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9681", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart));
    }

    @TmsLink("RP-TC-9682")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "overflowRangeStart equals rangeStart")
    public void runRpTc9682() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 12L;
        Long overflowRangeStart = 10L;
        Long step = 1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9682", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart));
    }

    @TmsLink("RP-TC-9683")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "rangeEnd equals to rangeStart")
    public void runRpTc9683() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 10L;
        Long overflowRangeStart = 20L;
        Long step = 1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        setVar("CASE_ID", "RP_TC_9683", true);
        EntityModel em1 = new EntityModel(loadFile("entity_RP_TC_9674.json")).post();
        List<AttributeModel> simpleAttrList = em1.getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = em1.getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = em1.getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + overflowRangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (overflowRangeStart + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart + 2 * step));
    }

    @TmsLink("RP-TC-9684")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Post several entities, so generated value exceeds rangeEnd")
    public void runRpTc9684() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 15L;
        Long overflowRangeStart = 40L;
        Long step = 1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        List<EntityModel> ems = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            setVar("CASE_ID", "RP_TC_9684_" + i, true);
            ems.add(new EntityModel(loadFile("entity_RP_TC_9674.json")).post());
        }
        List<AttributeModel> simpleAttrList = ems.get(0).getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = ems.get(0).getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = ems.get(0).getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (rangeStart + 2 * step));

        simpleAttrList = ems.get(1).getAttributes(nameAttrSimple);
        nestedAttrList = ems.get(1).getAttributes(nameAttrNested);
        cwList = ems.get(1).getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + (rangeStart + 3 * step));
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + 4 * step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart));

        simpleAttrList = ems.get(2).getAttributes(nameAttrSimple);
        nestedAttrList = ems.get(2).getAttributes(nameAttrNested);
        cwList = ems.get(2).getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + (overflowRangeStart + step));
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (overflowRangeStart + 2 * step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart + 3 * step));
    }

    @TmsLink("RP-TC-9685")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Post several entities, generated value exceeds rangeEnd, step is negative")
    public void runRpTc9685() throws Exception {
        Long rangeStart = 10L;
        Long rangeEnd = 5L;
        Long overflowRangeStart = -10L;
        Long step = -1L;

        GeneratorModel generator = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setStep(step)
                .setRangeStart(rangeStart)
                .setRangeEnd(rangeEnd)
                .setOverflowRangeStart(overflowRangeStart);
        GeneratorService.removeGenerator(generator);
        GeneratorService.registerGenerator(generator);
        setVar("GENERATOR_NAME_IN_TEST", generator.getName(), true);
        BusinessConfigurationService.updateConfig(loadFile("config_RP_TC_9674.json"));

        List<EntityModel> ems = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            setVar("CASE_ID", "RP_TC_9685_" + i, true);
            ems.add(new EntityModel(loadFile("entity_RP_TC_9674.json")).post());
        }
        List<AttributeModel> simpleAttrList = ems.get(0).getAttributes(nameAttrSimple);
        List<AttributeModel> nestedAttrList = ems.get(0).getAttributes(nameAttrNested);
        List<CrosswalkModel> cwList = ems.get(0).getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + rangeStart);
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (rangeStart + 2 * step));

        simpleAttrList = ems.get(1).getAttributes(nameAttrSimple);
        nestedAttrList = ems.get(1).getAttributes(nameAttrNested);
        cwList = ems.get(1).getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + (rangeStart + 3 * step));
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (rangeStart + 4 * step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart));

        simpleAttrList = ems.get(2).getAttributes(nameAttrSimple);
        nestedAttrList = ems.get(2).getAttributes(nameAttrNested);
        cwList = ems.get(2).getCrosswalks();

        assertEquals(simpleAttrList.size(), 1, "Amount of FirstName's values");
        assertEquals(nestedAttrList.size(), 1, "Amount of License's values");
        assertEquals(cwList.size(), 1, "Amount of crosswalks");
        assertEquals(cwList.get(0).getValue(), "Source_" + (overflowRangeStart + step));
        assertEquals(simpleAttrList.get(0).getAsSimple().getValue(), "Simple_" + (overflowRangeStart + 2 * step));
        assertEquals(nestedAttrList.get(0).getAsNested().getAttribute("Number").getAsSimple().getValue(), "Nested_" + (overflowRangeStart + 3 * step));
    }

    @TmsLink("RP-TC-11993")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "All UUID values are stored for sequential generated attribute in parallel")
    public void runRpTc11993() throws Exception {
        int hcpCount = 500;
        int threadPoolSize = 10;

        TenantManagementService.cleanTenant(Config.getTenantId());
        GeneratorModel generatorModel = new GeneratorModel()
                .setType("SEQUENTIAL")
                .setRangeStart(1)
                .setStep(1);
        registerGenerator(generatorModel);
        String sequentialGeneratorName = generatorModel.getName();
        setVar("AutoGeneratedSimpleName", sequentialGeneratorName, true);
        BusinessConfigurationService.updateConfig(loadFile("config1.json"));

        List<String> autoGeneratorValues = Collections.synchronizedList(new ArrayList());
        List<String> expectedAutoGeneratorValues = new ArrayList<>();
        for (int i = 1; i < hcpCount + 1; i++) {
            expectedAutoGeneratorValues.add("Simple_" + i);
        }
        List<Callable<Object>> actions = new ArrayList<>();
        String tenantIdFinal = Config.getTenantId();
        for (int i = 0; i < hcpCount; i++) {
            actions.add(
                    () -> {
                        try {
                            Config.setTenantId(tenantIdFinal);
                            EntityModel hcp = new EntityModel(loadJson("hcpEntity.json")).post();
                            autoGeneratorValues.add(hcp.getAttribute("AutoGeneratedSimple").getValue().toString());
                            return hcp;
                        } catch (Exception e) {
                            log.error(ExceptionUtils.getStackTrace(e));
                            throw e;
                        }
                    }
            );
        }
        ThreadUtils.executeActions(actions, threadPoolSize);
        Assert.assertEqualsNoOrder(autoGeneratorValues,
                expectedAutoGeneratorValues,
                "Auto generated values aren't correct");
    }

    @TmsLink("RP-TC-5894")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Get list of generators")
    public void runRpTc5894() throws Exception {
        log.info("getting list of generators using ADMIN user");
        Collection<GeneratorModel> generators = getGenerators();
        assertNotNull(generators, "Didn't get list of generators!");

        for (GeneratorModel generator : generators) {
            assertNotNull(generator.getName(), "Name of generator is null!");
            assertNotNull(generator.getType(), "generator type is null!");
        }

        log.info("getting list of generators using NON_ADMIN user");
        try (Account account = AccountService.createUser("RpTc5894", "ROLE_USER", "ROLE_API")) {
            Request getGenerators = new Request(account.getUsername(), Request.Type.GET, Config.getPlatformUrl() + "/api/generators");
            getGenerators.execute();
            Assert.fail("Non-admin user should not be able to get list of generators!");
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().contains("Security error. This endpoint is forbidden for current user."), "Didn't get expected error message!");
        }
    }

    @TmsLink("RP-TC-6436")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Disable generation of ID by \"autoGenerated\": false")
    public void runRpTc6436() throws Exception {
        String oldBusConfig = BusinessConfigurationService.getConfig();
        String[] expectedValues = new String[]{
                "RP_TC_6436_Non_Generated_Value_Crosswalk",
                "RP_TC_6436_Non_Generated_Value_Simple",
                "RP_TC_6436_Non_Generated_Value_Nested"
        };
        // Precondition
        List<GeneratorModel> listGenerators = new ArrayList<>();
        listGenerators.add(new GeneratorModel().setName("UUID4_generator_crosswalk").setType("UUID").setUUIDVersion(4));
        listGenerators.add(new GeneratorModel().setName("UUID4_generator_simple").setType("UUID").setUUIDVersion(4));
        listGenerators.add(new GeneratorModel().setName("UUID4_generator_nested").setType("UUID").setUUIDVersion(4));
        registerGenerators(listGenerators);

        try {
            //1 "autoGenerated": true
            updateConfig(loadFile("config_6436.json"));
            assertNotEquals(getActualAttrValuesRpTc6436(), expectedValues, "Expected value to be auto-generated, but it was not.");

            //2 "autoGenerated": false
            updateConfig(loadFile("config_6436.json").replaceAll("\"autoGenerated\": true", "\"autoGenerated\": false"));
            assertEquals(getActualAttrValuesRpTc6436(), expectedValues, "Expected value to remain unchanged, but it was auto-generated");
        } finally {
            updateConfig(oldBusConfig);
            cleanTenantInternal(getTenant(), true);
            removeAllGenerators("UUID4_generator_");
        }
    }

    private String[] getActualAttrValuesRpTc6436() throws Exception {
        EntityModel em = new EntityModel(loadFile("entity_6436-1.json")).post();
        SmartWaiting.waitForConsistency(em);
        String[] actualValues = {
                em.getCrosswalk(0).getValue(),
                em.getAttribute("AutoGeneratedSimple").getAsSimple().getValue(),
                em.getAttribute("AutoGeneratedNested").getAsNested().getAttribute("ID").getAsSimple().getValue()
        };

        return actualValues;
    }

    private Integer getGeneratedValueFromCrosswalkValue(EntityModel entityModel, String type, String prefix) {
        return this.getGeneratedValueIntFromString(entityModel.getCrosswalkByType(type).getValue(), prefix);
    }

    private Integer getGeneratedValueFromSimpleAttributeValue(EntityModel entityModel, String path, Integer index, String prefix)
            throws ReltioObjectException {
        return this.getGeneratedValueIntFromString(entityModel.getAttributes(path).get(index).getAsSimple().getValue(), prefix);
    }

    private Integer getGeneratedValueFromNestedAttributeValue(EntityModel entityModel, String path, String subPath, String prefix)
            throws ReltioObjectException {
        return this.getGeneratedValueIntFromString(entityModel
                .getAttributes(path).get(0).getAsNested().getAttribute(subPath).getAsSimple().getValue(), prefix);
    }

    private Integer getGeneratedValueIntFromString(String inputString, String prefix) {
        return Integer.parseInt(inputString.substring(prefix.length()));
    }

    private void shouldBeNonDecreasing(List<Integer> listOfIntegers, String message) {
        for (int i = 0; i < listOfIntegers.size() - 1; i++) {
            assertTrue(listOfIntegers.get(i) <= listOfIntegers.get(i + 1), message);
        }
    }

    private void shouldHaveExpectedNumberOfAttributes(EntityModel entityModel, String path, Integer numberOfAttributes)
            throws Exception {
        assertEquals(entityModel.getAttributes(path).size(), numberOfAttributes.intValue(), "Entity " + entityModel.getUri() + " has unexpected number of attributes " + path);
    }

    /***
     * Check that value matched/unmatched to pattern
     * @param pattern
     * @param checkedLine
     * @param should - If value should match then 'should' should be 'true'
     * @throws Exception - If actual result doesn't equal expected result then exception will be thrown
     */
    public void shouldMatchToPattern(String pattern, String checkedLine, boolean should) {
        boolean matches = isMatchToPattern(pattern, checkedLine);
        assertEquals(matches, should, "Value '" + checkedLine + "' " + (matches ? "matches" : "doesn't match") + " to pattern '" + pattern + "'");
    }

    public void shouldMatchToPattern(String pattern, String checkedLine) {
        shouldMatchToPattern(pattern, checkedLine, true);
    }

    public boolean isMatchToPattern(String pattern, String checkedLine) {
        Pattern pattern1 = Pattern.compile(pattern);
        Matcher matcher = pattern1.matcher(checkedLine);
        return matcher.find();
    }

    private String setGeneratorForSourceByUri(String l3config, String sourceUri, String generatorName) {
        JsonObject l3configJson = JsonParser.parseString(l3config).getAsJsonObject();
        JsonArray arrayOfSources = l3configJson.get("sources").getAsJsonArray();
        for (int i = 0; i < arrayOfSources.size(); i++) {
            if (arrayOfSources.get(i).getAsJsonObject().get("uri").getAsString().equals(sourceUri)) {
                arrayOfSources.get(i).getAsJsonObject().addProperty("generator", generatorName);
            }
        }
        return l3configJson.toString();
    }

    private String setGeneratorForAttributeInsideNestedOfEntityType(String l3config, String entityTypeUri,
                                                                    String attributeUri, String subAttributeUri,
                                                                    String generatorName) {
        JsonObject l3configJson = JsonParser.parseString(l3config).getAsJsonObject();
        JsonArray arrayOfEntityTypes = l3configJson.get("entityTypes").getAsJsonArray();
        for (int i = 0; i < arrayOfEntityTypes.size(); i++) {
            if (arrayOfEntityTypes.get(i).getAsJsonObject().get("uri").getAsString().equals(entityTypeUri)) {
                JsonArray arrayOfAttributes = arrayOfEntityTypes.get(i).getAsJsonObject().get("attributes").getAsJsonArray();
                for (int j = 0; j < arrayOfAttributes.size(); j++) {
                    if (arrayOfAttributes.get(j).getAsJsonObject().get("uri").getAsString().equals(attributeUri)) {
                        JsonArray arrayOfSubAttributes = arrayOfAttributes.get(j).getAsJsonObject().get("attributes").getAsJsonArray();
                        for (int k = 0; k < arrayOfSubAttributes.size(); k++) {
                            if (arrayOfSubAttributes.get(k).getAsJsonObject().get("uri").getAsString().equals(subAttributeUri)) {
                                arrayOfSubAttributes.get(k).getAsJsonObject().addProperty("generator", generatorName);
                            }
                        }
                    }
                }
            }
        }
        return l3configJson.toString();
    }

    private String setGeneratorForAttributeOfEntityType(String l3config, String entityTypeUri,
                                                        String attributeUri, String generatorName) {
        JsonObject l3configJson = JsonParser.parseString(l3config).getAsJsonObject();
        JsonArray arrayOfEntityTypes = l3configJson.get("entityTypes").getAsJsonArray();
        for (int i = 0; i < arrayOfEntityTypes.size(); i++) {
            if (arrayOfEntityTypes.get(i).getAsJsonObject().get("uri").getAsString().equals(entityTypeUri)) {
                JsonArray arrayOfAttributes = arrayOfEntityTypes.get(i).getAsJsonObject().get("attributes").getAsJsonArray();
                for (int j = 0; j < arrayOfAttributes.size(); j++) {
                    if (arrayOfAttributes.get(j).getAsJsonObject().get("uri").getAsString().equals(attributeUri)) {
                        arrayOfAttributes.get(j).getAsJsonObject().addProperty("generator", generatorName);
                    }
                }
            }
        }
        return l3configJson.toString();
    }

    private String setGeneratorForAttributeOfRelation(String l3config, String relationTypeUri,
                                                      String attributeUri, String generatorName) {
        JsonObject l3configJson = JsonParser.parseString(l3config).getAsJsonObject();
        JsonArray arrayOfEntityTypes = l3configJson.get("relationTypes").getAsJsonArray();
        for (int i = 0; i < arrayOfEntityTypes.size(); i++) {
            if (arrayOfEntityTypes.get(i).getAsJsonObject().get("uri").getAsString().equals(relationTypeUri)) {
                JsonArray arrayOfAttributes = arrayOfEntityTypes.get(i).getAsJsonObject().get("attributes").getAsJsonArray();
                for (int j = 0; j < arrayOfAttributes.size(); j++) {
                    if (arrayOfAttributes.get(j).getAsJsonObject().get("uri").getAsString().equals(attributeUri)) {
                        arrayOfAttributes.get(j).getAsJsonObject().addProperty("generator", generatorName);
                    }
                }
            }
        }
        return l3configJson.toString();
    }

    private void updateattributeOfEntity(String attributeUri, String value) throws CommandException {
        new Request(Request.Type.PUT, Config.getTenantUrl() + "/" + attributeUri,
                String.format("{\"value\": \"%s\"}", value)).execute();
    }

    private String updateAttributeOfEntityByCrosswalk(String attributeUri, String crosswalkType, String crosswalkValue, String newValueOfAttribute) throws CommandException {
        return new Request(Request.Type.PUT, Config.getTenantUrl() + "/"
                + attributeUri + "?crosswalkValue=" + crosswalkValue, new HashMap<String, String>() {{
            put("Source-System", crosswalkType);
        }}, String.format("{\"value\": \"%s\"}", newValueOfAttribute)).execute();
    }

    private JsonElement registerGenerator(GeneratorModel generatorModel, boolean force) throws CommandException {
        JsonElement result = GeneratorService.registerGenerator(generatorModel, force);
        this.generatorsUsed.add(generatorModel.getName());
        return result;
    }

    private JsonElement registerGenerator(GeneratorModel generatorModel) throws CommandException {
        return registerGenerator(generatorModel, false);
    }

    private void removeUsedGenerators() throws CommandException {
        for (String generatorName : this.generatorsUsed) {
            if (GeneratorService.isGeneratorExistByName(generatorName)) {
                GeneratorService.removeGenerator(generatorName);
            }
        }
    }

    /**
     * Register simple generator with random name and set appropriate variable
     *
     * @param varName variable that will be set
     */
    private void addRandomGenerator(String varName) throws CommandException {
        GeneratorModel generator = new GeneratorModel();
        registerGenerator(generator);
        setVar(varName, generator.getName(), true);
    }

    private void checkAttributes9384(EntityModel em, String prefix, Integer nesting, String simple, String sentValue) throws ReltioObjectException {
        String patternNested = "Nested_[0-9]+";
        String generatedValue = em.getAttribute(simple).getAsSimple().getValue().substring("Simple_".length());
        for (int i = 1; i <= nesting; i++) {
            List<AttributeModel> attributes = em.getDeepNestedAttributes(NestedAttributes.getSimplePath(i, prefix, simple));
            assertEquals(attributes.size(), 1);
            assertNotEquals(attributes.get(0).getAsSimple().getValue().substring("Nested_".length()), generatedValue);
            shouldMatchToPattern(patternNested, attributes.get(0).getAsSimple().getValue());
            assertNotEquals(attributes.get(0).getAsSimple().getValue(), sentValue);
        }
    }
}
