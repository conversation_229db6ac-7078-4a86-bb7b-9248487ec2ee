package com.reltio.qa.services.devOps;

import com.reltio.qa.GlobalConfig;
import com.reltio.qa.enums.common.CloudType;
import com.reltio.qa.enums.devOps.*;
import com.reltio.qa.enums.match.MatchingStrategy;
import com.reltio.qa.exceptions.ApplicationGlobalException;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.model.devOps.DevOpsTaskModel;
import com.reltio.qa.model.devOps.DevOpsTenantModel;
import com.reltio.qa.request.Request;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.utils.ProvisioningUtils;
import com.reltio.qa.utils.RandomGenerator;
import com.reltio.qa.utils.TimeUtils;
import lombok.extern.log4j.Log4j2;

import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.reltio.qa.Config.getEnvName;
import static com.reltio.qa.Config.isIsolatedEnvironment;
import static com.reltio.qa.request.Request.Type.*;
import static com.reltio.qa.services.TenantManagementService.getTenant;
import static com.reltio.qa.services.devOps.DevOpsRequest.devopsRequestUrl;
import static com.reltio.qa.services.devOps.DevOpsRequest.doDevOpsRequest;
import static com.reltio.qa.services.devOps.DevOpsTaskService.*;

@Log4j2
public final class DevOpsTenantService {

    private static final String ENV_NAME = getEnvName();
    private static final Map<String, Long> cleanTenantTimestamp = new ConcurrentHashMap<>();

    private DevOpsTenantService() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Generate tenant physical configuration
     *
     * @param tenantName       - tenant name
     * @param tenantId         - tenant ID
     * @param tenantSize       - tenant size
     * @param matchingStrategy - matching strategy
     * @return tenant physical configuration as String
     * @throws CommandException
     */
    public static String generateTenantConfig(String tenantName, String tenantId, TenantSize tenantSize, MatchingStrategy matchingStrategy) throws CommandException {
        CloudType cloudType = DevOpsService.getEnvironmentCloudProvider();
        return generateTenantConfig(
                tenantName,
                tenantId,
                tenantSize,
                matchingStrategy,
                ProvisioningUtils.getStoragePriorityList(cloudType)
        );
    }

    /**
     * Generate tenant physical configuration
     *
     * @param tenantName          - tenant name
     * @param tenantId            - tenant ID
     * @param tenantSize          - tenant size
     * @param matchingStrategy    - matching strategy
     * @param storagePriorityList - storage priority list
     * @return tenant physical configuration as String
     * @throws CommandException
     */
    public static String generateTenantConfig(String tenantName, String tenantId, TenantSize tenantSize, MatchingStrategy matchingStrategy, Map<ServicePurpose, List<ReltioService>> storagePriorityList) throws CommandException {
        boolean useServiceBusNamespace = GlobalConfig.useServiceBusStandardNamespace();
        boolean useSpannerForInteractions = GlobalConfig.isUseSpannerForInteractions();
        boolean useSpannerCloudFunction =
                (GlobalConfig.isUseSpannerCloudFunction() && (GlobalConfig.useSpannerDB() || GlobalConfig.isUseSpannerForInteractions()))
                        || GlobalConfig.useCosmosDB();

        String serviceBusNamespace = null;
        DevOpsTenantModel.Tier tier = null;

        if (useServiceBusNamespace) {
            serviceBusNamespace = ENV_NAME + "-" + tenantId;
            tier = DevOpsTenantModel.Tier.STANDARD;
        }

        return generateTenantConfig(
                tenantName,
                tenantId,
                tenantSize,
                matchingStrategy,
                storagePriorityList,
                useSpannerCloudFunction,
                useSpannerForInteractions,
                useServiceBusNamespace,
                serviceBusNamespace,
                tier
        );
    }

    /**
     * Generate tenant physical configuration
     *
     * @param tenantName              - tenant name
     * @param tenantId                - tenant ID
     * @param tenantSize              - tenant size
     * @param matchingStrategy        - matching strategy
     * @param storagePriorityList     - storage priority list
     * @param useSpannerCloudFunction - use Spanner Cloud Function
     * @param useServiceBusNamespace  - use Service Bus Namespace
     * @param serviceBusNamespace     - Service Bus Namespace
     * @param tier                    - Service Bus Pricing Tier
     * @return tenant physical configuration as String
     * @throws CommandException
     */
    public static String generateTenantConfig(
            String tenantName,
            String tenantId,
            TenantSize tenantSize,
            MatchingStrategy matchingStrategy,
            Map<ServicePurpose, List<ReltioService>> storagePriorityList,
            boolean useSpannerCloudFunction,
            boolean useSpannerForInteractions,
            boolean useServiceBusNamespace,
            String serviceBusNamespace,
            DevOpsTenantModel.Tier tier) throws CommandException {

        String body = new DevOpsTenantModel.DevOpsTenantModelBuilder()
                .setTenantName(tenantName)
                .setTenantId(tenantId)
                .setTenantSize(tenantSize)
                .setMatchingStrategy(matchingStrategy)
                .setReplicationFactor(1)
                .setTenantInternalId(RandomGenerator.getInstance().getRandomInt(1, 4095))
                .setStoragePriorityList(storagePriorityList)
                .setUseSpannerCloudFunction(useSpannerCloudFunction)
                .setUseSpannerForInteractions(useSpannerForInteractions)
                .setUseServiceBusFunction(useServiceBusNamespace)
                .setServiceBusNamespace(serviceBusNamespace)
                .setServiceBusPricingTier(tier)
                .build()
                .toString();
        return doDevOpsRequest(new Request(POST, devopsRequestUrl(null, "_generate", null), body));
    }

    /**
     * Generate tenant physical configuration
     *
     * @param body Request body
     * @return tenant physical configuration as String
     * @throws CommandException
     */
    public static String generateTenantConfig(String body) throws CommandException {
        return doDevOpsRequest(new Request(POST, devopsRequestUrl(null, "_generate", null), body));
    }

    /**
     * Create tenant by DevOpsApi
     *
     * @param body        - physical configuration
     * @param waitForTask - defines whether need to wait for task to complete
     * @return result of creating as String
     * @throws CommandException
     */
    public static DevOpsTaskModel createTenant(String body, boolean waitForTask) throws CommandException {
        log.info("Start creating tenant by task 'DevOpsTenantService.createTenant(.*)' ...");
        DevOpsTaskModel task = DevOpsTaskModel.fromJson(
                doDevOpsRequest(new Request(POST, devopsRequestUrl(null, "tenants", null), body)));

        log.info("Create tenant task submitted");

        if (waitForTask) {
            task = SmartWaiting.waitForDevopsTaskHasStatus(task, TaskStatus.COMPLETED);
            log.info("Create {} tenant task completed with status {} in {}", task.getTenantId(), task.getStatus(), task.getDuration());
        }
        return task;
    }

    /**
     * Delete tenant by DevOpsApi
     *
     * @param tenantId    - tenant ID
     * @param waitForTask - defines whether need to wait for task to complete
     * @return result delete task
     */
    public static DevOpsTaskModel deleteTenant(String tenantId, boolean waitForTask) throws CommandException {
        return deleteTenant(tenantId, waitForTask, false);
    }

    /**
     * Delete tenant by DevOpsApi, wait for task to complete
     *
     * @param tenantId - tenant ID
     * @return result delete task
     * @throws CommandException
     */
    public static DevOpsTaskModel deleteTenant(String tenantId) throws CommandException {
        return deleteTenant(tenantId, true);
    }

    /**
     * Delete tenant by DevOpsApi
     *
     * @param tenantId                - tenant ID
     * @param waitForTask             - defines whether need to wait for task to complete
     * @param skipMaintenanceModeStep - specify if MaintenanceMode should be ignored or not (for DynamoDB and SpannerDB is never ignored)
     * @return result delete task
     * @throws CommandException
     */
    public static DevOpsTaskModel deleteTenant(String tenantId, boolean waitForTask, boolean skipMaintenanceModeStep) throws CommandException {
        log.info("Start deleting tenant '" + tenantId + "' by task 'DevOpsTenantService.deleteTenant(.*)' ...");
        List<CleaningTenantsSteps> stepsToIgnore = new ArrayList<>();

        // Skip validating tenant resources to avoid DevOps task failure when some environments are down
        stepsToIgnore.add(CleaningTenantsSteps.ValidateTenantResources);

        // Skip BackupTenantConfigs step on Pipelines
        if (isIsolatedEnvironment()) {
            stepsToIgnore.add(CleaningTenantsSteps.BackupTenantConfigs);
        }

        if (skipMaintenanceModeStep && getTenant(tenantId).hasCassandraDBDataStorage()) {
            stepsToIgnore.add(CleaningTenantsSteps.MaintenanceMode);
        }
        DevOpsTaskModel task = deleteTenantTask(tenantId, "ignoreAllErrors=true", makeTaskBody(stepsToIgnore));

        log.info("Delete {} tenant task submitted", tenantId);

        if (waitForTask) {
            task = SmartWaiting.waitForDevopsTaskHasStatus(task, TaskStatus.COMPLETED);
            log.info("Delete {} tenant task completed with status {} in {}", task.getTenantId(), task.getStatus(), task.getDuration());
        }
        return task;
    }

    /**
     * Delete ActivityLog tenant by DevOpsApi
     *
     * @param tenantId    - tenant ID
     * @param waitForTask - defines whether you need to wait for task to complete
     * @return result delete task
     * @throws CommandException
     */
    public static DevOpsTaskModel deleteALTenant(String tenantId, boolean waitForTask) throws CommandException {
        log.info("Start deleting ActivityLog tenant '" + tenantId + "' by task 'DevOpsTenantService.deleteALTenant(.*)' ...");

        DevOpsTaskModel task = DevOpsTaskModel.fromJson(doDevOpsRequest(new Request(DELETE, devopsRequestUrl(null, "al/" + tenantId, null))));

        log.info("Delete {} ActivityLog tenant task submitted {}", tenantId, task.getTaskId());

        if (waitForTask) {
            task = SmartWaiting.waitForDevopsTaskHasStatus(task, TaskStatus.COMPLETED);
            log.info("Delete {} ActivityLog tenant task completed with status {} in {}", task.getTenantId(), task.getStatus(), task.getDuration());
        }
        return task;
    }

    /**
     * Delete tenant by DevOpsApi without throwing any Exceptions, wait for task to complete
     *
     * @param tenantId - tenant ID
     */
    public static void deleteTenantUnchecked(String tenantId) {
        try {
            deleteTenant(tenantId);
        } catch (Exception e) {
            log.warn("Can not delete tenant '" + tenantId + "' due to exception: " + e.getMessage());
        }
    }

    /**
     * Delete tenant by DevOpsApi without throwing any Exceptions, do not wait for task to complete
     *
     * @param tenantId - tenant ID
     */
    public static void deleteTenantUncheckedWithoutWait(String tenantId) {
        try {
            deleteTenant(tenantId, false);
        } catch (Exception e) {
            log.warn("Can not delete tenant '" + tenantId + "' due to exception: " + e.getMessage());
        }
    }

    /**
     * Clean tenant by DevOpsApi by async task
     *
     * @param tenantId - tenant ID
     * @return result of cleaning as String
     * @throws CommandException
     */
    public static DevOpsTaskModel cleanTenant(String tenantId) throws CommandException {
        return cleanTenant(tenantId, true, true);
    }

    /**
     * Clean tenant by DevOpsApi by async task
     *
     * @param tenantId                - tenant ID
     * @param waitForTask             - specify if method has to wait for task finishes or not
     * @param skipMaintenanceModeStep - specify if MaintenanceMode should be ignored or not
     * @return result of cleaning as String
     * @throws CommandException
     */
    public static DevOpsTaskModel cleanTenant(String tenantId, boolean waitForTask, boolean skipMaintenanceModeStep) throws CommandException {
        List<CleaningTenantsSteps> steps = skipMaintenanceModeStep ? Collections.singletonList(CleaningTenantsSteps.MaintenanceMode) : null;
        return cleanTenant(tenantId, waitForTask, steps, false);
    }

    /**
     * Clean tenant by DevOpsApi by async task
     *
     * @param tenantId         - tenant ID
     * @param waitForTask      - specify if method has to wait for task finishes or not
     * @param steps            - list of steps which are required OR should be ignored
     * @param areStepsRequired - specify if steps are required or should be ignored
     * @return result of cleaning as String
     * @throws CommandException
     */
    public static DevOpsTaskModel cleanTenant(String tenantId, boolean waitForTask, List<CleaningTenantsSteps> steps, boolean areStepsRequired) throws CommandException {
        log.info("Start cleaning tenant '" + tenantId + "' by async task 'DevOpsTenantService.cleanTenant(.*)' ...");

        long currentTimestamp = System.currentTimeMillis();
        if (cleanTenantTimestamp.containsKey(tenantId) && currentTimestamp - cleanTenantTimestamp.get(tenantId) < GlobalConfig.getWaiterUpdateDelay()) {
            log.info("We are cleaning tenant too frequently for " + tenantId + " tenant. Waiting " + (GlobalConfig.getWaiterUpdateDelay() / 1000) + " seconds");
            TimeUtils.sleep(GlobalConfig.getWaiterUpdateDelay());
        }

        List<CleaningTenantsSteps> stepsToIgnore = defineStepsToIgnore(steps, areStepsRequired);

        DevOpsTaskModel task = cleanTenantTask(tenantId, "cleanKeys=true&ignoreAllErrors=true", makeTaskBody(stepsToIgnore));

        log.info("Clean Tenant task was submitted for {}", tenantId);
        if (waitForTask) {
            task = SmartWaiting.waitForDevopsTaskHasStatus(task, TaskStatus.COMPLETED);
            log.info("Clean {} task completed with status {} in {}", tenantId, task.getStatus(), task.getDuration());
        }
        cleanTenantTimestamp.put(tenantId, System.currentTimeMillis());
        return task;
    }

    /**
     * Define steps to ignore based on the provided list of steps and whether they are required.
     *
     * @param steps            List of steps to consider.
     * @param areStepsRequired Whether the provided steps are required or should be ignored.
     * @return List of steps to ignore.
     */
    private static List<CleaningTenantsSteps> defineStepsToIgnore(List<CleaningTenantsSteps> steps, boolean areStepsRequired) {
        List<CleaningTenantsSteps> stepsToIgnore = new ArrayList<>();

        if (steps != null) {
            if (areStepsRequired) {
                // Add all existent steps and remove one that need to execute
                stepsToIgnore = Arrays.stream(CleaningTenantsSteps.values()).toList();
                stepsToIgnore.removeAll(steps);
            } else {
                // Add steps that need to ignore
                stepsToIgnore = new ArrayList<>(steps);
            }
        }

        // Skip validating tenant resources to avoid DevOps task failure when some environments are down
        stepsToIgnore.add(CleaningTenantsSteps.ValidateTenantResources);

        // Skip HistoryKeyspace cleanup by default
        if (GlobalConfig.isSkipHistoryCleanup()) {
            stepsToIgnore.add(CleaningTenantsSteps.HistoryKeyspace);
        }

        return stepsToIgnore;
    }

    public static void checkTaskFails(DevOpsTaskModel task) throws CommandException {
        if (ifStatusFailed(task)) {
            String failureMessage;
            String status = task.getStatus();

            if (status.equals(TaskStatus.STOPPED.toString()) || status.equals(TaskStatus.STOPPING.toString())) {
                failureMessage = task.getTaskName() + " was stopped manually";
            } else if (status.equals(TaskStatus.CANCELED.toString()) || status.equals(TaskStatus.ABANDONED.toString())) {
                failureMessage = task.getMessage();
            } else {
                failureMessage = String.join(", ", getFailedStepMessages(task.getTaskId()));
            }
            throw new ApplicationGlobalException(task.getTaskName() + " " + task.getTaskId()
                    + " failed with messages: " + failureMessage);
        }
    }

    /**
     * Reset cleanTenantTimestamp for a tenant
     *
     * @param tenantId - tenant ID
     */
    public static void resetCleanTenantTimestamp(String tenantId) {
        cleanTenantTimestamp.remove(tenantId);
    }

    /**
     * Clean internal queues of events for tenant with tenantId
     * NOTE: it doesn't clean streaming queues
     *
     * @param tenantId
     * @return result - DevOps API response
     */
    public static String cleanInternalQueues(String tenantId) throws CommandException {
        log.info("Start cleaning internal queues for tenant '" + tenantId + "' by 'DevOpsTenantService.cleanInternalQueues(.*)' ...");
        String result = doDevOpsRequest(new Request(POST, devopsRequestUrl(tenantId, "iqueues/clean", null), ""));
        log.info("Internal queues for tenant '" + tenantId + "' were cleaned");
        return result;
    }

    /**
     * Get list of tenant ids in specified environment
     *
     * @return list of tenants
     * @throws CommandException
     */
    public static String getTenants() throws CommandException {
        return doDevOpsRequest(new Request(GET, devopsRequestUrl(null, "tenants", null), ""));
    }

    /**
     * Post tenant lookups
     *
     * @param tenantId - tenant ID
     * @param lookups  - body
     * @return response
     * @throws CommandException
     */
    public static String postLookups(String tenantId, String lookups) throws CommandException {
        return doDevOpsRequest(new Request(POST, devopsRequestUrl(tenantId, "lookups", null), lookups));
    }

    /**
     * Get tenant lookups
     *
     * @param tenantId - tenant ID
     * @return tenant lookups
     * @throws CommandException
     */
    public static String getLookups(String tenantId) throws CommandException {
        return doDevOpsRequest(new Request(GET, devopsRequestUrl(tenantId, "lookups", null), ""));
    }

    /**
     * Get API system configuration
     *
     * @return
     * @throws CommandException
     */
    public static String getSystemConfig() throws CommandException {
        return doDevOpsRequest(new Request(GET, devopsRequestUrl(null, "systemConfig", null), ""));
    }

    /**
     * Get streaming configuration
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     * @throws MalformedURLException
     */
    public static String getStreamingConfig(String tenantId) throws MalformedURLException, CommandException {
        try {
            return doDevOpsRequest(new Request(GET, devopsRequestUrl(tenantId, "streamingConfig", null), ""));
        } catch (Exception exc) {
            if (exc.getMessage().contains("Streaming is not defined")) {
                return exc.getMessage();
            } else {
                throw exc;
            }
        }
    }

    /**
     * Delete tenant physical configuration from system keyspace
     *
     * @param tenantId - tenant ID
     * @throws CommandException
     */
    public static void deleteTenantConfig(String tenantId) throws CommandException {
        doDevOpsRequest(new Request(DELETE, devopsRequestUrl(ENV_NAME, tenantId, "deleteTenantConfig", null)));
    }

    /**
     * Search tenants for specified expression
     *
     * @param body
     * @return
     * @throws CommandException
     */
    public static String searchTenants(String body) throws CommandException {
        return doDevOpsRequest(new Request(POST, devopsRequestUrl(null, null, "search", ENV_NAME), body));
    }

    /**
     * Get ES index statistics
     *
     * @param tenantId - tenant ID
     * @param index    - index name
     * @return ES indexes statistics
     * @throws CommandException
     */
    public static String getEsSize(String tenantId, String index) throws CommandException {
        String body = String.format("{\"%s\":[\"%s\"]}", getTenant(tenantId).getSearchStorageConfiguration().getEsClusterName(), index);
        return doDevOpsRequest(new Request(POST, devopsRequestUrl(null, null, "_essize", ENV_NAME), body));
    }

    /**
     * Get tenant config
     *
     * @param tenantId - tenant ID
     * @return tenant config
     * @throws CommandException
     */
    public static String getPhysicalConfig(String tenantId) throws CommandException {
        return doDevOpsRequest(new Request(GET, devopsRequestUrl(tenantId, "", null), ""));
    }

    /**
     * Get tenant business config
     *
     * @param tenantId - tenant ID
     * @return tenant business config
     * @throws CommandException
     */
    public static String getBusinessConfig(String tenantId) throws CommandException {
        return doDevOpsRequest(new Request(GET, devopsRequestUrl(tenantId, "configuration", null), ""));
    }
}
