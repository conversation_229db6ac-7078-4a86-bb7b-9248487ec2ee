package com.reltio.qa.services.tenant.config;

import com.google.gson.JsonElement;
import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.model.TenantModel;
import com.reltio.qa.model.platform.config.phys.DataStorageModel;
import com.reltio.qa.model.platform.config.phys.TableProperties;
import com.reltio.qa.request.Request;
import lombok.extern.log4j.Log4j2;

import static com.reltio.qa.model.platform.config.phys.TableProperties.CAPACITY_MODE_ON_DEMAND;

@Log4j2
public final class DataStorageConfigUtils {

    // DynamodDB
    public static final String CUSTOM_IAM_ROLE_QA01_TEMPLATE = "arn:aws:iam::************:role/reltio.platform.role.crossaccount-matching-dynamodb-ENV_NAME";
    public static final String CUSTOM_IAM_ROLE_QA02_TEMPLATE = "arn:aws:iam::************:role/reltio.platform.role.crossaccount-dynamodb-ENV_NAME";
    public static final String DYNAMODB_TYPE = "DynamoDB";

    private DataStorageConfigUtils() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * Returns the DynamoDB IAM role for the given environment.
     * Depending on the USE_CUSTOM_DYNAMODB_IAM_ROLE flag, a different AWS account will be used.
     *
     * @param envName Name of the environment; postfix in the IAM role name.
     * @return DynamoDB IAM role.
     */
    public static String getaAwsIAMExternalRole(String envName) {
        String template = GlobalConfig.useCustomDynamoDBIamRole()
                ? CUSTOM_IAM_ROLE_QA02_TEMPLATE
                : CUSTOM_IAM_ROLE_QA01_TEMPLATE;

        return template.replace("ENV_NAME", envName);
    }

    /**
     * Generates a DynamoDB storage model.
     *
     * @param id  Storage ID.
     * @param env Environment name.
     * @return Default DynamoDB storage model.
     */
    public static DataStorageModel generateDefaultDynamoStorage(String id, String env) {
        return new DataStorageModel()
                .setId(id)
                .setType(DYNAMODB_TYPE)
                .setDynamoDBConfig(
                        new DataStorageModel.DynamoDBConfig()
                                .setAwsRegion(Config.getAwsRegion().getName())
                                .setAwsIAMExternalRole(DataStorageConfigUtils.getaAwsIAMExternalRole(env))
                                .setDefaultTableProperties(
                                        new TableProperties()
                                                .capacityMode(CAPACITY_MODE_ON_DEMAND)
                                                .minReadCapacity(10)
                                                .minWriteCapacity(10)
                                                .maxReadCapacity(400)
                                                .maxWriteCapacity(300)
                                ));
    }

    public static JsonElement synchronizeStoragesWithTenantConfiguration(String tenantId) throws CommandException {
        return new Request(Request.Type.POST, Config.getApiUrl() + tenantId + "/synchronizeStoragesWithTenantConfiguration").executeJson();
    }

    /**
     * Returns the repository version for interactions storage in the tenant model.
     *
     * @param tenantModel The tenant model containing the storage configuration.
     * @param storageId   The ID of the storage to retrieve the repository version from.
     * @return The repository version for interactions storage.
     */
    public static String getInteractionsRepositoryVersion(TenantModel tenantModel, String storageId) {
        return tenantModel.getDataStorage(storageId)
                .getSpannerDBConfig()
                .getRepositoryVersion();
    }
}
