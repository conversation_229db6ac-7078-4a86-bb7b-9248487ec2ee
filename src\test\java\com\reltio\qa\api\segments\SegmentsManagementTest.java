package com.reltio.qa.api.segments;

import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.enums.tasks.ExecutionType;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.exceptions.ReltioObjectException;
import com.reltio.qa.helpers.loaddata.LoadData;
import com.reltio.qa.model.*;
import com.reltio.qa.model.Interactions.InteractionModel;
import com.reltio.qa.model.segmentation.GetSegmentResponse;
import com.reltio.qa.model.segmentation.PostSegmentResponse;
import com.reltio.qa.model.segmentation.SegmentModel;
import com.reltio.qa.model.segmentation.SegmentService;
import com.reltio.qa.services.*;
import com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformConfigs;
import com.reltio.qa.services.tenant.config.TenantManagementServiceForInteractions;
import com.reltio.qa.utils.RandomGenerator;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.Assert;
import org.testng.SkipException;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.reltio.qa.Config.getTenantUrl;
import static com.reltio.qa.helpers.CommonStrings.*;
import static com.reltio.qa.model.Interactions.InteractionModel.interactionWithSimpleAttr;
import static com.reltio.qa.model.Interactions.InteractionModel.simpleInteraction;
import static com.reltio.qa.model.SearchFilterExpression.contains;
import static com.reltio.qa.model.SearchFilterExpression.equalsFilter;
import static com.reltio.qa.model.segmentation.SegmentModel.Sharing.PRIVATE;
import static com.reltio.qa.model.segmentation.SegmentModel.Sharing.PUBLIC;
import static com.reltio.qa.model.segmentation.SegmentModel.Status.*;
import static com.reltio.qa.model.segmentation.SegmentModel.Type.REALTIME;
import static com.reltio.qa.model.segmentation.SegmentService.*;
import static com.reltio.qa.services.EntityService.addAnalyticsAttribute;


@Log4j2
public class SegmentsManagementTest extends TestNGBaseTest {

    RandomGenerator rg = RandomGenerator.getInstance();
    String randomString;

    private static final String SEGMENTATION_TEST_POSTFIX = " segmentation test";

    private static final String USER_2 = "User" + RandomGenerator.getInstance().getRandomString(5);
    private String User2;
    private GetSegmentResponse searchResult;
    SearchFilter searchFilter;
    SearchFilterGroup searchFilterGroup;
    GetSegmentRequestParameters filterAndOptions;
    private EntityModel en1;
    boolean nonGCPEnv;

    @BeforeClass(alwaysRun = true)
    public void bcSegmentsTest() throws Exception {
        //TODO remove this line and set explicitly account name in all tests. NB! Admin account will not be allowed as a default account in the future. RP-151889
        AccountService.setDefaultAccountName(AccountService.ADMIN_NAME);
        setDataFolder("resources:api/search/SegmentationSearch");

        AccountService.createUser(USER_2, "ROLE_API", "ROLE_USER", "ROLE_TEST");
        BusinessConfigurationService.updateConfig(loadFile("ss-tenant-config.json"));

        if (!GlobalConfig.isUseSpannerForInteractions()) {
            log.info("Skip switching to Interactions V2, using legacy interactions");
            return;
        }

        log.info("Using Spanner for Interactions");
        SearchService.setV2(true);

        if (!TenantManagementServiceForInteractions.isInteractionsRepositoryVersionSetToV2()) {
            log.info("Update tenant's config to use SpannerDB for Interactions...");
            TenantManagementServiceForInteractions.enableInteractionsV2Config(Config.getTenantId());
            SmartWaitingPlatformConfigs.waitForRepositoryVersionSetToV2InConfig();
        }

        precondition();
    }

    private void precondition() throws IOException, CommandException, ReltioObjectException {

        // - add HCP1 by User2
        // - add HCP2
        // - add HCO1 by User2
        // - add HCP3 by User2 w/o Interaction
        log.info("Run precondition...");
        User2 = AccountService.getAccount(USER_2).getUsername();
        en1 = LoadData.createHCP(getTenantUrl(), "AMA", false).post(User2);
        EntityModel en2 = new EntityModel(ENTITY_TYPE_HCP).post();
        EntityModel en3 = new EntityModel(ENTITY_TYPE_HCO).post(User2);
        EntityModel entityWOInteractions = new EntityModel(ENTITY_TYPE_HCP).post(User2);
        String email_attribute_subject_value = "Subject. test$01".concat(SEGMENTATION_TEST_POSTFIX);

        // - add Interaction1 for HCP1,
        // - add Interaction2 for HCP2 by User2,
        // - add Interaction3 for HCO and HCP2
        simpleInteraction(en1, INTERACTION_TYPE_EMAIL, "From").post();
        interactionWithSimpleAttr(en2, INTERACTION_TYPE_PRESCRIPTIONS, "HCP", "Subject", "Subject.prescription").post(User2);
        interactionWithSimpleAttr(en3, INTERACTION_TYPE_EMAIL, "From", "Subject", email_attribute_subject_value).post();

        //load members and ExhibitionEventInteraction
        EntityService.postEntities(loadFile("HCP-HCO-members.json"));
        InteractionModel im = new InteractionModel(loadFile("interaction-ExhEvent.json")).post();

        //add analytics attributes
        addAnalyticsAttribute(en1, "AT_EXHBT_NAME",
                new SimpleAttributeModel("name1"));


        TaskService.segmentationSync(ExecutionType.TENANT, new PostTaskRequestParameters()
                        .tenantId(Config.getTenantId()))
                .waitWhileRunning();

    }

    @TmsLink("RP-TC-13761")
    @Test(groups = {"regression"}, description = "Get segments test")
    public void runRpTc13761() throws CommandException, IOException {
        //delete segments if presents
        cleanupSegments();

        // get with ORDER, MAX, OFFSET, SORT, FILTER
        log.info("Load segments from json file");
        postSegments(loadFile("resources:api/segments/segments-01.json"));

        // get segments and check objects count
        log.info("Step1. Get segments w/o filter");
        checkGetSegmentsResponse(null, 5);

        log.info("Step2. Get segments with type field");
        searchFilter = equalsFilter("type", REALTIME.getName());
        filterAndOptions = new GetSegmentRequestParameters().filter(searchFilter);

        checkGetSegmentsResponse(filterAndOptions, 4);

        log.info("Step3. Get segments with sharing field");
        searchFilter = equalsFilter("sharing", PRIVATE.getName());
        filterAndOptions = new GetSegmentRequestParameters().filter(searchFilter);

        checkGetSegmentsResponse(filterAndOptions, 2);

        //TODO: remove condition after bug is fixed
        if (nonGCPEnv) {
            log.info("Step4. Get segments with integrationIDs field filter");
            searchFilter = contains("integrationIDs", "some integration id%2301");
            filterAndOptions = new GetSegmentRequestParameters().filter(searchFilter);

            checkGetSegmentsResponse(filterAndOptions, 3);
        }


        log.info("Step5. Get segments with status field");
        searchFilterGroup = new SearchFilterGroup(equalsFilter("status", DISABLED.getName())).or(
                equalsFilter("status", SUBMITTED.getName())).or(equalsFilter("status", LIVE.getName()));
        filterAndOptions = new GetSegmentRequestParameters().filter(searchFilterGroup);
        checkGetSegmentsResponse(filterAndOptions, 5);

        //max
        //offset
        log.info("Step6. Get segments with max and offset ");
        searchFilter = contains("label", "Test segment");
        filterAndOptions = new GetSegmentRequestParameters().filter(searchFilter).offset(1).max(3);

        checkGetSegmentsResponse(filterAndOptions, 4);

    }

    @TmsLink("RP-TC-13762")
    @Test(groups = {"regression"}, description = "Batch segment test")
    public void runRpTc13762() throws Exception {
        log.info("Load segments from json file");
        postSegments(loadFile("resources:api/segments/segments-02.json"));

        log.info("Step1. Get segments and store segment id");
        searchFilter = equalsFilter("label", "Batch job test RP-13762");
        filterAndOptions = new GetSegmentRequestParameters().filter(searchFilter);
        searchResult = getSegments(filterAndOptions);

        String segmentId = Arrays.stream(searchResult.getSegments()).findFirst().get().getId();

        log.info("Step2. Add DoB attribute to model");
        en1.addSimpleAttributeToModel("DoB", "795916800000").post();

        log.info("Step3. Run batch segmentation job");
        ListOfTasksModel tasks = TaskService.segmentRecalculation(ExecutionType.TENANT,
                new PostTaskRequestParameters().tenantId(Config.getTenantId()).segmentId(segmentId)).waitWhileRunning();


        String lastEvaluationDateFromTask = tasks.getListOfTasks().getFirst().getCurrentState()
                .getLastRecalculationDate().split("\\[")[0];

        log.info("Step4. Check segments fields: status, lastRecalculation, segmentSize");
        SegmentModel segment = SegmentService.getSegment(segmentId);
        Assert.assertEquals(segment.getStatus(), LIVE.getName(), "segment status not expected");
        Assert.assertEquals(lastEvaluationDateFromTask, segment.getLastEvaluationDate(), "evaluation date not expected");
        Assert.assertEquals(segment.getSegmentSize(), 1, "segment size not expected");
    }

    @TmsLink("RP-TC-13653")
    @Test(groups = {"regression"}, description = "CRUD private segments test")
    public void runRpTc13653() throws CommandException {

        String restrictionMessage = "User does not have access to the segment: %s";
        String segLabelUpdateOwner = "Update segment by owner";
        String segLabelUpdateNonOwner = "Update segment by non owner";

        log.info("Step1.Add segments private and public");
        List<PostSegmentResponse> postSegmentsResponse = addSimpleSegment(PRIVATE);
        SegmentModel segmentPrivate = postSegmentsResponse.getFirst().getObject();
        postSegmentsResponse = addSimpleSegment(PUBLIC);
        SegmentModel segmentPublic = postSegmentsResponse.getFirst().getObject();

        String segmentPrivateId = segmentPrivate.getId();
        String segmentPublicId = segmentPublic.getId();

        segmentPrivate.setLabel(segLabelUpdateOwner);
        log.info("Step2. Update segment by owner");
        SegmentModel segmentUpdated = updateSegment(segmentPrivateId, segmentPrivate);
        Assert.assertEquals(segmentPrivate, segmentUpdated);

        log.info("Step3. Update private segment by non owner");
        segmentPrivate.setLabel(segLabelUpdateNonOwner);
        try {
            updateSegment(USER_2, segmentPrivateId, segmentPrivate);
        } catch (CommandException e) {

            Assert.assertTrue(e.getMessage().contains(String.format(restrictionMessage, segmentPrivateId)));
        }

        log.info("Step4. Update public segment by non owner");
        segmentPublic.setLabel(segLabelUpdateNonOwner);
        segmentPublic = updateSegment(USER_2, segmentPublicId, segmentPublic);
        Assert.assertEquals(segmentPublic.getLabel(), segLabelUpdateNonOwner);

        log.info("Step5. Delete segment by non owner");
        try {
            deleteSegment(USER_2, segmentPrivateId);
        } catch (CommandException e) {
            Assert.assertTrue(e.getMessage().contains(String.format(restrictionMessage, segmentPrivateId)));
        }

        log.info("Step6. Delete public segment by non owner");
        Assert.assertEquals(deleteSegment(USER_2, segmentPublicId), "{\"status\":\"success\"}");
        verifySegmentIdDeleted(segmentPublicId);

        log.info("Step7. Delete segment by owner. Get deleted segment");
        Assert.assertEquals(deleteSegment(segmentPrivateId), "{\"status\":\"success\"}");
        verifySegmentIdDeleted(segmentPrivateId);
    }

    private void verifySegmentIdDeleted(String segmentId) {
        try {
            getSegment(segmentId);
        } catch (CommandException e) {
            Assert.assertTrue(e.getMessage()
                    .contains(String.format("Segment with id=%s not found", segmentId)));
        }
    }

    private List<PostSegmentResponse> addSimpleSegment(SegmentModel.Sharing sharing) throws CommandException {
        randomString = rg.getRandomAlphabeticString(6);
        String description = "Some description_".concat(randomString);
        String label = "Some label_".concat(randomString);
        boolean favorite = rg.getRandomBooleanPrimitive();

        searchFilter = equalsFilter("entity.type", "HCP");

        String rule = searchFilter.toString();

        SegmentModel segment = new SegmentModel(
                description,
                label,
                sharing.getName(),
                favorite,
                rule);

        return segment.post();
    }

    private void checkGetSegmentsResponse(GetSegmentRequestParameters filterAndOptions, int expectedValue) {
        try {
            searchResult = filterAndOptions != null ? getSegments(filterAndOptions) : getSegments();
            int actualTotalValue = searchResult.getTotal();
            Assert.assertEquals(actualTotalValue, expectedValue, "Segments total is not expected!");

            //correct expected value if max presents in search options
            expectedValue = ((filterAndOptions != null) && (filterAndOptions.getMax() != null))
                    ? filterAndOptions.getMax() : expectedValue;

            int actualSegmentsSize = searchResult.getSegments().length;
            Assert.assertEquals(actualSegmentsSize, expectedValue, "The size of segments is not expected!");

            log.info("Check search segments result passed");
        } catch (IllegalArgumentException | NullPointerException | CommandException e) {
            log.error("Segments search result fails with: {}\n", e.getMessage());
            throw new SkipException("Test skipped", e);
        }
    }

    private void cleanupSegments() throws CommandException {
        GetSegmentResponse getSegmentResponse = getSegments();
        if (getSegmentResponse.getTotal() > 0) {
            log.info("Start segments deletion");
            SegmentModel[] segmentsToDelete = getSegmentResponse.getSegments();
            for (SegmentModel sm : segmentsToDelete) {
                deleteSegment(sm.getId());
            }
        }
    }
}
