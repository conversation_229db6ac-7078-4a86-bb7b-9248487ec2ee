package com.reltio.qa.services;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.constants.DBConstants;
import com.reltio.qa.constants.ErrorMessages;
import com.reltio.qa.enums.common.CleanStorageMethod;
import com.reltio.qa.enums.common.Service;
import com.reltio.qa.enums.devOps.TenantSize;
import com.reltio.qa.enums.match.MatchingStrategy;
import com.reltio.qa.enums.tasks.ExecutionType;
import com.reltio.qa.enums.tasks.PeriodicTasks;
import com.reltio.qa.exceptions.*;
import com.reltio.qa.factories.DatabaseSystemProviderFactory;
import com.reltio.qa.json.JsonDiff;
import com.reltio.qa.model.*;
import com.reltio.qa.model.BusinessModel.BusinessConfigModel;
import com.reltio.qa.model.database.DatabaseResultSet;
import com.reltio.qa.model.platform.SystemStoragesModel;
import com.reltio.qa.model.platform.config.phys.CreditsConfig;
import com.reltio.qa.model.platform.config.phys.EventProcessorsConfig;
import com.reltio.qa.model.platform.config.phys.TableProperties;
import com.reltio.qa.model.platform.config.phys.TenantCreditsConfig;
import com.reltio.qa.model.platform.messaging.MessagingModel;
import com.reltio.qa.model.rdm.RDMConfigIdModel;
import com.reltio.qa.request.Request;
import com.reltio.qa.services.aws.SQSService;
import com.reltio.qa.services.database.system.DatabaseSystemProvider;
import com.reltio.qa.services.devOps.DevOpsService;
import com.reltio.qa.services.devOps.DevOpsTenantService;
import com.reltio.qa.services.gbt.GoogleBigTableService;
import com.reltio.qa.services.nodes.ServiceNodesControllerWrapper;
import com.reltio.qa.services.pms.PlatformManagementTenantService;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformConfigs;
import com.reltio.qa.utils.*;
import com.reltio.qa.utils.database.CosmosDbUtils;
import io.grpc.LoadBalancerRegistry;
import io.grpc.internal.PickFirstLoadBalancerProvider;
import io.restassured.path.json.JsonPath;
import lombok.extern.log4j.Log4j2;

import javax.ws.rs.core.UriBuilder;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Consumer;
import java.util.stream.StreamSupport;

import static com.reltio.qa.Config.*;
import static com.reltio.qa.GlobalConfig.getWaiterUpdateDelay;
import static com.reltio.qa.GlobalConfig.getWaiterUpdateDelaySeconds;
import static com.reltio.qa.constants.EnvVarKeys.*;
import static com.reltio.qa.enums.common.CleanStorageMethod.*;
import static com.reltio.qa.enums.common.DaoType.metadataConfigDAO;
import static com.reltio.qa.enums.common.DaoType.tenantsDAO;
import static com.reltio.qa.request.Request.EXPECTED_HOST_KEY;
import static com.reltio.qa.request.Request.Type.*;
import static com.reltio.qa.services.AccountService.ADMIN_NAME;
import static com.reltio.qa.services.EnvironmentService.ProcessorType.EVENTS;
import static com.reltio.qa.services.EnvironmentService.ProcessorType.MATCH;
import static com.reltio.qa.services.EnvironmentService.getDataprocessService;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForValue;
import static com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformConfigs.waitForTenantConfigurationUpdated;
import static com.reltio.qa.services.smartwaiting.platform.SmartWaitingTenant.waitForTenantEventHubStatsOnAllPods;
import static com.reltio.qa.utils.TimeUtils.sleep;
import static org.glassfish.jersey.internal.guava.Preconditions.checkNotNull;

@Log4j2
public final class TenantManagementService { //NOSONAR: too many methods

    private static final Cache<String, Long> CLEAN_TENANT_TIMESTAMP = CacheBuilder.newBuilder().build();
    private static final int DEFAULT_TASK_GETTING_INTERVAL = 5;
    private static final long DEFAULT_WAIT_TASK_TIME = 400;
    private static final String TENANT_ID = "tenantId";

    private TenantManagementService() {
        throw new IllegalAccessError("Utility class");
    }

    public static void setUpdateDelay(long newUpdateDelay) {
        GlobalConfig.setWaiterUpdateDelay(newUpdateDelay);
    }

    public static long getUpdateDelay() {
        return getWaiterUpdateDelay();
    }

    /**
     * Returns list of tenant ids from the environment
     */
    public static List<String> getTenantsList() {
        try {
            JsonElement tenants = JsonParser.parseString(new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants").execute());
            return GsonUtils.getGson().fromJson(tenants, new TypeToken<List<String>>() {
            }.getType());
        } catch (CommandException e) {
            log.warn("Cannot return list of tenants from the environment: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public static TenantModel getTenant() {
        return getTenant(getTenantId());
    }

    /**
     * @return Additionally try to get config stored in DB directly ignoring cache of the pod
     */
    public static TenantModel getTenant(String tenantId) {
        return getTenant(tenantId, null, true);
    }

    /**
     * @return Additionally try to get config stored in DB directly ignoring cache of the pod
     */
    public static TenantModel getTenant(String tenantId, Account tokenAccount) {
        return getTenant(tenantId, tokenAccount, true);
    }

    /**
     * @return Additionally try to get config stored in DB directly ignoring cache of the pod
     */
    public static TenantModel getTenant(String tenantId, boolean safeMode) {
        return getTenant(tenantId, null, safeMode);
    }

    /**
     * @param safeMode if 'true' (~ ignoreCache = true) then config will be received from DB directly</br>
     *                 if 'false' then Platform will return config from cache of the pod</br>
     *                 <p>
     *                 The code at Platform using the {@code safeMode}:
     *                 <pre>
     *                                                                                                                 {@code
     *                                                                                                                 @Override
     *                                                                                                                 public TenantConfiguration getTenantConfiguration(TenantId tenantId, boolean safeMode) {
     *                                                                                                                     if (safeMode && tenantDataAccess.tenantExists(tenantId) && !tenantDataAccess.isStarted(tenantId)) {
     *                                                                                                                         return tenantDataAccess.getTenantConfiguration(tenantId, true <- ignoreCache );
     *                                                                                                                     } else {
     *                                                                                                                         return tenantDataAccess.getTenantConfiguration(tenantId);
     *                                                                                                                     }
     *                                                                                                                 }
     *                                                                                                                 }
     *                                                                                                                 </pre>
     */
    public static TenantModel getTenant(String tenantId, Account tokenAccount, boolean safeMode) {
        if (tenantId == null || tenantId.isEmpty()) {
            throw new IllegalArgumentException("Incorrect tenantId: '" + tenantId + "'");
        }
        try {
            return TenantModel.fromJson(new Request((tokenAccount == null) ? ADMIN_NAME : tokenAccount.getUsername(), GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "?safeMode=" + safeMode).executeJsonObject());
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on getting tenants: " + e.getMessage(), e);
        }
    }

    /**
     * Secret endpoint<br/>
     * Show messaging models (destinations) used at the tenant
     */
    public static List<MessagingModel> getTenantMessaging(String tenantId) throws CommandException {
        return new Gson().fromJson(new Request(ADMIN_NAME, Request.Type.GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "/messaging").execute(), new TypeToken<List<MessagingModel>>() {
        }.getType());
    }

    public static TenantModel copy(TenantModel original, String newName) {
        return TenantModel.fromJson(original.toString().replace("\":\"" + original.getTenantId(), "\":\"" + newName));
    }

    /**
     * In Platform took deep copy of config with 'safeMode=false' (from cache of the pod) and return 'optionalParameters' of it
     */
    public static Map<String, Object> getOptionalParameters(String tenantId) throws CommandException {
        return new Gson().fromJson(
                new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "/optionalParameters").execute(),
                new TypeToken<TreeMap<String, Object>>() {
                }.getType());
    }

    public static Map<String, Object> getOptionalParameters() throws CommandException {
        return getOptionalParameters(getTenantId());
    }

    public static void putOptionalParameters(String body) {
        log.info("We are trying to set optionalParameters: {}", body);
        try {
            new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + getTenantId() + "/optionalParameters", body).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on put optional parameters: " + e.getMessage(), e);
        }
    }

    public static void putOptionalParameters(Map<String, Object> parameters) {
        putOptionalParameters(GsonUtils.getGson().toJson(parameters));
    }

    public static void putOptionalParameter(String key, Object value) {
        putOptionalParameters(Map.of(key, value));
    }

    public static void deleteOptionalParameters(String parameter) {
        try {
            new Request(ADMIN_NAME, Request.Type.DELETE, Config.getPlatformUrl() + "/tenants/" + getTenantId() + "/optionalParameters/" + parameter).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on delete optional parameters: " + e.getMessage(), e);
        }
    }

    /**
     * Creates tenant from string and returns it as new TenantModel
     *
     * @param tenantPhysConfig
     * @return
     */
    public static TenantModel createTenant(String tenantPhysConfig) {
        return createTenant(tenantPhysConfig, null, true);
    }

    /**
     * Creates tenant from string and returns it as new TenantModel
     *
     * @param tenantPhysConfig
     * @param tokenAccount
     * @return
     */
    public static TenantModel createTenant(String tenantPhysConfig, Account tokenAccount) {
        return createTenant(tenantPhysConfig, tokenAccount, true);
    }

    public static TenantModel createTenant(String tenantPhysConfig, Account tokenAccount, Boolean replaceTenantName) {
        String tenantId = getTenantIdFromConfig(tenantPhysConfig);
        if (tenantId == null || tenantId.isEmpty()) {
            log.error("Tenant '{}' id is incorrect into {} but we have to proceed due to test purposes ...", tenantId, tenantPhysConfig);
        }

        log.info("Tenant '{}' gonna be created/updated ...", tenantId);
        try {
            JsonObject tenantConfigNew = JsonParser.parseString(tenantPhysConfig).getAsJsonObject();

            if (!tenantConfigNew.has("customerName")) {
                tenantConfigNew.addProperty("customerName", "Reltio");
            }

            if (Boolean.TRUE.equals(replaceTenantName)) {
                tenantConfigNew.remove("tenantName");
                tenantConfigNew.addProperty("tenantName", generateInfoForTenantName());
            }
            JsonElement tenantConfiguration = new Request((tokenAccount != null) ? tokenAccount.getUsername() : ADMIN_NAME, POST, getPlatformUrl() + "/tenants?options=allowDataStorageChange", tenantConfigNew.toString()).executeJson();

            // post tenant creation procedures
            waitForTenantConfigurationUpdated(tenantConfigNew);
            waitForTenantEventHubStatsOnAllPods(tenantId);
            AccountService.updateDefaultUserTenants(tenantId);

            log.info("Tenant '{}' was created/updated", tenantId);
            return TenantModel.fromJson(tenantConfiguration);

        } catch (CommandException e) {
            String exceptionMessageNew = String.format("Tenant '%s' wasn't created/updated. Root : %s", tenantId, e.getMessage());
            log.error(exceptionMessageNew, e);
            throw new ApplicationGlobalException(exceptionMessageNew, e);
        }
    }

    private static String getTenantIdFromConfig(String tenantPhysConfig) {
        String tenantId = null;
        try {
            JsonObject tenantPhysConfigJO = JsonParser.parseString(tenantPhysConfig).getAsJsonObject();
            if (tenantPhysConfigJO.has(TENANT_ID)) {
                tenantId = tenantPhysConfigJO.get(TENANT_ID).getAsString();
            }
        } catch (JsonSyntaxException e) {
            log.warn(e.getMessage(), e);
        }
        return tenantId;
    }

    /**
     * Generate tenant physical configuration
     *
     * @param tenantName       - tenant name
     * @param tenantId         - tenant ID
     * @param tenantSize       - tenant size
     * @param matchingStrategy - matching strategy
     * @return tenant physical configuration as String
     * @throws CommandException
     * @throws MalformedURLException
     */
    public static String generateTenantConfig(String tenantName, String tenantId, TenantSize tenantSize, MatchingStrategy matchingStrategy) throws CommandException {
        if (Config.isDevOpsApiEnabled()) {
            return DevOpsTenantService.generateTenantConfig(tenantName, tenantId, tenantSize, matchingStrategy);
        } else if (GlobalConfig.usePMS()) {
            return PlatformManagementTenantService.generateTenantConfig(tenantId, tenantSize, matchingStrategy);
        } else {
            log.info("PMS/DevOpsApi is disabled. Start generating tenant physical configuration from template JSON with default parameters");
            Map<String, Object> vars = new HashMap<>();
            vars.put("TENANT_ID", tenantId);
            vars.put("TENANT_ID_LOW", tenantId.toLowerCase());
            vars.put("TENANT_NAME", tenantName);
            vars.put("CASSANDRA_HOSTS", System.getenv(KEY_ENV_VAR_CASSANDRA_HOSTS));
            vars.put("ES_HOSTS", System.getenv(KEY_ENV_VAR_ES_HOSTS).replace(",", ";"));
            vars.put("CASSANDRA_CLUSTER", System.getenv(KEY_ENV_VAR_ElasticSearchCluster) != null ?
                    System.getenv(KEY_ENV_VAR_CassandraCluster) : "ReltioCluster");
            vars.put("ES_CLUSTER", System.getenv(KEY_ENV_VAR_ElasticSearchCluster) != null ?
                    System.getenv(KEY_ENV_VAR_ElasticSearchCluster) : "ReltioCluster");
            return ParseString.parseString("resources:physConfig.json", vars);
        }
    }

    /**
     * Creates tenant from TenantModel and returns it as new TenantModel
     *
     * @param tenantModel
     * @return
     */
    public static TenantModel createTenant(TenantModel tenantModel) {
        return createTenant(tenantModel.toString());
    }

    /**
     * Creates tenant from TenantModel and returns it as new TenantModel
     *
     * @param tenantModel
     * @param tokenAccount
     * @return
     */
    public static TenantModel createTenant(TenantModel tenantModel, Account tokenAccount) {
        return createTenant(tenantModel.toString(), tokenAccount);
    }

    public static TenantModel createTenant(TenantModel tenantModel, Boolean replaceTenantName) {
        return createTenant(tenantModel.toString(), null, replaceTenantName);
    }

    /**
     * Create a new tenant or recreate deleted one with needed business configuration
     *
     * @param tenantModel         - tenant model
     * @param businessConfigModel - business configuration model
     */
    public static TenantModel createTenantWithConfiguration(TenantModel tenantModel, BusinessConfigModel businessConfigModel) {
        // sleep to avoid "You must wait 60 seconds after deleting a queue before you can create another with the same name."
        sleep(60_000);
        TenantModel tenant = createTenant(tenantModel);
        waitForValue(() ->
                        BusinessConfigurationService.putBusinessConfig(tenantModel.getTenantId(), businessConfigModel).getUri(),
                "configuration",
                "Wait for business config is successfully loaded");
        return tenant;
    }

    /**
     * Clean several tenants in parallel
     *
     * @param tenantName
     */
    public static void cleanTenants(String... tenantName) {
        ForkJoinPool executor = ThreadUtils.threadPool(tenantName.length);

        for (String tenant : tenantName) {
            executor.execute(() -> cleanTenant(tenant));
        }

        ThreadUtils.terminateExecutor(executor);
    }

    /**
     * cleanTenant method recreates all C* keyspaces and all related indexes in ES<br/>
     * This action is done by DevOps API<br/>
     * Please use this method when you want to get tenant "from scratch"<br/>
     * This method is the most preferred method to clean tenant even if we have small count of entities in case when
     * we are applying cardinally different business configuration because method cleanTenantByUris can give
     * unpredictable result in this case<br/>
     * Please use cleanTenantByUris if you want just delete unnecessary entities. It is easier and faster way apart the case above
     *
     * @param tenantId tenantId
     * @return void
     */
    public static void cleanTenant(String tenantId, boolean forceInternal) {
        TenantModel tm = getTenant(tenantId);

        if (Config.isDevOpsApiEnabled()) {
            try {
                long currentTimestamp = System.currentTimeMillis();
                Long prevTimestamp = CLEAN_TENANT_TIMESTAMP.getIfPresent(tenantId);

                if (prevTimestamp != null && currentTimestamp - prevTimestamp < getWaiterUpdateDelay()) {
                    log.info("We are cleaning tenant '{}' too frequently. Waiting {} seconds", tenantId, getWaiterUpdateDelaySeconds());
                    TimeUtils.sleep(getWaiterUpdateDelay());
                }
                DevOpsTenantService.cleanTenant(tenantId);
                CLEAN_TENANT_TIMESTAMP.put(tenantId, System.currentTimeMillis());
                try {
                    if (!GlobalConfig.isSkipHistoryCleanup() && tm.getDataStorageConfig().getHistoryBigTableConfig() != null) {
                        //DevOpsAPI may delete and does not recreate history tables in GBT - simple repost tenant fixes this problem
                        createTenant(tm);
                    }
                } catch (Exception e) {
                    log.error("Repost tenant during its cleaning thrown an exception: {}", e.getMessage(), e);
                }
                return;
            } catch (Exception e) {
                if (getMaintenanceMode(tenantId)) {
                    TenantManagementService.disableMaintenanceMode(tm.getTenantId());
                }
                log.error("DevOpsAPI thrown an exception: {}", e.getMessage(), e);
            }
        }
        Exception tmpExc = null;
        try {
            cleanTenantByUris(tenantId);
            // TODO
            //cleanInternalQueues(tenantId);
        } catch (Exception e) {
            tmpExc = e;
            log.error("cleanTenantByUris thrown an exception:" + e.getMessage(), e);
        }

        boolean deleteByUrisFailed = false;

        if (tmpExc != null) {
            deleteByUrisFailed = tmpExc.getMessage() != null && tmpExc.getMessage().contains("_deleteByUris") && tmpExc.getMessage().contains("ends with the response");
            deleteByUrisFailed = deleteByUrisFailed || (tmpExc.getMessage() != null && tmpExc.getMessage().contains("EVENT_POPULATION_EXCEPTION"));
        }

        boolean cleanByInternal = GlobalConfig.getEnvPropertyAsBoolean("USE_CLEAN_TENANT_INTERNAL", false) || forceInternal;

        if (deleteByUrisFailed || cleanByInternal) {
            cleanTenantInternal(tm, true);
        } else {
            if (tmpExc != null) {
                throw new ApplicationGlobalException(tmpExc.getMessage(), tmpExc);
            }
        }
    }

    public static void cleanTenant(String tenantId) {
        cleanTenant(tenantId, false);
    }

    /**
     * cleanTenantByUris method deletes entities which were returned by dbscan endpoint<br/>
     * Please use this method when you want to get tenant with no entities<br/>
     * Actually all activities and other objects can be there which were before this clean<br/>
     * Please use cleanTenant if you want to get tenant from scratch<br/>
     * Don't use this method if tenant have many entities because of performance can be low.<br/>
     * This method is the least preferred method to clean tenant even if we have small count of entities in case when
     * we are applying cardinally different business configuration - please use cleanTenant instead<br/>
     * This method uses EntityService.deleteEntitiesByUris - delete entities by pack of uris (max 150 for one request)<br/>
     * After deleting the method waits for queues are empty and then run consistency (C* - ES) task with fix inconsistency
     *
     * @param tenantId - tenant id
     * @return void
     */
    public static void cleanTenantByUris(String tenantId) throws CommandException {
        cleanTenantByUris(tenantId, true, true);
    }

    public static void cleanTenantByUris() throws CommandException {
        cleanTenantByUris(getTenantId(), true, true);
    }

    /**
     * cleanTenantByUris method deletes entities which were returned by dbscan endpoint<br/>
     * Please use this method when you want to get tenant with no entities<br/>
     * Actually all activities and other objects can be there which were before this clean<br/>
     * Please use cleanTenant if you want to get tenant from scratch<br/>
     * Don't use this method if tenant have many entities because of performance can be low.<br/>
     * This method is the least preffered method to clean tenant even if we have small count of entities in case when
     * we are applying cardinally different business configuration - please use cleanTenant instead<br/>
     * This method uses EntityService.deleteEntitiesByUris - delete entities by pack of uris (max 150 for one request)
     *
     * @param tenantId             tenant id
     * @param withWaitingOfQueue   if true - after the deleting entities wait status for zero numbers of queues
     * @param withConsistencyCheck if true - run consistency check with fix inconsistency=true. This quarantees us that C* and ES are in consistent state after massive deleting
     * @return void
     */
    public static void cleanTenantByUris(String tenantId, boolean withWaitingOfQueue, boolean withConsistencyCheck) throws CommandException {
        log.info("Start to clean tenant '" + tenantId + "' by 'TenantManagementService.cleanTenantByUris(.*)' ...");

        DBscanModel dbScan = new DBscanModel(new DBscanModel.BodyOfDBscan().returnObjects(Boolean.FALSE).pageSize(200)).select("uri").entities().executeDBscan(Config.getTenantUrl(tenantId));
        List<String> listUris = GsonUtils.getGson().fromJson(dbScan.getReturnedObjects(), new TypeToken<List<String>>() {
        }.getType());

        EntityService.deleteEntitiesByUris(Config.getTenantUrl(tenantId), listUris, true);

        if (withWaitingOfQueue) {
            SmartWaiting.waitForEmptyMatchEventsQueue(tenantId);
        }

        if (withConsistencyCheck) {
            PostTaskRequestParameters paramsMatch = new PostTaskRequestParameters()
                    .setTenantId(tenantId)
                    .user(ADMIN_NAME)
                    .fixInconsistency(Boolean.TRUE)
                    .fixVersionConflicts(Boolean.TRUE)
                    .compareVersions(Boolean.TRUE);

            try {
                TaskService.esCassandraConsistencyCheck(ExecutionType.TENANT, paramsMatch).waitWhileRunning(DEFAULT_TASK_GETTING_INTERVAL, DEFAULT_WAIT_TASK_TIME);
            } catch (WaitException | ReltioObjectException e) {
                if (e.getMessage().contains("COMPLETED_WITH_ERRORS")) {
                    log.warn("Task esCassandraConsistencyCheck finished with status COMPLETED_WITH_ERRORS");
                    // TODO rerun to check that inconsistency was fixed
                } else {
                    throw new ApplicationGlobalException(e);
                }
            }

            try {
                TaskService.matchCassandraConsistencyCheck(ExecutionType.TENANT, paramsMatch).waitWhileRunning(DEFAULT_TASK_GETTING_INTERVAL, DEFAULT_WAIT_TASK_TIME);
            } catch (WaitException | ReltioObjectException e) {
                if (e.getMessage().contains("COMPLETED_WITH_ERRORS")) {
                    log.warn("Task matchCassandraConsistencyCheckViaTenantUri finished with status COMPLETED_WITH_ERRORS");
                    // TODO rerun to check that inconsistency was fixed
                } else {
                    throw new ApplicationGlobalException(e);
                }
            }
        }

        log.info("Tenant '{}' was cleaned", tenantId);
    }

    public static void cleanTenant() {
        cleanTenant(getTenantId());
    }

    public static void addLookups(String lookups) throws CommandException {
        addLookups(getTenantId(), lookups);
    }

    /**
     * This method is used for deleting tenants if DevOpsApi is disabled
     *
     * @param tenantId tenant that you want to delete
     */
    public static void deleteTenantInternal(String tenantId) {
        ApplicationGlobalException commonException = new ApplicationGlobalException("There are errors during deleting tenant '" + tenantId + "'");

        log.info("Start deleting tenant '" + tenantId + "' by 'TenantManagementService.deleteTenantInternal(.*)' ...");

        TenantModel tm;
        try {
            tm = getTenant(tenantId);
        } catch (Exception e) {
            if (!e.getMessage().contains("Tenant " + tenantId + " not configured. Use admin API to configure tenant")) {
                throw e;
            } else {
                log.info("It seems tenant already deleted: " + e.getMessage());
                return;
            }
        }
        //Enable maintenance mode
        enableMaintenanceMode(tenantId);

        try {
            cleanCrudInternalQueue(tenantId, true);
            cleanMatchInternalQueue(tenantId, true);
        } catch (Exception e) {
            if (e.getMessage().contains("The specified queue does not exist for this wsdl version")) {
                log.warn(e.getMessage(), e);
            } else {
                commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting queues: " + e.getMessage(), e));
            }
        }

        try {
            ESHTTPService.deleteIndexes(tenantId);
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting ES indexes: " + e.getMessage(), e));
        }

        log.info("Start dropping tenant from database ...");
        try {
            if (tm.hasDynamoDBDataStorage()) {
                TenantManagementService.cleanDynamoDBStorage(tenantId, DROP_TABLES);
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting dynamoDB tables: " + e.getMessage(), e));
        }

        try {
            if (tm.hasSpannerDBDataStorage()) {
                TenantManagementService.cleanSpannerDBStorage(tenantId, DROP_TABLES);
                DevOpsService.deleteSpannerDBResources(tm.getDataStorageConfig().getDataStorages().getFirst().getSpannerDBConfig().getDatabaseId());
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting Spanner database: " + e.getMessage(), e));
        }

        try {
            if (tm.hasCosmosDBStorage()) {
                TenantManagementService.cleanCosmosDBStorage(tenantId, DROP_TABLES);
                CosmosDbUtils.deleteCosmosDbResources(CosmosDbUtils.getCosmosAccountName(tenantId));
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting Cosmos database: " + e.getMessage(), e));
        }

        try {
            if (tm.hasCassandraDBDataStorage() || tm.hasCassandraDBMatchStorage()) {
                try (CassandraService cassandraService = new CassandraService(tm)) {
                    if (tm.hasCassandraDBDataStorage()) {
                        cassandraService.dropKeyspace(tm.getDataStorageConfig().getDataKeyspaceConfig().getKeyspaceName());
                    }
                    if (tm.hasCassandraDBMatchStorage()) {
                        cassandraService.dropKeyspace(tm.getDataStorageConfig().getMatchKeyspaceConfig().getKeyspaceName());
                    }
                    if (tm.getDataStorageConfig().getHistoryKeyspaceConfig() != null) {
                        cassandraService.dropKeyspace(tm.getDataStorageConfig().getHistoryKeyspaceConfig().getKeyspaceName());
                    }
                }
            }
            SystemStoragesModel systemStorages = EnvironmentService.getSystemStorages();

            try (DatabaseSystemProvider<DatabaseResultSet> dbTenantProvider = DatabaseSystemProviderFactory.getDbSysTestProvider(tenantsDAO, systemStorages);
                 DatabaseSystemProvider<DatabaseResultSet> dbMetaProvider = DatabaseSystemProviderFactory.getDbSysTestProvider(metadataConfigDAO, systemStorages)) {

                log.info("Start deleting tenant data from SYSTEM KEYSPACE ...");

                dbMetaProvider.deleteMetaConfig(tenantId); //Delete tenant business config
                dbTenantProvider.deleteTenantConfig(tenantId); //Delete tenant phys config
            }
            //This part can be removed after system keyspace migration finished--
            if (systemStorages.getDaos().get(tenantsDAO.toString()).getType().startsWith(DBConstants.MIGRATION)) {
                try (DatabaseSystemProvider<DatabaseResultSet> dbTenantProviderSecondary = DatabaseSystemProviderFactory.getDbSysTestProvider(tenantsDAO, systemStorages, false)) {

                    log.info("Start deleting tenant data from secondary SYSTEM KEYSPACE ...");

                    dbTenantProviderSecondary.deleteTenantConfig(tenantId); //Delete tenant phys config
                }
            }
            if (systemStorages.getDaos().get(metadataConfigDAO.toString()).getType().startsWith(DBConstants.MIGRATION)) {
                try (DatabaseSystemProvider<DatabaseResultSet> dbMetaProviderSecondary = DatabaseSystemProviderFactory.getDbSysTestProvider(metadataConfigDAO, systemStorages, false)) {

                    log.info("Start deleting tenant business config data from secondary SYSTEM KEYSPACE ...");

                    dbMetaProviderSecondary.deleteMetaConfig(tenantId); //Delete tenant business config
                }
            }
            //--
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting tenant: " + e.getMessage(), e));
        }

        if (commonException.getSuppressed() != null && commonException.getSuppressed().length > 0) {
            throw commonException;
        } else {
            log.info("Tenant '{}' was deleted", tenantId);
        }
    }

    /**
     * This method is used for deleting tenants via DevOpsApi or internal functional without throwing any Exceptions
     *
     * @param tenantId tenant that you want to delete
     */
    public static void deleteTenantUnchecked(String tenantId) {
        boolean tenantDeleted = false;

        if (Config.isDevOpsApiEnabled()) {
            // In case of CosmosDb, get the account name before deleting the tenant
            var cosmosDbAccountName = GlobalConfig.useCosmosDB()
                    ? CosmosDbUtils.getCosmosAccountName(tenantId)
                    : "";

            try {
                DevOpsTenantService.deleteTenant(tenantId);
                tenantDeleted = true;
            } catch (Exception e) {
                log.warn("Can not delete tenant '{}' by DevOps API due to exception: {}", tenantId, e.getMessage(), e);
            }

            // Clean CosmosDb resources if they were created
            if (!cosmosDbAccountName.isEmpty()) {
                CosmosDbUtils.deleteCosmosDbResources(cosmosDbAccountName);
            }

            // Clean ServiceBus resources if they were created
            if (GlobalConfig.useServiceBusStandardNamespace()) {
                try {
                    DevOpsService.deleteServiceBusResources(tenantId, getEnvName() + "-" + tenantId);
                } catch (CommandException e) {
                    log.warn("Can not delete Azure Service Bus Namespace for tenant '{}' by DevOps API due to exception: {}", tenantId, e.getMessage(), e);
                }
            }
        }

        if (!tenantDeleted) {
            try {
                deleteTenantInternal(tenantId);
            } catch (Exception e) {
                log.warn("Can not delete tenant '{}' by internal tool due to exception: {}", tenantId, e.getMessage(), e);
            }
        }
    }

    /**
     * cleanTenantInternal method was created as an alternative way to get tenant from scratch<br/>
     * Please use this only when you need tenant from scratch and for some reason cleanTenant does not work<br/>
     * If possible just omit run tests until cleanTenant will work<br/>
     * This method recreates C* keyspaces and ES indexes, but does not do any checks on C*, ES level<br/>
     * Will be used if USE_CLEAN_TENANT_INTERNAL is set to true or if called with forceInternal flag
     *
     * @param tenantModel   - tenant model
     * @param forceInternal - if true the method will be called, else - skipped with exception
     * @return void
     */
    public static void cleanTenantInternal(TenantModel tenantModel, boolean forceInternal) {
        if (!forceInternal) {
            throw new ApplicationGlobalException("Either set environment variable USE_CLEAN_TENANT_INTERNAL to 'true' or use 'forceInternal' flag if you want to call cleanTenantInternal");
        }

        String tenantId = tenantModel.getTenantId();
        ApplicationGlobalException commonException = new ApplicationGlobalException("There are errors during cleaning tenant '" + tenantId + "'");
        log.info("Start cleaning tenant '" + tenantId + "' by 'TenantManagementService.cleanTenantInternal(.*)' ...");

        try {
            ESHTTPService.deleteIndexes(tenantId);
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on deleting ES indices: " + e.getMessage(), e));
        }

        boolean hasCassandraHistoryStorage = tenantModel.getDataStorageConfig().getHistoryKeyspaceConfig() != null;
        try {
            if (tenantModel.hasCassandraDBDataStorage() || tenantModel.hasCassandraDBMatchStorage()) {
                try (CassandraService cassandraService = new CassandraService(tenantModel)) {
                    if (tenantModel.hasCassandraDBDataStorage()) {
                        cassandraService.truncateKeyspace(tenantModel.getDataStorageConfig().getDataKeyspaceConfig().getKeyspaceName());
                    }
                    if (tenantModel.hasCassandraDBMatchStorage()) {
                        cassandraService.truncateKeyspace(tenantModel.getDataStorageConfig().getMatchKeyspaceConfig().getKeyspaceName());
                    }
                    if (hasCassandraHistoryStorage) {
                        cassandraService.truncateKeyspace(tenantModel.getDataStorageConfig().getHistoryKeyspaceConfig().getKeyspaceName());
                    }
                }
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning C*: " + e.getMessage(), e));
        }

        try {
            if (tenantModel.hasDynamoDBMatchStorage() || tenantModel.hasDynamoDBDataStorage() || tenantModel.hasDynamoDBHistoryStorage()) {
                if (!TenantManagementService.cleanDynamoDBStorage(tenantId, CLEAN_ITEMS)) {
                    commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning Dynamo DB: " + tenantId));
                }
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning Dynamo DB*: " + e.getMessage(), e));
        }

        try {
            if (tenantModel.hasSpannerDBDataStorage() || tenantModel.hasSpannerDBMatchStorage()) {
                if (!TenantManagementService.cleanSpannerDBStorage(tenantId, CLEAN_ITEMS)) {
                    commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning Spanner DB: " + tenantId));
                }
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning Spanner DB: " + e.getMessage(), e));
        }

        try {
            if (tenantModel.hasCosmosDBStorage()) {
                if (!TenantManagementService.cleanCosmosDBStorage(tenantId, CLEAN_ITEMS)) {
                    commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning Cosmos DB: " + tenantId));
                }
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning Cosmos DB: " + e.getMessage(), e));
        }

        try {
            if (!hasCassandraHistoryStorage) {
                log.info("Start cleaning history in GBT ...");
                try (GoogleBigTableService gbt = new GoogleBigTableService(tenantModel.getDataStorageConfig().getHistoryBigTableConfig().getProject(),
                        tenantModel.getDataStorageConfig().getHistoryBigTableConfig().getInstanceId())) {
                    LoadBalancerRegistry.getDefaultRegistry().register(new PickFirstLoadBalancerProvider());
                    checkNotNull(LoadBalancerRegistry.getDefaultRegistry().getProvider("pick_first"),
                            "pick_first balancer not available");
                    gbt.cleanTable(tenantModel.getDataStorageConfig().getHistoryBigTableConfig().getTableName());
                }
            }
        } catch (Exception e) {
            commonException.addSuppressed(new ApplicationGlobalException("An error occurred on cleaning GBT: " + e.getMessage(), e));
        }

        if (commonException.getSuppressed() != null && commonException.getSuppressed().length > 0) {
            throw commonException;
        } else {
            log.info("Tenant '{}' was cleaned", tenantId);
        }
    }

    /**
     * Clean tenant queues if IsExternalQueue=true
     * Use DevOpsApi if it is enabled, else clean in AWS through Platform
     *
     * @param tenantId tenant ID
     * @throws Exception
     */
    public static void cleanInternalQueues(String tenantId) throws Exception {
        ReltioStatusModel status = getTenantStatus(tenantId);
        if (status.isExternalQueue()) {
            if (Config.isDevOpsApiEnabled()) {
                DevOpsTenantService.cleanInternalQueues(tenantId);
                log.info("Wait. Internal queues for tenant '" + tenantId + "' were cleaned by DevOpsApi");
            } else {
                purgeInternalQueues(tenantId);
                log.info("Wait. Internal queues for tenant '" + tenantId + "' were cleaned");
            }
        } else {
            log.warn("IsExternalQueue=false for tenant '" + tenantId + "'. Can't clean internal queues.");
        }
    }

    /**
     * Get status of tenant
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static ReltioStatusModel getTenantStatus(String tenantId) throws CommandException {
        return ReltioStatusModel.fromJson(new Request(ADMIN_NAME, Request.Type.GET, Config.getPlatformUrl() + "/status/tenant/" + tenantId).execute());
    }

    /**
     * Clean tenant queues in AWS AWS directly
     * Used AWS credentials should have appropriate permission
     *
     * @param tenantId     tenant ID
     * @param withDeleting true if you want to delete queues, false if you want to purge queues
     */
    public static void cleanSQS(String tenantId, boolean withDeleting) throws CommandException {
        BasicAWSCredentials basicAWSCredentials = GlobalConfig.getBasicAWSCredentials();
        Regions region = getAwsRegion();
        SQSService sqsService = new SQSService(basicAWSCredentials, region);
        List<String> queuesList = new ArrayList<>();
        String prefix;
        if (EnvironmentService.getSystemApiConfig("eventhub.mode") != null &&
                EnvironmentService.getSystemApiConfig("eventhub.mode").equals("aws")) {
            prefix = EnvironmentService.getSystemApiConfig("eventhub.prefix.pattern");
            queuesList.add(sqsService.getQueueUrl(String.format(prefix, "crud") + tenantId));
            queuesList.add(sqsService.getQueueUrl(String.format(prefix, "match") + tenantId));
        } else {
            prefix = EnvironmentService.getSystemApiConfig("eventhub.crud.sqs.qprefix");
            queuesList.add(sqsService.getQueueUrl(prefix + tenantId));
            prefix = EnvironmentService.getSystemApiConfig("eventhub.match.sqs.qprefix");
            queuesList.add(sqsService.getQueueUrl(prefix + tenantId));
        }
        if (withDeleting) {
            sqsService.deleteQueues(queuesList);
        } else {
            sqsService.purgeQueues(queuesList);
        }
    }

    public static void purgeInternalQueues(String tenantId) {
        cleanCrudInternalQueue(tenantId, false);
        cleanMatchInternalQueue(tenantId, false);
        clearStreamingDestinationsQueues(tenantId, false);
    }

    public static void cleanCrudInternalQueue(String tenantId, boolean withDeleting) {
        try {
            String resp = new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantId + "/clearCrudInternalQueues" + "?delete=" + withDeleting).execute();
            JsonElement json = JsonParser.parseString(resp).getAsJsonObject();
            if (!GsonUtils.getByPath(json, "successful").getAsBoolean()) {
                String err = GsonUtils.getByPath(json, "errors/errorMessage").getAsString();
                throw new CommandException(err);
            }
        } catch (CommandException e) {
            String process = withDeleting ? "deleting" : "purging";
            throw new ApplicationGlobalException("An error occurred on " + process + " tenant queue:\n" + e.getMessage(), e);
        }
    }

    public static void cleanMatchInternalQueue(String tenantId, boolean withDeleting) {
        try {
            String resp = new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantId + "/clearMatchInternalQueues" + "?delete=" + withDeleting).execute();
            JsonElement json = JsonParser.parseString(resp).getAsJsonObject();
            if (!GsonUtils.getByPath(json, "successful").getAsBoolean()) {
                String err = GsonUtils.getByPath(json, "errors/errorMessage").getAsString();
                throw new CommandException(err);
            }
        } catch (CommandException e) {
            String process = withDeleting ? "deleting" : "purging";
            throw new ApplicationGlobalException("An error occurred on " + process + " tenant queue:\n" + e.getMessage(), e);
        }
    }

    /**
     * clear/delete streaming all queues from destinations of current tenant
     */
    public static void clearStreamingDestinationsQueues() {
        clearStreamingDestinationsQueues(getTenantId(), false);
    }

    /**
     * clear/delete streaming all queues from destinations
     *
     * @param tenantId
     * @param withDeleting - default false, set true if you need delete queue
     */
    public static void clearStreamingDestinationsQueues(String tenantId, boolean withDeleting) {
        try {
            String resp = new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantId + "/clearStreamingDestinations" + "?delete=" + withDeleting).execute();
            JsonElement json = JsonParser.parseString(resp).getAsJsonObject();
            for (Map.Entry<String, JsonElement> queueEntry : json.getAsJsonObject().entrySet()) {
                if (!queueEntry.getValue().getAsString().equals("Ok")) {
                    throw new CommandException("Unsuccessfully clearing of streaming queue: " + resp);
                }
            }
        } catch (CommandException e) {
            String process = withDeleting ? "deleting" : "purging";
            throw new ApplicationGlobalException("An error occurred on " + process + " tenant queue:\n" + e.getMessage(), e);
        }
    }

    public static void updateRatingConfigurationSources(String config) {
        updateRatingConfigurationSources(getTenantId(), config);
    }

    public static void updateRatingConfigurationSources(String tenantName, String config) {
        updateRatingConfiguration(tenantName, config, "sources");
    }

    public static void updateRatingConfigurationUserRoles(String config) {
        updateRatingConfigurationUserRoles(getTenantId(), config);
    }

    public static void updateRatingConfigurationUserRoles(String tenantName, String config) {
        updateRatingConfiguration(tenantName, config, "userRoles");
    }

    private static void updateRatingConfiguration(String tenantName, String config, String type) {
        try {
            new Request(ADMIN_NAME, PUT, Config.getApiUrl() + tenantName + "/ratingConfiguration/" + type, config).execute();
            SmartWaitingPlatformConfigs.waitRatingConfigUpdated(tenantName);
            log.info("Rating configuration is updated. Tenant: " + tenantName);
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on updating rating configuration", e);
        }
    }

    private static JsonElement setLookupToSync(String lookups, String lookupValue) {
        JsonElement oldLookups = JsonParser.parseString(lookups);
        JsonObject lookupLabel = new JsonObject();
        JsonObject lookup = new JsonObject();
        lookup.addProperty("displayName", lookupValue);
        lookupLabel.add("TestSyncLookup", lookup);
        oldLookups.getAsJsonObject().add("TestSyncLookup", lookupLabel);
        return oldLookups;
    }

    public static List<TenantHistoryModel> getTenantHistory(String tenantName) throws ReltioObjectException {
        JsonElement response;
        try {
            response = new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantName + "/_history").executeJson();
        } catch (CommandException e) {
            throw new ReltioObjectException("An error occurred on getting tenant history:\n", e);
        }
        if (response.isJsonArray()) {
            return TenantHistoryModel.parseList(response.getAsJsonArray());
        } else {
            throw new ReltioObjectException("Unable get tenant history, response: " + response);
        }
    }

    public static void deleteLookups(String... tenantName) {
        ForkJoinPool executor = ThreadUtils.threadPool(tenantName.length);
        for (String tenant : tenantName) {
            executor.execute(() -> {
                try {
                    new Request(ADMIN_NAME, DELETE, Config.getApiUrl() + tenant + "/lookups").execute();
                    SmartWaitingPlatformConfigs.waitLookupsDeleted(tenant);
                    log.info("Lookups for tenant '{}' are deleted.", tenant);
                } catch (CommandException e) {
                    log.error(String.format("Lookups for tenant '%s' were not deleted.", tenant), e);
                }
            });
        }
        ThreadUtils.terminateExecutor(executor, "Something wrong with deleting lookups. This is a blocker for further processing.", 1000);
    }

    /**
     * Updates lookups on specified tenant
     *
     * @param tenantName name of a tenant
     * @param lookups    lookups in LookupModel representation
     */
    public static void updateLookups(String tenantName, LookupModel lookups) {
        updateLookups(tenantName, lookups.toString());
    }

    /**
     * Updates lookups on specified tenant
     *
     * @param tenantName name of a tenant
     * @param lookups    lookups in string representation
     */
    public static void updateLookups(String tenantName, String lookups) {
        try {
            String time = Long.toString(System.currentTimeMillis());
            new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantName + "/lookups", setLookupToSync(lookups, time).toString()).execute();
            SmartWaitingPlatformConfigs.waitLookupsUpdated(tenantName, time);
            log.info("Lookups for tenant '{}' are updated.", tenantName);
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on posting tenant lookups:\n", e);
        }
    }

    /**
     * Updates lookups on current tenant
     *
     * @param lookups lookups in LookupModel representation
     */
    public static void updateLookups(String lookups) {
        updateLookups(getTenantId(), lookups);
    }

    /**
     * Resolve lookup value by code on specified tenant. Returns lookup value for the specified lookup code
     *
     * @param tenantName name of a tenant
     * @param body       string of JSON object with the lookup code
     * @return JSON object with lookup value
     */
    public static JsonElement resolveLookups(String tenantName, String body) {
        try {
            return new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantName + "/lookups/resolve", body).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on resolving tenant lookups: " + e.getMessage(), e);
        }
    }

    /**
     * Resolve lookup value by code on current tenant. Returns lookup value for the specified lookup code
     *
     * @param body string of JSON object with the lookup code
     * @return JSON object with lookup value
     */
    public static JsonElement resolveLookups(String body) {
        return resolveLookups(getTenantId(), body);
    }

    /**
     * Resolve lookup value by code on specified tenant. Returns lookup value for the specified lookup code
     *
     * @param tenantName name of a tenant
     * @param type       string with lookups type
     * @param codeValue  string with lookup value
     * @return JSON object with lookup value
     */
    public static JsonElement resolveLookups(String tenantName, String type, String codeValue) {
        try {
            JsonElement requestBody = new JsonObject();
            requestBody.getAsJsonObject().addProperty("type", type);
            requestBody.getAsJsonObject().addProperty("codeValue", codeValue);
            return new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantName + "/lookups/resolve", requestBody.toString()).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on resolving tenant lookups:\n", e);
        }

    }

    /**
     * Resolve lookup value by code on current tenant. Returns lookup value for the specified lookup code
     *
     * @param type      string with lookups type
     * @param codeValue string with lookup value
     * @return JSON object with lookup value
     */
    public static JsonElement resolveLookupsByTypeValue(String type, String codeValue) {
        try {
            JsonElement requestBody = new JsonObject();
            requestBody.getAsJsonObject().addProperty("type", type);
            requestBody.getAsJsonObject().addProperty("codeValue", codeValue);
            return new Request(ADMIN_NAME, POST, Config.getApiUrl() + getTenantId() + "/lookups/resolve", requestBody.toString()).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on resolving tenant lookups:\n", e);
        }

    }

    /**
     * Method returns list of tenants lookups for user with proper permissions
     *
     * @param account    user account authorized to make the call with proper permissions
     * @param tenantName tenant ID
     * @param body       body for request
     * @return list of lookups as JsonElement
     */
    public static JsonElement getLookupsList(Account account, String tenantName, String body) {
        try {
            return new Request(account.getUsername(), POST, Config.getApiUrl() + tenantName + "/lookups/list", body).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on getting tenant lookups list:\n", e);
        }
    }

    /**
     * Method returns list of tenant lookups for current user
     *
     * @param tenantName tenant ID
     * @param body       body for request
     * @return list of lookups as JsonElement
     */
    public static JsonElement getLookupsList(String tenantName, String body) {
        return getLookupsList(AccountService.getAdminAccount(), tenantName, body);
    }

    /**
     * Method returns list of current tenant lookups for current user
     *
     * @param body body for request
     * @return list of lookups as JsonElement
     */
    public static JsonElement getLookupsList(String body) {
        return getLookupsList(getTenantId(), body);
    }

    /**
     * Resolve an array of lookup codes for specified tenant. In case if such code doesn't exist - it is not present in response.
     *
     * @param tenantName name of a tenant
     * @param body       string of JSON object with the lookup codes
     * @return JSON object with lookup value
     */
    public static JsonElement resolveLookupsList(String tenantName, String body) {
        try {
            return new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantName + "/lookups/resolveList", body).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on resolving tenant lookups:\n", e);
        }

    }

    /**
     * Resolve an array of lookup codes for current tenant. In case if such code doesn't exist - it is not present in response.
     *
     * @param body string of JSON object with the lookup codes
     * @return JSON object with lookup value
     */
    public static JsonElement resolveLookupsList(String body) {
        return resolveLookupsList(getTenantId(), body);
    }

    /**
     * Validate current lookups structure on specified tenant
     * Checks:
     * code exists
     * hierarchy - code has such a parent (union - at least one of the parents)
     * In response a codes that hasn't pass validation will have "Error" tag.
     *
     * @param tenantName name of a tenant
     * @param body       string of JSON object with the lookup codes
     * @return JSON object with lookup value
     */
    public static JsonElement validateLookups(String tenantName, String body) {
        try {
            return new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantName + "/lookups/validate", body).executeJson();
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on validating tenant lookups:\n", e);
        }
    }

    /**
     * Validate current lookups structure on current tenant
     * Checks:
     * code exists
     * hierarchy - code has such a parent (union - at least one of the parents)
     * In response a codes that hasn't pass validation will have "Error" tag.
     *
     * @param body string of JSON object with the lookup codes
     * @return JSON object with lookup value
     */
    public static JsonElement validateLookups(String body) {
        return validateLookups(getTenantId(), body);
    }

    /**
     * Add new lookups to existing lookups on specified tenant
     *
     * @param tenantName name of a tenant
     * @param lookups    string of JSON object with the lookups
     */
    public static void addLookups(String tenantName, String lookups) throws CommandException {
        String time = Long.toString(System.currentTimeMillis());
        Map<String, List<Object>> diff = new JsonDiff(getLookups(tenantName), lookups).getDifference();
        if (!diff.get("lostElements").isEmpty() || diff.get("differentElements").isEmpty()) {
            new Request(ADMIN_NAME, PUT, Config.getApiUrl() + tenantName + "/lookups", setLookupToSync(lookups, time).toString()).execute();
            SmartWaitingPlatformConfigs.waitLookupsUpdated(tenantName, time);
            log.info("Lookups were added");
        } else {
            log.info("Lookups already exist");
        }
    }

    /**
     * Get lookups from specified tenant for specified user
     *
     * @param tenantName name of a tenant
     * @param user       user name
     */
    public static String getLookups(String tenantName, String user) throws CommandException {
        return new Request(user, GET, Config.getApiUrl() + tenantName + "/lookups").execute();
    }

    /**
     * Get lookups from specified tenant for specified user
     *
     * @param tenantName name of a tenant
     */
    public static String getLookups(String tenantName) throws CommandException {
        return getLookups(tenantName, null);
    }

    /**
     * Get lookups from current tenant
     */
    public static String getLookups() throws CommandException {
        return getLookups(getTenantId(), null);
    }

    // Work with "cleanse" section

    @Deprecated
    public static String setReturnUnverifiedStatus(Boolean returnUnverifiedStatus) throws ReltioObjectException {
        CleanseModel cleanseFunctionLoqate = CleanseService.getCleanseFunction(getTenantId(), "Loqate");
        if (cleanseFunctionLoqate == null) {
            return "Loqate function does not exist at physical configuration of the tenant (" + getTenantId() + ")";
        }
        cleanseFunctionLoqate.getOptions().setReturnUnverifiedStatus(returnUnverifiedStatus);
        putCleanseFunction(getTenantId(), cleanseFunctionLoqate);
        return "";
    }

    /**
     * Will add new or replace already existed {{cleanse}} cleanse function with its' parameters at current tenantModel<br>
     * It will set new changes at the environment via API request<br>
     * PUT {{tenantURI}}//cleanse
     *
     * @param tenantName
     * @param cleanseFunction CleanseModel with parameters such as "cleanseFunction" ...
     * @throws ReltioObjectException
     */
    public static void putCleanseFunction(String tenantName, CleanseModel cleanseFunction) throws ReltioObjectException {
        CleanseService.putCleanseFunction(tenantName, cleanseFunction);
    }

    /**
     * Will replace already existed "cleanse" section by new data from List {{cleanseFunctions}} with its' parameters at current tenantModel<br>
     * It will set new changes at the environment via API request<br>
     * PUT {{tenantURI}}/cleanse
     *
     * @param tenantName
     * @param cleanseFunctions List of CleanseModels with parameters such as "cleanseFunction" ...
     * @throws ReltioObjectException
     */
    public static void overrideCleanseSection(String tenantName, List<CleanseModel> cleanseFunctions) throws ReltioObjectException {
        CleanseService.overrideCleanseSection(tenantName, cleanseFunctions);
    }

    /**
     * Will add new or replace already existed {{cleanseFunctions}} with its' parameters at current tenantModel<br>
     * It will do changes at the environment via API request<br>
     * PUT {{tenantURI}}/cleanse
     *
     * @param tenantName
     * @param cleanseFunctions List of CleanseModel, each of them should have parameter "cleanseFunction" ...
     * @throws ReltioObjectException
     */
    public static void putCleanseFunctions(String tenantName, List<CleanseModel> cleanseFunctions) throws ReltioObjectException {
        CleanseService.putCleanseFunctions(tenantName, cleanseFunctions);
    }

    /**
     * Will get cleanse function {{cleanseFunction}} from physical configuration of the {{tenantName}} by<br>
     * GET {{tenantURI}}/cleanse
     *
     * @param tenantName
     * @return
     * @throws ReltioObjectException
     */
    public static List<CleanseModel> getCleanseFunctions(String tenantName) throws ReltioObjectException {
        return CleanseService.getCleanseFunctions(tenantName);
    }

    /**
     * Will get cleanse function {{nameOfCleanseFunction}} from physical configuration of the {{tenantName}} by<br>
     * GET {{tenantURI}}/cleanse
     *
     * @param tenantName
     * @param nameOfCleanseFunction
     * @return
     * @throws ReltioObjectException
     */
    public static CleanseModel getCleanseFunction(String tenantName, String nameOfCleanseFunction) throws ReltioObjectException {
        return CleanseService.getCleanseFunction(tenantName, nameOfCleanseFunction);
    }

    public static Boolean postDataloadMode(String tenantName, boolean mode) throws ReltioObjectException {

        if (tenantName == null || tenantName.isEmpty()) {
            tenantName = getTenantId();
        }

        String body = String.format("{ \"streamingAPIEnabled\": %s }", mode);

        try {
            return new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + tenantName + "/dataloadMode", body).execute().contains(ModelConstants.SUCCESS);
        } catch (CommandException e) {
            throw new ReltioObjectException("Unable put dataloadMode, message " + e.getMessage(), e);
        }
    }

    public static TenantModel putTenantName(String tenantId, String newTenantName) throws CommandException {
        new Request(ADMIN_NAME, PUT, String.format("%s/tenants/%s/name", Config.getPlatformUrl(), tenantId), String.format("{\"value\":\"%s\"}", newTenantName)).execute();
        waitForTenantConfigurationUpdated(tenantId);
        return getTenant(tenantId);
    }

    public static String getTenantNameByRequest(String tenantId) throws CommandException {
        JsonElement je = new Request(ADMIN_NAME, GET, String.format("%s/tenants/%s/name", Config.getPlatformUrl(), tenantId)).executeJson();
        return je.getAsJsonObject().get("value").getAsString();
    }

    /**
     * Get list of RDM tenants from physical configuration
     *
     * @param username
     * @param tenantId
     * @return list of RDMConfigIds
     */
    public static List<RDMConfigIdModel> getListOfRdmTenants(String username, String tenantId) {
        List<RDMConfigIdModel> rdmTenants = new ArrayList<>();
        String url = Config.getPlatformUrl() + "/tenants/" + tenantId + "/rdmTenants";
        try {
            JsonArray response = (new Request(username, GET, url).executeJson()).getAsJsonArray();
            for (JsonElement element : response) {
                rdmTenants.add(JsonPath.from(String.valueOf(element)).getObject("", RDMConfigIdModel.class));
            }
        } catch (Exception e) {
            throw new ApplicationGlobalException(e);
        }
        return rdmTenants;
    }

    /**
     * Get list of RDM tenants from physical configuration
     *
     * @param tenantId
     * @return list of RDMConfigIds
     */
    public static List<RDMConfigIdModel> getListOfRdmTenants(String tenantId) {
        return getListOfRdmTenants(ADMIN_NAME, tenantId);
    }

    /**
     * Put list of RDM tenants to physical configuration
     *
     * @param username
     * @param tenantId
     * @param rdmTenants
     * @return list of posted RDMConfigIds
     */
    public static List<RDMConfigIdModel> putListOfRdmTenants(String username, String tenantId, List<RDMConfigIdModel> rdmTenants) {
        String url = Config.getPlatformUrl() + "/tenants/" + tenantId + "/rdmTenants";
        List<RDMConfigIdModel> result = new ArrayList<>();
        List<RDMConfigIdModel> body = new ArrayList<>(rdmTenants);
        try {
            JsonArray response = (new Request(username, PUT, url, body.toString()).executeJson()).getAsJsonArray();
            for (JsonElement element : response) {
                result.add(JsonPath.from(String.valueOf(element)).getObject("", RDMConfigIdModel.class));
            }
        } catch (Exception e) {
            throw new ApplicationGlobalException(e);
        }
        return result;
    }

    /**
     * Put list of RDM tenants to physical configuration
     *
     * @param tenantId
     * @param rdmTenants
     * @return list of posted RDMConfigIds
     */
    public static List<RDMConfigIdModel> putListOfRdmTenants(String tenantId, List<RDMConfigIdModel> rdmTenants) {
        return putListOfRdmTenants(ADMIN_NAME, tenantId, rdmTenants);
    }

    /**
     * Put RDM tenant to physical configuration
     *
     * @param tenantId
     * @param rdmTenant
     * @return list of posted RDMConfigIds
     */
    public static List<RDMConfigIdModel> putRdmTenant(String tenantId, RDMConfigIdModel rdmTenant) {
        return putListOfRdmTenants(ADMIN_NAME, tenantId, Collections.singletonList(rdmTenant));
    }

    /**
     * Clean list of RDM tenants to physical configuration
     *
     * @param username
     * @param tenantId
     * @return clean list of RDMConfigIds
     */
    public static List<RDMConfigIdModel> cleanListOfRdmTenants(String username, String tenantId) {
        List<RDMConfigIdModel> emptyList = new ArrayList<>();
        return putListOfRdmTenants(username, tenantId, emptyList);
    }

    /**
     * Clean list of RDM tenants to physical configuration
     *
     * @param tenantId
     * @return clean list of RDMConfigIds
     */
    public static List<RDMConfigIdModel> cleanListOfRdmTenants(String tenantId) {
        return cleanListOfRdmTenants(ADMIN_NAME, tenantId);
    }

    public static List<String> getEnahancedTenants(String userName) {
        List<String> enhancedTenants = new ArrayList<>();
        String url = Config.getPlatformUrl() + "/enhancedTenants";
        try {
            JsonElement response = (new Request(userName, GET, url).executeJson());
            List<JsonElement> je = GsonUtils.getAllSubElementsWithKey(TENANT_ID, response);
            for (JsonElement je1 : je) {
                enhancedTenants.add(je1.getAsString());
            }
        } catch (Exception e) {
            throw new ApplicationGlobalException(e);
        }
        return enhancedTenants;
    }

    /**
     * Returns the full least of tenants using /tenants?showAll=true
     *
     * @return list of tenants
     */
    public static List<String> getAllTenants() {
        return getAllTenants(getPlatformUrl() + "/tenants");
    }

    /**
     * Returns the full least of tenants using /tenants?showAll=true
     *
     * @param tenantsUrl URL to get tenants
     * @return list of tenants
     */
    public static List<String> getAllTenants(String tenantsUrl) {
        List<String> allTenants = new ArrayList<>();
        String url = tenantsUrl + "?showAll=true";
        try {
            JsonArray response = (new Request(ADMIN_NAME, GET, url).executeJson()).getAsJsonArray();
            for (JsonElement je : response) {
                allTenants.add(je.getAsString());
            }
        } catch (CommandException e) {
            throw new ApplicationGlobalException("An error occurred on getting tenants:\n", e);
        }
        return allTenants;
    }

    /**
     * Check if tenant exists
     *
     * @param tenantId Tenant to check
     * @return true if exists, otherwise false
     */
    public static boolean isTenantExists(String tenantId) {
        return getAllTenants().contains(tenantId);
    }

    /**
     * Getting physical configuration from one tenant and creating the same tenant with new name
     *
     * @param sourceTenantID
     * @param newTenantID
     */
    public static void createTenantWithOptionsFrom(String sourceTenantID, String newTenantID) {
        createTenant(copy(getTenant(sourceTenantID), newTenantID));
    }

    public static TenantCreditsConfig putTenantCreditsConfig(TenantCreditsConfig tenantCreditsConfig) {
        return putTenantCreditsConfig(getTenantId(), tenantCreditsConfig);
    }

    public static TenantCreditsConfig putTenantCreditsConfig(String tenantId, TenantCreditsConfig tenantCreditsConfig) {
        String url = String.format("%s/tenants/%s/throttling", Config.getPlatformUrl(), tenantId);
        if (tenantCreditsConfig.getCreditsConfig() != null) {
            if (tenantCreditsConfig.getCreditsConfig().getPrimarySection().getMaxInternalThrottlingCredits() == null) {
                tenantCreditsConfig.getCreditsConfig().getPrimarySection().setMaxInternalThrottlingCredits(tenantCreditsConfig.getCreditsConfig().getPrimarySection().getMaxAsyncThrottlingCredits());
                tenantCreditsConfig.getCreditsConfig().getPrimarySection().setInternalThrottlingCreditsRefreshRate(tenantCreditsConfig.getCreditsConfig().getPrimarySection().getAsyncThrottlingCreditsRefreshRate());
            }
            if (tenantCreditsConfig.getCreditsConfig().getMatchingSection() != null && tenantCreditsConfig.getCreditsConfig().getMatchingSection().getMaxInternalThrottlingCredits() == null) {
                tenantCreditsConfig.getCreditsConfig().getMatchingSection().setMaxInternalThrottlingCredits(tenantCreditsConfig.getCreditsConfig().getMatchingSection().getMaxAsyncThrottlingCredits());
                tenantCreditsConfig.getCreditsConfig().getMatchingSection().setInternalThrottlingCreditsRefreshRate(tenantCreditsConfig.getCreditsConfig().getMatchingSection().getAsyncThrottlingCreditsRefreshRate());
            }
        }
        try {
            TenantCreditsConfig creditsConfig = GsonUtils.getGson().getAdapter(TenantCreditsConfig.class).fromJson(new Request(ADMIN_NAME, PUT, url, tenantCreditsConfig.toString()).execute());
            SmartWaiting.waitForAllPlatformPodsSynchronized(new Request(ADMIN_NAME, GET, url));
            return creditsConfig;
        } catch (Exception e) {
            throw new ApplicationGlobalException("An error occurred on posting throttling: " + e.getMessage(), e);
        }

    }

    public static TenantCreditsConfig getTenantCreditsConfig() {
        return getTenantCreditsConfig(getTenantId());
    }

    public static TenantCreditsConfig getTenantCreditsConfig(String tenantName) {
        String req = String.format("%s/tenants/%s/throttling", Config.getPlatformUrl(), tenantName);
        try {
            return GsonUtils.getGson().getAdapter(TenantCreditsConfig.class).fromJson(new Request(ADMIN_NAME, GET, req).execute());
        } catch (Exception e) {
            throw new ApplicationGlobalException("An error occurred on getting throttling:\n", e);
        }

    }

    /**
     * Get credit balance
     *
     * @return
     * @throws IOException
     * @throws CommandException
     */
    public static CreditsBalanceModel getCreditsBalance() throws IOException, CommandException {
        return getCreditsBalance(getTenantId());
    }

    public static CreditsBalanceModel getCreditsBalance(String tenantName) throws IOException, CommandException {
        String req = String.format("%s/credits/balance/tenant/%s", Config.getPlatformUrl(), tenantName);
        return GsonUtils.getGson().getAdapter(CreditsBalanceModel.class).fromJson(new Request(ADMIN_NAME, GET, req).execute());
    }

    /**
     * Add credits
     *
     * @param amount
     * @return
     * @throws CommandException
     * @throws IOException
     */
    public static CreditsBalanceModel addCredits(long amount) throws CommandException, IOException {
        return addCredits(getTenantId(), amount);
    }

    public static CreditsBalanceModel addCredits(String tenantId, long amount) throws CommandException, IOException {
        String req = String.format("%s/credits/balance/tenant/%s?amount=%d", Config.getPlatformUrl(), tenantId, amount);
        return GsonUtils.getGson().getAdapter(CreditsBalanceModel.class).fromJson(new Request(ADMIN_NAME, POST, req).execute());
    }

    public static CreditsBalanceModel resetCreditsTo(long amount) throws CommandException, IOException {
        return resetCreditsTo(getTenantId(), amount);
    }

    public static CreditsBalanceModel resetCreditsTo(String tenantId, long amount) throws CommandException, IOException {
        String req = Config.getPlatformUrl() + "/credits/balance/tenant/" + tenantId + "?resetTo=" + amount;
        return GsonUtils.getGson().getAdapter(CreditsBalanceModel.class).fromJson(new Request(ADMIN_NAME, POST, req).execute());
    }

    /**
     * Reset credit counters to its maximum values
     *
     * @return
     * @throws CommandException
     * @throws IOException
     */
    public static CreditsBalanceModel resetCredits() throws CommandException, IOException {
        return resetCredits(getTenantId());
    }

    /**
     * Reset credit counters to its maximum values
     *
     * @param tenantId
     * @return previous value
     * @throws CommandException
     * @throws IOException
     */
    public static CreditsBalanceModel resetCredits(String tenantId) throws CommandException, IOException {
        CreditsConfig creditsConfig = getTenantCreditsConfig(tenantId).getCreditsConfig();
        Set<Long> maxes = new HashSet<>();
        long max;
        maxes.add(creditsConfig.getMaxPriorityThrottlingCredits());
        maxes.add(creditsConfig.getMaxSyncThrottlingCredits());
        maxes.add(creditsConfig.getMaxAsyncThrottlingCredits());

        max = maxes.stream()
                .filter(Objects::nonNull)
                .mapToLong(Long::longValue)
                .max()
                .getAsLong();

        return resetCreditsTo(max);
    }

    /**
     * Configure Event processors
     *
     * @return
     * @throws CommandException
     * @throws IOException
     */
    public static EventProcessorsConfig getEventProcessors() throws CommandException, IOException {
        return getEventProcessors(getTenantId());
    }

    public static EventProcessorsConfig getEventProcessors(String tenantId) throws CommandException, IOException {
        String req = String.format("%s/tenants/%s/queues", Config.getPlatformUrl(), tenantId);
        String response = new Request(ADMIN_NAME, GET, req).execute();
        return GsonUtils.getGson().getAdapter(EventProcessorsConfig.class).fromJson(response);
    }

    public static EventProcessorsConfig putEventProcessorsConfig(EventProcessorsConfig eventProcessorsConfig) {
        return putEventProcessorsConfig(getTenantId(), eventProcessorsConfig);
    }

    public static EventProcessorsConfig putEventProcessorsConfig(String tenantName, EventProcessorsConfig eventProcessorsConfig) {
        String req = String.format("%s/tenants/%s/queues", Config.getPlatformUrl(), tenantName);
        try {
            return GsonUtils.getGson().getAdapter(EventProcessorsConfig.class).fromJson(new Request(ADMIN_NAME, PUT, req, eventProcessorsConfig.toString()).execute());
        } catch (Exception e) {
            throw new ApplicationGlobalException("An error occurred on posting events processors config:\n", e);
        }
    }

    public static void putEventProcessorsConfigWithRetries(String body, int tries) throws CommandException, InterruptedException {
        int count = 0;
        while (count < tries) {
            try {
                String req = String.format("%s/tenants/%s/queues", Config.getPlatformUrl(), getTenantId());
                new Request(ADMIN_NAME, PUT, req, body).execute();
                break;
            } catch (Exception e) {
                if (!e.getMessage().contains("Queue event processors migration is already in progress.")) {
                    throw e;
                }
                count++;
                Thread.sleep(30_000);
                log.info("Update failed because queue event processors migration is already in progress. Tries: {}", count);
            }
        }
    }

    /**
     * Return list ErrorModel from endpoint _validateAll
     *
     * @param tenantId
     * @return List<ErrorModel>
     * @throws CommandException
     */
    public List<ErrorModel> getErrorsFromPhysicalConfiguration(String tenantId) throws CommandException {
        List<ErrorModel> err = new ArrayList<>();
        JsonObject resp = new Request(ADMIN_NAME, Request.Type.GET, Config.getPlatformUrl() + "/tenants/_validateAll").executeJson().getAsJsonObject();
        if (resp.has(tenantId)) {
            StreamSupport.stream(resp.get(tenantId).getAsJsonArray().spliterator(), false)
                    .forEach(j -> err.add(Converter.getModelfromString(j.toString(), ErrorModel.class)));
        }
        return err;
    }

    /**
     * Return list ErrorModel from endpoint _validateAllMetadataConfigurations
     *
     * @param tenantId
     * @return List<ErrorModel>
     * @throws CommandException
     */
    public List<ErrorModel> getErrorsFromMetaConfiguration(String tenantId) throws CommandException {
        List<ErrorModel> err = new ArrayList<>();
        JsonObject resp = new Request(ADMIN_NAME, Request.Type.GET, Config.getPlatformUrl() + "/status/_validateAllMetadataConfigurations").executeJson().getAsJsonObject();
        if (resp.has(tenantId)) {
            resp.get(tenantId).getAsJsonObject().entrySet().stream().filter(s -> s.getValue().getAsJsonObject().has("errors"))
                    .forEach(s -> err.addAll(Arrays.asList(Converter.getModelfromString(s.getValue().getAsJsonObject().get("errors").toString(), ErrorModel[].class))));
        }
        return err;
    }

    /**
     * Sets parameters of current tenant config by using Consumer
     *
     * @param parameters (e.g.: setTenantParameters(tenant -> tenant.getSearchStorageConfiguration().setPreserveCursor(Boolean.TRUE)))
     */
    public static void setTenantParameters(Consumer<TenantModel> parameters) {
        TenantModel tenant = getTenant();
        parameters.accept(tenant);
        createTenant(tenant);
    }

    /**
     * Sets parameters of current tenant config if they are changed
     *
     * @param parameters (e.g.: tenant -> tenant.setDefaultCrosswalk("TWITTER"))
     */
    public static void setTenantParametersIfDifferent(Consumer<TenantModel> parameters) {
        TenantModel tenant = getTenant();
        JsonElement oldTenant = tenant.getJson();
        parameters.accept(tenant);

        JsonDiff diff = new JsonDiff(tenant.getJson(), oldTenant);
        if (diff.isDifferent()) {
            createTenant(tenant);
        }
    }

    //
    // Throttling related
    //

    /**
     * Turn off throttling on current tenant<br/>
     * - calculation of credits won't be done
     * - amount of credits at the tenant won't be taken into account
     */
    public static void removeThrottlingParametersFromTenant() {
        removeThrottlingParametersFromTenant(Config.getTenantId());
    }

    /**
     * Turn off throttling on the specified tenant<br/>
     * - calculation of credits won't be done
     * - amount of credits at the tenant won't be taken into account
     */
    public static void removeThrottlingParametersFromTenant(String tenantId) {
        // turn off throttling on current tenant
        putTenantCreditsConfig(tenantId, new TenantCreditsConfig());
    }

    /**
     * Setting indexOvStrategy for tenant
     *
     * @param strategy
     */
    public static void setOvStrategy(String strategy) {
        setOvStrategy(getTenantId(), strategy);
    }

    /**
     * Setting indexOvStrategy for provided tenant
     *
     * @param tenantName
     * @param strategy
     */
    public static void setOvStrategy(String tenantName, String strategy) {
        setOvStrategy(tenantName, strategy, true);
    }

    /**
     * Setting indexOvStrategy for tenant
     *
     * @param strategy
     * @param reindex
     */
    public static void setOvStrategy(String strategy, boolean reindex) {
        setOvStrategy(getTenantId(), strategy, reindex);
    }

    /**
     * Setting indexOvStrategy for provided tenant
     *
     * @param tenantName
     * @param strategy
     * @param reindex
     */
    public static void setOvStrategy(String tenantName, String strategy, boolean reindex) {
        TenantModel tenant = getTenant(tenantName);
        String currentOvStrategy = tenant.getSearchStorageConfiguration().getIndexOvStrategy();
        if (!currentOvStrategy.equals(strategy)) {
            log.info("Going to set indexOvStrategy = " + strategy + " instead of " + currentOvStrategy + " for tenant " + tenantName);
            tenant.getSearchStorageConfiguration().setIndexOvStrategy(strategy);
            TenantManagementService.createTenant(tenant);

            if (reindex) {
                PostTaskRequestParameters reindexParams = new PostTaskRequestParameters()
                        .tenantId(tenantName)
                        .forceIgnoreInStreaming(Boolean.TRUE)
                        .distributed(Boolean.TRUE)
                        .enableSeparateIndexing(Boolean.TRUE)
                        .updateEntities(Boolean.FALSE);
                try {
                    TaskService.reindex(ExecutionType.TENANT, reindexParams).waitWhileRunning(DEFAULT_TASK_GETTING_INTERVAL, DEFAULT_WAIT_TASK_TIME);
                } catch (ReltioObjectException e) {
                    log.error("Error on reindexing tenant");
                }
            }
            return;
        }
        log.info("indexOvStrategy already = " + strategy + " for tenant " + tenantName + ". Skipping strategy change");
    }

    /**
     * Change matching strategy at phys config of default tenant to {newMatchingStrategy}
     *
     * @param newMatchingStrategy
     * @return
     */
    public static TenantModel setMatchingStrategy(MatchingStrategy newMatchingStrategy) {
        return setMatchingStrategy(TenantManagementService.getTenant(getTenantId()), newMatchingStrategy);
    }

    /**
     * Change matching strategy at phys config of tenant {tenant} to {newMatchingStrategy}
     *
     * @param tenant
     * @param newMatchingStrategy
     * @return
     */
    public static TenantModel setMatchingStrategy(TenantModel tenant, MatchingStrategy newMatchingStrategy) {
        if (tenant.getMatchingConfiguration().getStrategy() != newMatchingStrategy) {
            tenant.getMatchingConfiguration().setStrategy(newMatchingStrategy);
            return TenantManagementService.createTenant(tenant);
        } else {
            log.warn("tenant.getMatchingConfiguration().getStrategy() already is " + newMatchingStrategy + ". I won't do any actions with tenant.");
            return tenant;
        }
    }

    /**
     * Sets ownerEmails config at phys config of default tenant to {newOwnerEmails}
     *
     * @param newOwnerEmails
     * @return
     */
    public static TenantModel setOwnerEmails(List<String> newOwnerEmails) {
        return setOwnerEmails(TenantManagementService.getTenant(), newOwnerEmails);
    }

    /**
     * Sets ownerEmails config at phys config of tenant {tenant} to {newOwnerEmails}
     *
     * @param tenant
     * @param newOwnerEmails
     * @return
     */
    public static TenantModel setOwnerEmails(TenantModel tenant, List<String> newOwnerEmails) {
        if (tenant.getOwnerEmails() == null || !tenant.getOwnerEmails().equals(newOwnerEmails)) {
            tenant.setOwnerEmails(newOwnerEmails);
            return TenantManagementService.createTenant(tenant);
        } else {
            log.info("tenant.getOwnerEmails() is already " + newOwnerEmails + " - no update required");
            return tenant;
        }
    }

    /**
     * Return string that will contain System.getenv().get("COMPUTERNAME"); System.getProperty("user.name"); System.currentTimeMillis()
     *
     * @return
     */
    public static String generateInfoForTenantName() {
        return System.getenv().get("COMPUTERNAME") + ":" + System.getProperty("user.name") + ":" + System.currentTimeMillis();
    }

    private static JsonObject setValidationParameters(JsonObject body) throws CommandException {
        return setValidationParameters(getTenantId(), body);
    }

    public static JsonObject setValidationParameters(String tenantId, JsonObject body) throws CommandException {
        JsonObject tenantValidationParameters;
        try {
            tenantValidationParameters = Retrier.retry(
                    () -> new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + tenantId + "/validationProperty", body.toString())
                            .executeJson().getAsJsonObject());
        } catch (Exception e) {
            throw new CommandException("Failed to set validation parameters", e); //NOSONAR
        }

        waitForTenantConfigurationUpdated(tenantId);
        return tenantValidationParameters;
    }

    /**
     * Set validation parameters 'physicalConfigValidationEnabled'; 'businessConfigValidationEnabled' on tenant
     *
     * @param physicalConfigValidationEnabled
     * @param businessConfigValidationEnabled
     * @return
     * @throws CommandException
     */
    public static JsonObject setValidationParameters(Boolean physicalConfigValidationEnabled, Boolean businessConfigValidationEnabled, String... tenantId) throws CommandException {
        JsonObject body = new JsonObject();
        body.addProperty("physicalConfigValidationEnabled", physicalConfigValidationEnabled);
        body.addProperty("businessConfigValidationEnabled", businessConfigValidationEnabled);
        String tenant = getTenantId();
        if (tenantId.length == 1) {
            tenant = tenantId[0];
        }
        return setValidationParameters(tenant, body);
    }

    /**
     * Set validation parameter 'physicalConfigValidationEnabled' on tenant
     *
     * @param physicalConfigValidationEnabled
     * @return
     * @throws CommandException
     */
    public static JsonObject setValidationParameterPhysicalConfigValidationEnabled(Boolean physicalConfigValidationEnabled) throws CommandException {
        JsonObject body = new JsonObject();
        body.addProperty("physicalConfigValidationEnabled", physicalConfigValidationEnabled);
        return setValidationParameters(body);
    }

    /**
     * Set validation parameter 'businessConfigValidationEnabled' on tenant
     *
     * @param businessConfigValidationEnabled
     * @return
     * @throws CommandException
     */
    public static JsonObject setValidationParameterBusinessConfigValidationEnabled(Boolean businessConfigValidationEnabled) throws CommandException {
        JsonObject body = new JsonObject();
        body.addProperty("businessConfigValidationEnabled", businessConfigValidationEnabled);
        return setValidationParameters(body);
    }

    /**
     * Enable/disable streaming for tenant
     *
     * @param tenant
     * @param newState
     * @return
     */
    public static TenantModel setStreaming(String tenant, boolean newState) {
        return setStreaming(getTenant(tenant), newState);
    }

    /**
     * Enable/disable streaming for tenant
     *
     * @param tenantModel
     * @param newState
     * @return
     */
    public static TenantModel setStreaming(TenantModel tenantModel, boolean newState) {
        //enable-disable streaming for tenant
        StreamingConfigModel streamingConfigModel = tenantModel.getStreamingConfig();
        if (streamingConfigModel == null) {
            streamingConfigModel = new StreamingConfigModel(Boolean.FALSE);
            tenantModel.setStreamingConfig(streamingConfigModel);
        }
        boolean currentState = tenantModel.getStreamingConfig().getStreamingEnabled();
        if (currentState != newState) {
            log.warn("Set streamingEnabled = " + newState + " for tenant " + tenantModel.getTenantId());
            tenantModel.getStreamingConfig().streamingEnabled(newState);
            tenantModel.getStreamingConfig().streamingAPIEnabled(newState);
            TenantManagementService.createTenant(tenantModel);
            sleep(10000); //TODO create good wait for DTSS
        } else {
            log.warn("streamingEnabled already " + currentState + " for tenant " + tenantModel.getTenantId());
        }
        return tenantModel;
    }

    /**
     * Enable/disable changed objects monitoring log for tenant
     *
     * @param tenantId              - tenant ID
     * @param changedObjectsEnabled - true/false
     * @return updated tenant model
     * @throws CommandException
     */
    public static TenantModel setChangedObjectsEnabled(String tenantId, boolean changedObjectsEnabled) throws CommandException {
        if (getTenant(tenantId).isChangedObjectsEnabled() != changedObjectsEnabled) {
            JsonElement monitoring = getMonitoringSection(tenantId);
            JsonObject body = monitoring.isJsonNull() ? new JsonObject() : monitoring.getAsJsonObject();
            body.addProperty("changedObjectsEnabled", changedObjectsEnabled);
            putMonitoringSection(tenantId, body);
            waitForTenantConfigurationUpdated(tenantId);
        } else {
            log.info("No need to update changedObjectsEnabled in physical configuration for tenant " + getTenantId());
        }
        return getTenant(tenantId);
    }

    /**
     * Override section "monitoring" in tenant physical configuration
     *
     * @param tenantId - tenant ID
     * @param body     - new section "monitoring"
     * @return updated tenant model
     * @throws CommandException
     */
    public static TenantModel putMonitoringSection(String tenantId, JsonObject body) throws CommandException {
        new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + tenantId + "/monitoring",
                body.toString()).execute();
        waitForTenantConfigurationUpdated(tenantId);
        return getTenant(tenantId);
    }

    /**
     * Update section "monitoring" in tenant physical configuration with new fields
     *
     * @param tenantId - tenant ID
     * @param body     - additional fields for section "monitoring"
     * @return updated tenant model
     * @throws CommandException
     */
    public static TenantModel updateMonitoringSection(String tenantId, JsonObject body) throws CommandException {
        JsonElement monitoringResponse = getMonitoringSection(tenantId);
        JsonObject monitoring;
        if (monitoringResponse.isJsonNull()) {
            monitoring = body;
        } else {
            monitoring = monitoringResponse.getAsJsonObject();
            for (Map.Entry<String, JsonElement> entry : body.entrySet()) {
                monitoring.getAsJsonObject().add(entry.getKey(), entry.getValue());
            }
        }
        return putMonitoringSection(tenantId, JsonParser.parseString(GsonUtils.getGson().toJson(monitoring)).getAsJsonObject());
    }

    /**
     * Get section "monitoring" from tenant physical configuration
     *
     * @param tenantId - tenant Id
     * @return section "monitoring" as JSON
     * @throws CommandException
     */
    public static JsonElement getMonitoringSection(String tenantId) throws CommandException {
        return new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "/monitoring").executeJson();
    }

    /**
     * Get timestamp when last consistency check was done on tenant by IRS
     *
     * @param tenantId - tenant ID
     * @return timestamp as long
     * @throws Exception
     */
    public static long getLastConsistencyCheckTime(String tenantId) throws Exception {
        JsonElement status = new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/status/tenant/" + tenantId + "?details=all").executeJson();
        JsonElement lastConsistencyCheckTime = status.getAsJsonObject().get("All background checks are finished for changes until");
        return lastConsistencyCheckTime == null ? 0 : lastConsistencyCheckTime.getAsLong();
    }

    /**
     * Get last update time for tenant
     *
     * @param tenantId - tenant ID
     * @return timestamp as long
     * @throws Exception
     */
    public static long getLastUpdateTime(String tenantId) throws Exception {
        JsonElement status = new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/status/tenant/" + tenantId + "?details=all").executeJson();
        JsonElement lastConsistencyCheckTime = status.getAsJsonObject().get("Recent update time");
        return lastConsistencyCheckTime.getAsJsonPrimitive().getAsString().equals("unknown") ? 0 : lastConsistencyCheckTime.getAsLong();
    }

    /**
     * Get detailed status for tenant
     *
     * @param tenantId - tenant ID
     * @return status as String
     * @throws Exception
     */
    public static String getDetailedTenantStatus(String tenantId) throws Exception {
        JsonElement status = new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/status/tenant/" + tenantId + "?details=all").executeJson();
        return status.getAsJsonObject().get("Status").getAsString();
    }

    public static boolean cleanDynamoDBStorage() throws CommandException {
        return cleanDynamoDBStorage(getTenantId(), TRUNCATE);
    }

    /**
     * Clean DynamoDB storage for tenant
     *
     * @param tenantId           - tenantId
     * @param cleanStorageMethod - one of CleanStorageMethod
     * @return true if cleaning is successful
     * @throws CommandException
     */
    public static boolean cleanDynamoDBStorage(String tenantId, CleanStorageMethod cleanStorageMethod) throws CommandException {
        JsonElement response = new Request(ADMIN_NAME, POST, String.format("%s%s/cleanDynamoDBStorage?method=%s", Config.getApiUrl(), tenantId, cleanStorageMethod.name())).executeJson();
        return response.getAsJsonObject().get("successful").getAsBoolean();
    }

    /**
     * Clean SpannerDB storage for tenant
     *
     * @param tenantId           - tenantId
     * @param cleanStorageMethod - one of CleanStorageMethod
     * @return true if cleaning is successful
     * @throws CommandException
     */
    public static boolean cleanSpannerDBStorage(String tenantId, CleanStorageMethod cleanStorageMethod) throws CommandException {
        JsonElement response = new Request(ADMIN_NAME, POST, String.format("%s%s/cleanSpannerDBStorage?method=%s", Config.getApiUrl(), tenantId, cleanStorageMethod.name())).executeJson();
        return response.getAsJsonObject().get("successful").getAsBoolean();
    }

    public static boolean cleanSpannerDBStorage(String tenantId, boolean delete, boolean cleanKeys) throws CommandException {
        UriBuilder uri = UriBuilder.fromPath(Config.getApiUrl());
        uri.path(tenantId).path("cleanSpannerDBStorage");
        if (delete) {
            uri.queryParam("delete", true);
        }
        if (cleanKeys) {
            uri.queryParam("cleanKeys", true);
        }
        return new Request(ADMIN_NAME, POST, uri.build().toString()).executeJson().getAsJsonObject().get("successful").getAsBoolean();
    }

    public static boolean cleanCosmosDBStorage(String tenantId, CleanStorageMethod cleanStorageMethod) throws CommandException {
        JsonElement response = new Request(ADMIN_NAME, POST, String.format("%s%s/cleanCosmosDBStorage?method=%s", Config.getApiUrl(), tenantId, cleanStorageMethod.name())).executeJson();
        return response.getAsJsonObject().get("successful").getAsBoolean();
    }

    public static String cleanDynamoDBStorage(String tenantId) throws CommandException {
        UriBuilder uri = UriBuilder.fromPath(Config.getEnvUrl());
        uri.path("/reltio/api/").path(tenantId).path("/cleanDynamoDBStorage");
        uri.queryParam("method", TRUNCATE.name());
        return new Request(ADMIN_NAME, POST, uri.build().toString()).execute();
    }

    /**
     * Updates DynamoDB table scaling properties.
     * NOTE: This method should not be used with onDemand tables.
     *
     * @param oldTm Tenant model
     * @deprecated since 2024.1.2.0
     */
    @Deprecated(since = "2024.1.2.0", forRemoval = true)
    public static void boostDynamoDBTables(TenantModel oldTm) {
        TenantModel tm = oldTm.clone();
        tm.getDataStorageConfig().setTableProperties(getBoostedTableProperties());
        createTenant(tm);
    }

    @Deprecated(since = "2024.1.2.0", forRemoval = true)
    private static Map<String, Map<String, TableProperties>> getBoostedTableProperties() {
        Map<String, TableProperties> tempProperty = new HashMap<>();
        tempProperty.put("dynamoDBProperties", new TableProperties().capacityMode("AutoScaling")
                .minReadCapacity(500)
                .maxReadCapacity(5000)
                .readTargetUtilization(50.0)
                .minWriteCapacity(100)
                .maxWriteCapacity(2000)
                .writeTargetUtilization(50.0));
        Map<String, Map<String, TableProperties>> dynamoDBTableProperties = new HashMap<>();
        dynamoDBTableProperties.put("entitiesCF", tempProperty);
        dynamoDBTableProperties.put("entitiesCompressedCF", tempProperty);
        dynamoDBTableProperties.put("relationsCF", tempProperty);
        dynamoDBTableProperties.put("relationsCompressedCF", tempProperty);
        dynamoDBTableProperties.put("externalCrosswalkCF", tempProperty);
        dynamoDBTableProperties.put("entityMergeTreeCF", tempProperty);
        dynamoDBTableProperties.put("matchCF", tempProperty);
        dynamoDBTableProperties.put("matchDocumentsCF", tempProperty);
        dynamoDBTableProperties.put("entityMatchesCF", tempProperty);
        dynamoDBTableProperties.put("relationsOneHopsCF", tempProperty);
        dynamoDBTableProperties.put("relationMergeTreeCF", tempProperty);
        dynamoDBTableProperties.put("externalRelationCrosswalkCF", tempProperty);
        dynamoDBTableProperties.put("analyticsAttributesCF", tempProperty);

        return dynamoDBTableProperties;
    }

    /**
     * RIQ confirmation to Reltio API that events have been successfully processed by ANALYTICS processor
     *
     * @param tenantId - tenant ID
     * @param events   - list of events
     * @throws CommandException
     */
    public static void postAnalyticsProcessed(String tenantId, List<String> events) throws CommandException {
        JsonObject response = new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantId + "/analyticsProcessed", new Gson().toJson(events)).executeJson().getAsJsonObject();
        if (!response.get("successful").getAsBoolean()) {
            throw new CommandException(response.getAsString());
        }
    }

    /**
     * RIQ confirmation to Reltio API that events have been successfully processed by ANALYTICS_PUBSUB processor
     *
     * @param tenantId - tenant ID
     * @param events   - list of events
     * @throws CommandException
     */
    public static void postAnalyticsGbqProcessed(String tenantId, List<String> events) throws CommandException {
        JsonObject response = new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantId + "/analyticsGbqProcessed", new Gson().toJson(events)).executeJson().getAsJsonObject();
        if (!response.get("successful").getAsBoolean()) {
            throw new CommandException(response.getAsString());
        }
    }

    /**
     * Update crud event queues configuration
     *
     * @param tenantId - tenant ID
     * @param body     - configuration
     * @throws CommandException
     */
    public static void putCrudEventQueuesConfig(String tenantId, String body) throws CommandException {
        String crudQueuesUrl = Config.getPlatformUrl() + "/tenants/" + tenantId + "/queues/crud";
        new Request(ADMIN_NAME, PUT, crudQueuesUrl, body).executeJson();
        waitForValue(() -> new Request(ADMIN_NAME, GET, crudQueuesUrl).execute(), body);
        SmartWaiting.waitForAllPlatformPodsSynchronized(new Request(ADMIN_NAME, GET, crudQueuesUrl));
    }

    /**
     * Get crud event queues configuration
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static JsonElement getCrudEventQueuesConfig(String tenantId) throws CommandException {
        return new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "/queues/crud").executeJson();
    }

    /**
     * Check if crud processors are disable
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static boolean isCrudProcessorDisable(String tenantId) throws CommandException {
        JsonElement disabledParam = GsonUtils.getByPath(getCrudEventQueuesConfig(tenantId),
                "processorsParams/defaultProcessorsParams/disabled");

        return disabledParam != null && disabledParam.getAsBoolean();
    }

    /**
     * Check that crud event processor has status "Idle"
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static boolean isCrudEventProcessorIdle(String tenantId) throws CommandException {
        if (isCrudProcessorDisable(tenantId)) {
            log.info("CRUD events processor is disabled");
            return true;
        } else {
            return isProcessorIdle(tenantId, EVENTS);
        }
    }

    /**
     * Get match event queues configuration
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static JsonElement getMatchEventQueuesConfig(String tenantId) throws CommandException {
        return new Request(ADMIN_NAME, GET, Config.getPlatformUrl() + "/tenants/" + tenantId + "/queues/match").executeJson();
    }

    /**
     * Check if match processors are disable
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static boolean isMatchProcessorDisable(String tenantId) throws CommandException {
        JsonElement disabledParam = GsonUtils.getByPath(getMatchEventQueuesConfig(tenantId),
                "processorsParams/defaultProcessorsParams/disabled");

        return disabledParam != null && disabledParam.getAsBoolean();
    }

    /**
     * Check that match event processor has status "Idle"
     *
     * @param tenantId - tenant ID
     * @return
     * @throws CommandException
     */
    public static boolean isMatchEventProcessorIdle(String tenantId) throws CommandException {
        if (isMatchProcessorDisable(tenantId)) {
            log.info("MATCH events processor is disabled");
            return true;
        } else {
            return isProcessorIdle(tenantId, MATCH);
        }
    }

    /**
     * Check that processor has status "Idle"
     *
     * @param tenantId      - tenant ID
     * @param processorType - processor type
     * @return
     * @throws CommandException
     */
    private static boolean isProcessorIdle(String tenantId, EnvironmentService.ProcessorType processorType) throws CommandException {
        Service service = getDataprocessService();
        List<String> nodes = ServiceNodesControllerWrapper.getServiceNodes(service);
        if (nodes.isEmpty()) {
            throw new ApplicationGlobalException("Count of nodes of API is equal 0");
        }
        List<String> subscriptions = switch (processorType) {
            case EVENTS -> EnvironmentService.getEventsSubscriptions(tenantId);
            case MATCH -> EnvironmentService.getMatchEventsSubscriptions(tenantId);
        };
        boolean isAnyConsumerFound = false;
        for (String node : nodes) {
            Request request = new Request(ADMIN_NAME, GET, Config.getUrl(service) + "/status/" +
                    processorType.toString().toLowerCase(Locale.ENGLISH) + "HubStats/all?tenantFilter=" + tenantId);
            if (Config.isEtalon(Service.API)) {
                if (GlobalConfig.isWaitByAllPods()) {
                    request.setHeader(EXPECTED_HOST_KEY, node);
                } else {
                    log.warn("WAIT_BY_ALL_PODS = false, due to it we will skip the check by all pods.");
                }
            }
            JsonElement eventsHubStats = request.executeJson();
            for (String subscription : subscriptions) {
                JsonElement subscriptionStatus = GsonUtils.getByPath(eventsHubStats, "consumers/" + subscription + "/currentState");
                if (subscriptionStatus != null) {
                    String hubStatus = subscriptionStatus.getAsString();
                    if (!"Idle".equals(hubStatus) && !"async credit balance".equals(hubStatus)) {
                        return false;
                    }
                    isAnyConsumerFound = true;
                }
            }
        }
        if (!isAnyConsumerFound) {
            throw new ApplicationGlobalException("Unable to get any " + processorType + " consumer for tenant " + tenantId);
        }
        return true;
    }

    /**
     * Update match event queues configuration
     *
     * @param tenantId - tenant ID
     * @param body     - configuration
     * @throws CommandException
     */
    public static void putMatchEventQueuesConfig(String tenantId, String body) throws CommandException {
        String matchQueuesUrl = Config.getPlatformUrl() + "/tenants/" + tenantId + "/queues/match";
        new Request(ADMIN_NAME, PUT, matchQueuesUrl, body).executeJson();
        waitForValue(() -> new Request(ADMIN_NAME, GET, matchQueuesUrl).execute(), body);
        SmartWaiting.waitForAllPlatformPodsSynchronized(new Request(ADMIN_NAME, GET, matchQueuesUrl));
    }

    /**
     * Set maintenance mode (true or false) on tenant, for its deletion
     *
     * @param tenantId
     */
    private static void setMaintenanceMode(String tenantId, boolean isEnable) {
        String action = isEnable ? "Enable" : "Disable";
        if (getMaintenanceMode(tenantId) != isEnable) {
            try {
                log.info("{} maintenance mode for tenant {}", action, tenantId);
                new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + tenantId + "/maintenance?enable=" + isEnable).executeJson();
                waitForTenantConfigurationUpdated(tenantId);
            } catch (Exception e) {
                log.warn("Could not {} maintenance mode for tenant {}. Due to {}; Try to delete tenant w/o this mode.", action, tenantId, e.getMessage());
            }
        } else {
            log.info("Maintenance mode for tenant {} is already {}", tenantId, action);
        }
    }

    /**
     * Get Maintenance mode for a tenant
     *
     * @param tenantId - tenant ID
     * @return true if Maintenance mode is enabled, else false (or if tenant does not exist)
     */
    public static boolean getMaintenanceMode(String tenantId) {
        if (isTenantExists(tenantId)) {
            try {
                return getTenant(tenantId).isMaintenance();
            } catch (ApplicationGlobalException exc) {
                if (exc.getMessage().contains(String.format("Tenant %s not configured", tenantId))
                        || exc.getMessage().contains("Tenant failed to initialize")) {
                    log.warn(exc.getMessage());
                    return false;
                } else {
                    throw new ApplicationGlobalException("Unexpected exception during getting Maintenance mode:\n" + exc, exc);
                }
            }
        } else {
            log.warn("Tenant {} does not exist", tenantId);
            return false;
        }
    }

    public static void disableMaintenanceMode(String tenantId) {
        setMaintenanceMode(tenantId, false);
    }

    public static void enableMaintenanceMode(String tenantId) {
        setMaintenanceMode(tenantId, true);
    }

    public static TaskModel putHistoryCache(String tenantId, TenantModel.HistoryCache historyCache) throws CommandException {
        // the time of task creation for some reason can be less than current timestamp on local machine
        Long currentTimestamp = System.currentTimeMillis() - 60_000;
        new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + tenantId + "/historyCache", new Gson().toJson(historyCache)).executeJson();
        return SmartWaiting.waitForAppearInHistoryTask(getTenantId(), PeriodicTasks.ReindexHistoryCacheTask.toString(), currentTimestamp);
    }

    public static void switchDataKeyspaceToDataStorage(String dataStorageId) {
        switchDataKeyspaceToDataStorage(dataStorageId, true);
    }

    public static void switchDataKeyspaceToDataStorage(String dataStorageId, Boolean changeMergeTreeStoringMode) {
        TenantModel tm = TenantManagementService.getTenant();
        tm.getDataStorageConfig().createEmptyDataKeyspaceConfig();
        tm.getDataStorageConfig().getDataKeyspaceConfig().setDataStorageId(dataStorageId);
        if (tm.getDataStorageConfig().getDataMigration() != null && tm.getDataStorageConfig().getDataMigration().get("tableNameMapping") != null) {
            Map<String, String> tableNameMapping = (Map<String, String>) tm.getDataStorageConfig().getDataMigration().get("tableNameMapping");
            for (Map.Entry<String, String> tableName : tableNameMapping.entrySet()) {
                String name = Character.toUpperCase(tableName.getKey().charAt(0)) + tableName.getKey().substring(1);
                Class cl = tm.getDataStorageConfig().getClass();
                try {
                    cl.getMethod("set" + name, String.class).invoke(tm.getDataStorageConfig(), tableName.getValue());
                } catch (Exception e) {
                    log.error("Can't set table name for CF '{}'. Exception: {}", name, e.getMessage(), e);
                }
            }
        }
        if (Boolean.TRUE.equals(changeMergeTreeStoringMode)) {
            tm.getDataStorageConfig().setEntityMergeTreeStoringMode("writeNew_readNew");
        }
        if (tm.getDataStorageConfig().getInteractionKeyspaceConfig() != null) {
            tm.getDataStorageConfig().setInteractionKeyspaceConfig(null);
        }
        if (tm.getDataStorageConfig().getActivityLogKeyspaceConfig() != null) {
            tm.getDataStorageConfig().setInteractionKeyspaceConfig(null);
        }
        createTenant(tm);
    }

    public static void enableCommitLog(String dataStorageId) throws Exception {
        String tenantId = getTenantId();
        String body = "{\n" +
                "\t\"tableNameMapping\": {\n" +
                "\t\t\"graphsCF\": \"graphs_groups_categories\",\n" +
                "\t\t\"groupsCF\": \"graphs_groups_categories\",\n" +
                "\t\t\"categoriesCF\": \"graphs_groups_categories\",\n" +
                "\t\t\"categoriesLinksCF\": \"graphs_groups_categories\"\n" +
                "\t}\n" +
                "}";
        log.info("Enabling commit log for tenant " + tenantId + " for datastorage " + dataStorageId);
        new Request(ADMIN_NAME, PUT, Config.getPlatformUrl() + "/tenants/" + tenantId + "/_dataMigration?destinationDataStorageId=" + dataStorageId, body).execute();
        waitForTenantConfigurationUpdated(tenantId);
    }

    public static void disableCommitLog() throws Exception {
        String tenantId = getTenantId();
        log.info("Disabling commit log for tenant " + tenantId);
        new Request(ADMIN_NAME, Request.Type.DELETE, Config.getPlatformUrl() + "/tenants/" + tenantId + "/_dataMigration").execute();
        waitForTenantConfigurationUpdated(tenantId);
    }

    public static DBService getDBServiceFromTenant(TenantModel tenantModel) {
        if (tenantModel.hasSpannerDBDataStorage()) {
            return new SpannerService(tenantModel);
        } else if (tenantModel.hasDynamoDBDataStorage()) {
            return new DynamoDBService();
        } else if (tenantModel.hasCosmosDBStorage()) {
            return new CosmosDBService(tenantModel);
        } else {
            return new CassandraService(tenantModel);
        }
    }

    public static List<TaskModel> updateHistoryTtl(String tenantId, String ttl) throws CommandException {
        return updateHistoryTtl(tenantId, ttl, Boolean.FALSE, 0);
    }

    public static List<TaskModel> updateHistoryTtl(String tenantId, String ttl, Boolean distributed, int taskPartsCount) throws CommandException {
        StringBuilder urlBuilder = new StringBuilder(getPlatformUrl());
        urlBuilder.append("/api/").append(tenantId).append("/updateHistoryTtl?ttl=").append(ttl);

        if (Boolean.TRUE.equals(distributed)) {
            urlBuilder.append("&distributed=true");
            if (taskPartsCount > 0) {
                urlBuilder.append("&taskPartsCount=").append(taskPartsCount);
            }
        }

        String url = urlBuilder.toString();
        JsonElement response = new Request(ADMIN_NAME, POST, url).executeJson();
        return GsonUtils.getGson().fromJson(response, new com.google.common.reflect.TypeToken<List<TaskModel>>() {
        }.getType());
    }

    /**
     * This method is used to improve Azure queues performance
     * Default messagingUnits = 1; recommended value is messagingUnits = 4 when loading 100K entities
     * Note processing cost increases proportionally so please restore the default after processing
     *
     * @param tenantId       - tenant ID
     * @param queueType      - "crud" or "match"
     * @param messagingUnits - number of units
     */
    public static String scaleAzureQueue(String tenantId, String queueType, int messagingUnits) {
        try {
            String resp = new Request(ADMIN_NAME, POST, Config.getApiUrl() + tenantId + "/scaleAzureQueue/" + queueType + "?messagingUnits=" + messagingUnits).execute();
            log.info("Scaled AzureQueue for tenant " + tenantId + ": " + resp);
            return resp;
        } catch (CommandException e) {
            log.error("Error scaling AzureQueue for tenant " + tenantId, e);
            return "";
        }
    }

    /**
     * This method gets tenantID from TENANTS system table
     *
     * @param tenantId
     * @return
     * @throws Exception
     */
    public static String getTenantInternalIDFromSysKeyspace(String tenantId) throws CommandException {
        SystemStoragesModel systemStorages = EnvironmentService.getSystemStorages();
        try (DatabaseSystemProvider<DatabaseResultSet> dbTenantProvider = DatabaseSystemProviderFactory.getDbSysTestProvider(tenantsDAO, systemStorages)) {
            return dbTenantProvider.selectTenantInternalIDFromSysTable(tenantId);
        } catch (Exception e) {
            throw new CommandException("Error during getting tenant internal ID", e);
        }
    }

    public static void createNewTenantWithWait(TenantModel tenantModel) {
        createNewTenantWithWait(tenantModel.getTenantId(), tenantModel.toString());
    }

    /**
     * Creates a tenant with the specified tenant ID and tenant model.
     * This method uses a retry mechanism to wait for the tenant creation to complete.
     * If certain exceptions are encountered during the process, the method will retry
     * or handle the exceptions as specified.
     *
     * @param tenantId    the ID of the tenant to be created.
     * @param tenantModel the model representing the tenant configuration.
     * @throws IllegalArgumentException   if the tenant creation parameters are invalid.
     * @throws ApplicationGlobalException if an application-level exception occurs during tenant creation
     *                                    that is not recoverable (e.g., not a "502 Server Error" or a "Read timed out" error).
     * @throws InterruptedWaitException   if the wait operation is interrupted or fails to complete.
     */
    public static void createNewTenantWithWait(String tenantId, String tenantModel) {
        Set<String> interruptIfNotThis = Set.of(
                ErrorMessages.ERROR_502_SERVER_ERROR,
                ErrorMessages.ERROR_READ_TIME_OUT,
                ErrorMessages.ERROR_TENANT_NOT_FOUND,
                ErrorMessages.HISTORY_NOT_SUPPORTED
        );
        try {
            waitForValue(() -> {
                try {
                    return TenantManagementService.createTenant(tenantModel).getTenantId();
                } catch (IllegalArgumentException e) {
                    throw new InterruptedWaitException(e);
                } catch (ApplicationGlobalException e) {
                    if (interruptIfNotThis.stream().noneMatch(e.getMessage()::contains)) {
                        throw new InterruptedWaitException(e);
                    }
                    return e.getMessage();
                }
            }, tenantId, "Wait for tenant '" + tenantId + "' is successfully created");
        } catch (InterruptedWaitException e) {
            log.error(e.getMessage(), e);
            if (e.getCause() instanceof ApplicationGlobalException applicationGlobalException) {
                throw applicationGlobalException;
            } else {
                throw e;
            }
        }
    }

}
