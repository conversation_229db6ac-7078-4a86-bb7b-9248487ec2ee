package com.reltio.qa.api.entity;

import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.model.*;
import com.reltio.qa.model.Interactions.GetInteractionResponse;
import com.reltio.qa.model.Interactions.InteractionMemberModel;
import com.reltio.qa.model.Interactions.InteractionModel;
import com.reltio.qa.model.Interactions.InteractionSubMember;
import com.reltio.qa.request.Request;
import com.reltio.qa.service.retry.RetryAnalyzer;
import com.reltio.qa.services.BusinessConfigurationService;
import com.reltio.qa.services.EntityService;
import com.reltio.qa.services.SearchService;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformConfigs;
import com.reltio.qa.services.tenant.config.TenantManagementServiceForInteractions;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.SkipException;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;
import java.util.function.Function;

import static com.reltio.qa.helpers.CommonStrings.*;
import static com.reltio.qa.helpers.loaddata.LoadData.createHCP;
import static com.reltio.qa.helpers.loaddata.LoadData.generateCrosswalk;
import static com.reltio.qa.model.Interactions.InteractionModel.*;
import static com.reltio.qa.model.SearchFilterExpression.*;
import static com.reltio.qa.services.EnvironmentService.isGCP;
import static com.reltio.qa.services.TenantManagementService.*;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForQueueStayEmpty;
import static java.util.Arrays.copyOfRange;
import static org.testng.Assert.*;

@Log4j2
public class Interactions extends TestNGBaseTest {

    @BeforeClass(alwaysRun = true)
    public void bcInteractions() throws Exception {
        cleanTenant();
        BusinessConfigurationService.updateConfig(loadFile("179-200-config.json"));

        if (!GlobalConfig.isUseSpannerForInteractions()) {
            log.info("Skip switching to Interactions V2, using legacy interactions");
            return;
        }

        log.info("Using Spanner for Interactions");
        SearchService.setV2(true);

        if (!TenantManagementServiceForInteractions.isInteractionsRepositoryVersionSetToV2()) {
            log.info("Update tenant's config to use SpannerDB for Interactions...");
            TenantManagementServiceForInteractions.enableInteractionsV2Config(Config.getTenantId());
            SmartWaitingPlatformConfigs.waitForRepositoryVersionSetToV2InConfig();
        }
    }

    @TmsLink("RP-TC-179")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression", "smoke"}, description = "Pagination mechanism of interactions")
    public void runRpTc179() throws Exception {
        EntityModel hcp = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel[] interactions = new InteractionModel[37];
        for (int i = 0; i < 37; i++) {
            interactions[36 - i] = simpleInteraction(hcp, INTERACTION_TYPE_EMAIL, "From").post();
        }
        //1
        testInteractions(hcp.getInteractions(new GetEntityRequestParameters().max(0).offset(0)), 37, true);
        //2
        testInteractions(hcp.getInteractions(new GetEntityRequestParameters().max(0).offset(10)), 37, true);
        //3
        testInteractions(hcp.getInteractions(new GetEntityRequestParameters().max(10).offset(10)), 37, true,
                copyOfRange(interactions, 10, 20));
        //4
        testInteractions(hcp.getInteractions(new GetEntityRequestParameters().max(10).offset(20)), 37, true,
                copyOfRange(interactions, 20, 30));
        //5
        testInteractions(hcp.getInteractions(new GetEntityRequestParameters().max(10).offset(15)), 37, true,
                copyOfRange(interactions, 15, 25));
        //6
        testInteractions(hcp.getInteractions(new GetEntityRequestParameters().max(10).offset(30)), 37, true,
                copyOfRange(interactions, 30, 37));
    }

    @TmsLink("RP-TC-180")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Verify 'fetchedAll' parameter")
    public void runRpTc180() throws Exception {
        TenantModel tenant = getTenant(Config.getTenantId());
        try {
            tenant.setMaxInteractionsToFetchPerEntity(10);
            createTenant(tenant);
            EntityModel hcp = new EntityModel(ENTITY_TYPE_HCP).post();

            for (int i = 0; i < 11; i++) {
                simpleInteraction(hcp, INTERACTION_TYPE_EMAIL, "From").post();
            }
            //1
            GetInteractionResponse gir = hcp.getInteractions();
            assertEquals(gir.getInteractions().size(), 10, "Number of interactions in response:");
            assertFalse(gir.getFetchedAll(), "fetchedAll");
        } finally {
            tenant.setMaxInteractionsToFetchPerEntity(100000);
            createTenant(tenant);
        }
    }

    @TmsLink("RP-TC-181")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Filtering of interactions by interactions_type")
    public void runRpTc181() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel inter1 = simpleInteraction(en, INTERACTION_TYPE_EMAIL, "From").post();
        InteractionModel inter2 = simpleInteraction(en, INTERACTION_TYPE_EMAIL, "From").post();
        simpleInteraction(en, INTERACTION_TYPE_PRESCRIPTIONS, "HCP").post();

        EntityModel en2 = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel inter4 = simpleInteraction(en2, INTERACTION_TYPE_EMAIL, "From").post();
        InteractionModel inter5 = simpleInteraction(en2, INTERACTION_TYPE_EMAIL, "From").post();

        Function<String, GetEntityRequestParameters> filter = type ->
                new GetEntityRequestParameters().filter(equalsFilter("type", type));
        //1
        testInteractions(en.getInteractions(filter.apply(INTERACTION_TYPE_EMAIL)), inter2, inter1);
        //2
        testInteractions(en2.getInteractions(filter.apply(INTERACTION_TYPE_PRESCRIPTIONS)));
        //3
        testInteractions(en2.getInteractions(filter.apply(INTERACTION_TYPE_EMAIL)), inter5, inter4);
    }

    @TmsLink("RP-TC-182")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Filtering of interactions with 'equals'")
    public void runRpTc182() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel inter1 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter2 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        //1
        testInteractions(en.getInteractions(new GetEntityRequestParameters()
                .filter(equalsFilter("attributes", "Subject 1"))), 2, true, inter2, inter1);
        //2
        testInteractions(en.getInteractions(new GetEntityRequestParameters()
                .filter(equalsFilter("attributes", "Subject xxxx"))), 0, true);
    }

    @TmsLink("RP-TC-196")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Filtering of interactions with 'fullText'")
    public void runRpTc196() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel inter1 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter2 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter3 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 12").post();
        interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 2").post();
        //1
        testInteractions(en.getInteractions(new GetEntityRequestParameters()
                .filter(fullText("attributes.Subject", "Subject 1"))), 3, true, inter3, inter2, inter1);
        //2
        testInteractions(en.getInteractions(new GetEntityRequestParameters()
                .filter(fullText("attributes.Subject", "Subject1"))), 0, true);
    }

    @TmsLink("RP-TC-197")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Filtering of interactions with 'missing'/'exists'")
    public void runRpTc197() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel inter1 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter2 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter3 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Test", "Test 1").post();
        //1
        testInteractions(en.getInteractions(new GetEntityRequestParameters()
                .filter(missing("attributes.Subject"))), 1, true, inter3);
        //2
        testInteractions(en.getInteractions(new GetEntityRequestParameters()
                .filter(exists("attributes.Subject"))), 2, true, inter2, inter1);
    }

    @TmsLink("RP-TC-198")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Filtering of interactions with complex filter")
    public void runRpTc198() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel inter1 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter2 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel inter3 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Test", "Test 1").post();
        interactionWithSimpleAttr(en, INTERACTION_TYPE_PRESCRIPTIONS, "HCP", "Subject", "Subject 1").post();
        interactionWithSimpleAttr(en, INTERACTION_TYPE_PRESCRIPTIONS, "HCP", "Subject", "Subject 1").post();
        InteractionModel inter6 = interactionWithSimpleAttr(en, INTERACTION_TYPE_PRESCRIPTIONS, "HCP", "Test", "Test 1").post();
        //1
        testInteractions(en.getInteractions(new GetEntityRequestParameters().filter(new SearchFilterGroup(
                equalsFilter("type", INTERACTION_TYPE_EMAIL)).and(startsWith("attributes.Subject", "S")))), 2, true, inter2, inter1);
        //2
        testInteractions(en.getInteractions(new GetEntityRequestParameters().filter(new SearchFilterGroup(
                equalsFilter("type", INTERACTION_TYPE_EMAIL)).and(containsWordStartingWith("attributes.Test", "Te")))), 1, true, inter3);
        //3
        testInteractions(en.getInteractions(new GetEntityRequestParameters().filter(new SearchFilterGroup(
                equalsFilter("type", INTERACTION_TYPE_PRESCRIPTIONS)).and(missing("attributes.Subject", "Subject")))), 1, true, inter6);
    }

    @TmsLink("RP-TC-200")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Ordering of Interactions")
    public void runRpTc200() throws Exception {
        EntityModel en = new EntityModel(ENTITY_TYPE_HCP).post();
        InteractionModel im1 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel im2 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 2").post();
        InteractionModel im3 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 2").post();
        InteractionModel im4 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 4").post();
        InteractionModel im5 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 1").post();
        InteractionModel im6 = interactionWithSimpleAttr(en, INTERACTION_TYPE_EMAIL, "From", "Subject", "Subject 3").post();
        //1
        log.warn("sort(\"attributes.Subject\")");
        testInteractions(en.getInteractions(new GetEntityRequestParameters().sort("attributes.Subject")),
                im4, im6, im3, im2, im5, im1);
        //2
        log.warn("sort(\"createdTime\").order(\"asc\")");
        testInteractions(en.getInteractions(new GetEntityRequestParameters().sort("createdTime").order("asc")),
                im1, im2, im3, im4, im5, im6);
        //3
        testInteractions(en.getInteractions(new GetEntityRequestParameters().sort("timestamp").order("desc")),
                im6, im5, im4, im3, im2, im1);
        //4
        testInteractions(en.getInteractions(new GetEntityRequestParameters().sort("updatedTime").order("asc")),
                im1, im2, im3, im4, im5, im6);
        //5
        testInteractions(en.getInteractions(), im6, im5, im4, im3, im2, im1);
    }

    @TmsLink("RP-TC-8709")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Reload interactions after deleting")
    public void runRpTc8709() throws Exception {
        EntityModel em1 = createHCP(Config.getTenantUrl(), "AMA");
        EntityModel em2 = createHCP(Config.getTenantUrl(), "AMA");
        CrosswalkModel cw = generateCrosswalk("AMA");

        InteractionModel im1 = new InteractionModel(cw, Config.getTenantUrl());
        im1.setType(INTERACTION_TYPE_EMAIL);
        im1.addAttributeToModel("Subject", new SimpleAttributeModel("Subj"));
        InteractionMemberModel memberTo = new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "memberTypes/To")
                .addSubMember(new InteractionSubMember(em1.getUri()));
        im1.addMember("To", memberTo);
        InteractionMemberModel memberFrom = new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "memberTypes/From")
                .addSubMember(new InteractionSubMember(em2.getUri()));
        im1.addMember("From", memberFrom);
        InteractionModel im2 = new InteractionModel(im1.toString());

        im1.post();
        List<InteractionModel> list1 = em1.getInteractions().getInteractions();
        assertEquals(list1.size(), 1, "Expected 1 interaction after loading");

        im1.delete();
        List<InteractionModel> list2 = em1.getInteractions().getInteractions();
        assertEquals(list2.size(), 0, "Expected 0 interaction after deleting");

        im2.postOnly(false);
        List<InteractionModel> list3 = em1.getInteractions().getInteractions();
        assertEquals(list3.size(), 1, "Expected 1 interaction after re-loading");
    }

    @TmsLink("RP-TC-11413")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Check interactions after merge/split member")
    public void runRpTc11413() throws Exception {
        EntityModel em1 = createHCP(Config.getTenantUrl(), "AMA");
        EntityModel em2 = createHCP(Config.getTenantUrl(), "AHA");
        String uri1 = em1.getUri();
        String uri2 = em2.getUri();

        InteractionModel im1 = interactionWithResolvingMembersByCrosswalks(INTERACTION_TYPE_EMAIL, "From", em1.getCrosswalks());
        im1.post();

        EntityService.mergeEntities(em1, em2, em2);
        SmartWaiting.waitForEntitiesToBeMerged(em1, em2);
        String memberUri = im1.getMembers().get("From").getMembers().get(0).getObjectURI();
        log.info(String.format("Check interaction members objectUri %s equals to initial uri %s", memberUri, uri1));
        assertEquals(uri1, memberUri, "interaction's objectUri differ from initial entity uri!");

        im1.refresh();
        String interactionMemberLabel = im1.getMembers().get("From").getMembers().get(0).getLabel();
        String hcpWinnerLabel = em2.getLabel();
        log.info(String.format("Check interaction members label '%s' is resolving from winner entity %s", interactionMemberLabel, uri2));
        assertEquals(interactionMemberLabel, hcpWinnerLabel, "interaction's objectUri differs from initial entity uri!");

        EntityService.unmergeEntities(em2, em1);
        SmartWaiting.waitForEntitiesToBeUnMerged(em2, em1);
        im1.refresh();
        memberUri = im1.getMembers().get("From").getMembers().get(0).getObjectURI();
        log.info(String.format("Check interaction members uri %s equals to winner uri %s", memberUri, uri1));
        assertEquals(uri1, im1.getMembers().get("From").getMembers().get(0).getObjectURI(), "member's uri does not contain initial uri member");

        interactionMemberLabel = im1.getMembers().get("From").getMembers().get(0).getLabel();
        String hcpInitialLabel = em1.getLabel();
        log.info(String.format("Check interaction members label '%s' is resolving from initial entity %s", interactionMemberLabel, uri1));
        assertEquals(interactionMemberLabel, hcpInitialLabel, "interaction's objectUri differs from initial entity uri!");
    }

    @TmsLink("RP-TC-13390")
    @Test(retryAnalyzer = RetryAnalyzer.class, groups = {"regression"}, description = "Schemaless interaction with entity recognition")
    public void runRpTc13390() throws Exception {
        if (!GlobalConfig.isUseSpannerForInteractions()) {
            throw new SkipException("Test can be run with Interactions V2 only! Test is skipped");
        }
        // 1. add HCP and store crosswalk id
        EntityModel em1 = createHCP(Config.getTenantUrl(), "AHA");

        // 2. add SL interaction from json with HCP crosswalk id as attribute value
        setVar("hcp_crosswalk_value", em1.get().getCrosswalks().getFirst().getValue());
        String slInteraction = new Request(Request.Type.POST, Config.getTenantUrl() + "/rawInteractions",
                null, loadFile("schemaless-interaction.json")).execute();
        JsonElement slInteractionJson = JsonParser.parseString(slInteraction).getAsJsonArray().get(0)
                .getAsJsonObject().get("object");
        String expectedInteractionUri = slInteractionJson.getAsJsonObject().get("uri").getAsString();
        assertTrue(slInteractionJson.getAsJsonObject().get("type").getAsString().contains("Unclassified"));
        // check entity recognition for SL interaction, GCP only
        if (isGCP()) {
            waitForQueueStayEmpty(Config.getTenantId(), true, false, 5);

            // 3. Get interactions for specified HCP with regular interaction api
            InteractionModel im = em1.getInteractions().getInteractions().getFirst();

            // 4. Check interaction id equals to SL interaction id
            String actualInteractionUri = im.getUri();
            assertEquals(expectedInteractionUri, actualInteractionUri);
        }
    }

    private void testInteractions(GetInteractionResponse interactionResponse, InteractionModel... interactions) {
        int responseSize = interactionResponse.getInteractions() == null ? 0 : interactionResponse.getInteractions().size();
        assertEquals(responseSize, interactions.length,
                "Expected " + interactions.length + " interactions, but got " + interactionResponse.getInteractions().size());

        for (int i = 0; i < responseSize; i++) {
            assertEquals(interactionResponse.getInteractions().get(i).getUri(), interactions[i].getUri(), "Incorrect order of interactions:");
        }
    }

    private void testInteractions(GetInteractionResponse interactionResponse, int totalFetched, boolean fetchedAll, InteractionModel... interactions) {
        assertEquals(fetchedAll, (boolean) interactionResponse.getFetchedAll());
        assertEquals(interactionResponse.getTotalFetched().intValue(), totalFetched, "TotalFetched");
        testInteractions(interactionResponse, interactions);
    }
}
