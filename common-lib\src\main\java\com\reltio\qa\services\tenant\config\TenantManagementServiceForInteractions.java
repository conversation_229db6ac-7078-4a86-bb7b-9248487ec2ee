package com.reltio.qa.services.tenant.config;

import com.google.gson.JsonElement;
import com.reltio.qa.Config;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.request.Request;
import com.reltio.qa.services.AccountService;
import com.reltio.qa.services.TenantManagementService;
import lombok.extern.log4j.Log4j2;

import javax.ws.rs.core.UriBuilder;

import static com.reltio.qa.request.Request.Type.PUT;

@Log4j2
public final class TenantManagementServiceForInteractions {

    public static final String SPANNER_STORAGE_INTERACTIONS_ID = "spannerdb.interactions.v2";

    private TenantManagementServiceForInteractions() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * Enables V2 interactions for a tenant.
     *
     * @param tenantId The ID of the tenant for which to enable V2 interactions.
     * @return JsonElement containing the response from the server.
     * @throws CommandException If there is an error executing the request.
     */
    public static JsonElement enableInteractionsV2Config(String tenantId) throws CommandException {
        UriBuilder uriBuilder = UriBuilder.fromUri(Config.getPlatformUrl())
                .path("tenants")
                .path(tenantId)
                .path("enableV2Interactions")
                .queryParam("force", "true");

        return new Request(AccountService.ADMIN_NAME, PUT, uriBuilder.build().toString()).executeJson();
    }

    /**
     * Check if a repository version for spanner storage is set to v2 in the tenant config
     * @return true if the repository version is set to v2, false otherwise
     */
    public static boolean isInteractionsRepositoryVersionSetToV2() {
        log.info("Check Interactions V2 is enabled...");
        String repositoryVersion = DataStorageConfigUtils
                .getInteractionsRepositoryVersion(TenantManagementService.getTenant(), SPANNER_STORAGE_INTERACTIONS_ID);

        return "v2".equals(repositoryVersion);
    }

}
