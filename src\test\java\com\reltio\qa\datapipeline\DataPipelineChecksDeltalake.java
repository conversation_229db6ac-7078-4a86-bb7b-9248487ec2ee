package com.reltio.qa.datapipeline;

import com.google.gson.JsonElement;
import com.reltio.qa.model.EntityModel;
import com.reltio.qa.model.Interactions.InteractionModel;
import com.reltio.qa.model.RelationModel;
import com.reltio.qa.services.datapipeline.DataPipelineDeltalakeProvider;
import com.reltio.qa.enums.common.DataPipelineGBQTables;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Map;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;
import static org.testng.Assert.assertFalse;
import static com.reltio.qa.helpers.CommonStrings.WINNER_ID_FIELD;
import static com.reltio.qa.helpers.CommonStrings.LOSER_ID_FIELD;

@Log4j2
public class DataPipelineChecksDeltalake implements IDataPipelineChecks {

    private DataPipelineDeltalakeProvider dps;

    public DataPipelineChecksDeltalake(DataPipelineDeltalakeProvider provider) {
        this.dps = provider;
    }

    @Override
    public int getRowCountAll(String tableName) {
        long count = dps.executeCountQuery(tableName, null);
        if (count > Integer.MAX_VALUE) {
            return Integer.MAX_VALUE;
        }
        return (int) count;
    }

    @Override
    public int getRowCount(String tableName, String fieldId, String value) {
        String whereClause = "`" + fieldId + "` = ?";
        long count = dps.executeCountQuery(tableName, whereClause, value);
        if (count > Integer.MAX_VALUE) {
            return Integer.MAX_VALUE;
        }
        return (int) count;
    }

    @Override
    public String getFieldValue(String table, String searchField, String searchValue, String fieldId, String nestedFieldId) {
        return null;
    }

    public JsonElement getRecord(String tableName, String filterCondition) {
        return null;
    }

    @Override
    public void checkTableInSyncByField(String table, String fieldId, String value) {
        checkTableInSyncByFieldWithCondition(table, fieldId, value, "");
    }

    @Override
    public void checkTableInSyncByFieldPartialMatch(String table, String fieldId, String value, String fieldId2, String value2) {
    }

    @Override
    public void checkTableInSyncByFieldWithCondition(String table, String fieldId, String value, String condition) {
        String sql = String.format("SELECT * FROM `%s` WHERE `%s` = ?", table.replace(".", "`.`"), fieldId);

        if (condition != null && !condition.isEmpty()) {
            sql += condition;
        }

        JsonElement result = dps.executeQuery(sql, value);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching record found in table %s where %s = %s %s",
                table, fieldId, value, condition != null ? condition : ""));
    }

    @Override
    public void checkTableInSyncByFieldByEndDate(String table, String fieldId, String value, String condition) {
    }

    @Override
    public void checkTableInSyncByCountWithDeletedTrue(String table, String fieldId, String value, int expectedCount) {
        String landingTable = table + "_landingtable";
        checkTableInSyncByCountWithCondition(landingTable, fieldId, value, " AND `deleted` = true", expectedCount);
    }

    @Override
    public void checkTableInSyncByCountWithDeletedFalse(String table, String fieldId, String value, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByCountWithMissingMatches(String table, String fieldId, String value, int expectedCount) {
        String whereClause = "`" + fieldId + "` = ? AND exists(not_matches, item -> true)";
        long actualCount = dps.executeCountQuery(table, whereClause, value);
        assertEquals(actualCount, expectedCount,
            String.format("Missing matches count mismatch for table %s where %s = %s. Expected: %d, Actual: %d",
                table, fieldId, value, expectedCount, actualCount));
    }

    @Override
    public void checkTableInSyncByCountWithEmptyMatches(String table, String fieldId, String value, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByCountWithPotentialMatches(String table, String fieldId, String value, int expectedCount) {
        // Following GBQ/Snowflake pattern: ignore fieldId/value parameters and just count rows with potential matches
        // This method signature is misleading across all adapters - it should just count potential matches
        String whereClause = "exists(potential_matches, item -> true)";
        long actualCount = dps.executeCountQuery(table, whereClause);
        assertEquals(actualCount, expectedCount,
            String.format("Potential matches count mismatch for table %s. Expected: %d, Actual: %d",
                table, expectedCount, actualCount));
    }

    @Override
    public void checkTableInSyncByCountDistinctWithDeletedTrue(String table, String fieldId, String value, String fieldCount, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByCountDistinctWithDeletedFalse(String table, String fieldId, String value, String fieldCount, int expectedCount) {
    }

    @Override
    public void checkTableInSyncBySchemaFieldCount(String schema, String table, String column, int expectedCount) {
    }

    @Override
    public void checkTableInSyncBySchemaFieldCountIdField(String schema, String table, int expectedCount) {
    }

    @Override
    public void checkTableInSyncBySchemaFieldCountIsOvField(String schema, String table, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByMissingAttribute(String schema, String table, String attribute) {
    }

    @Override
    public void checkTableInSyncByCount(String table, String fieldId, String value, int expectedCount) {
        checkTableInSyncByCountWithCondition(table, fieldId, value, "", expectedCount);
    }

    @Override
    public void checkTableInSyncByCountAll(String table, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByCountWithCondition(String table, String fieldId, String value, String condition, int expectedCount) {
        String whereClause = "`" + fieldId + "` = ? " + condition;
        long actualCount = dps.executeCountQuery(table, whereClause, value);

        assertEquals(actualCount, expectedCount,
                String.format("Row count mismatch for table %s where %s = %s %s. Expected: %d, Actual: %d",
                        table, fieldId, value, condition, expectedCount, actualCount));
    }

    @Override
    public void checkTableInSyncByCountDistinctWithCondition(String table, String fieldId, String value, String fieldCount, String condition, int expectedCount) {
    }

    /**
     * Helper method for two-level nested fields like Address[i].value.AddressLine1[0].value or Phone[i].value.Number[0].value
     * Following GBQ pattern: checkTableInSyncByTwoLevelNestedFieldsWithCondition
     */
    public void checkTableInSyncByTwoLevelNestedFieldWithArrayIndex(String table, String level1Field, String level2Field,
                                                                   String whereField, String whereValue, String selectValue,
                                                                   String tableType, int arrayIndex, String condition) {
        String jsonTable = table.replace(tableType, tableType + "_json");
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `%s` = ? " +
            "AND get_json_object(attributes, '$.%s[%d].value.%s[0].value') = ?",
            jsonTable.replace(".", "`.`"), whereField, level1Field, arrayIndex, level2Field
        );

        if (condition != null && !condition.isEmpty()) {
            sql += condition;
        }

        JsonElement result = dps.executeQuery(sql, whereValue, selectValue);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching two-level nested field found in table %s where %s = %s, %s[%d].value.%s[0].value = %s",
                jsonTable, whereField, whereValue, level1Field, arrayIndex, level2Field, selectValue));
    }

    /**
     * Helper method to check that expected nested field is present in the Deltalake table with specific array index
     *
     */
    public void checkTableInSyncByNestedFieldWithArrayIndex(String table, String nestedField, String selectField,
                                                           String whereField, String whereValue, String selectValue,
                                                           String tableType, int arrayIndex, String condition) {
        String jsonTable = table.replace(tableType, tableType + "_json");
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `%s` = ? " +
            "AND get_json_object(attributes, '$.%s[%d].%s') = ?",
            jsonTable.replace(".", "`.`"), whereField, nestedField, arrayIndex, selectField
        );

        if (condition != null && !condition.isEmpty()) {
            sql += condition;
        }

        JsonElement result = dps.executeQuery(sql, whereValue, selectValue);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching nested field found in table %s where %s = %s, nestedField = %s[%d], selectField = %s, value = %s",
                jsonTable, whereField, whereValue, nestedField, arrayIndex, selectField, selectValue));
    }

    @Override
    public void checkTableInSyncByHcpFirstNameField(String table, String uri, String value) {
        checkTableInSyncByNestedFieldWithExists(table, "FirstName", "value", "uri", uri, value, "entities", "=");
    }

    @Override
    public void checkTableInSyncByHcpFirstNameFieldOvCondition(String table, String uri, String value, String condition) {
        int arrayIndex = "true".equals(condition) ? 0 : 1;
        String ovCondition = String.format(" AND get_json_object(attributes, '$.FirstName[%d].ov') = '%s'", arrayIndex, condition);

        checkTableInSyncByNestedFieldWithArrayIndex(table, "FirstName", "value", "uri", uri, value, "entities", arrayIndex, ovCondition);
    }

    @Override
    public void checkTableInSyncByHcpAddressFieldOvCondition(String table, String uri, String value, String condition1, String condition2) {
        // Address has two-level nested structure: Address[i].value.AddressLine1[0].value
        // Now using the two-level nested helper method (following GBQ pattern)
        int arrayIndex = "true".equals(condition1) ? 0 : 1;
        String ovCondition = String.format(" AND get_json_object(attributes, '$.Address[%d].ov') = '%s'", arrayIndex, condition1);
        checkTableInSyncByTwoLevelNestedFieldWithArrayIndex(table, "Address", "AddressLine1", "uri", uri, value, "entities", arrayIndex, ovCondition);
    }

    @Override
    public void checkTableInSyncByHcpPhoneFieldOvCondition(String table, String uri, String value, String condition1, String condition2) {
        // Phone method was working correctly with simple array index approach - reverting to original
        int arrayIndex = "true".equals(condition1) ? 0 : 1;
        String ovCondition = String.format(" AND get_json_object(attributes, '$.Phone[%d].ov') = '%s'", arrayIndex, condition1);
        checkTableInSyncByNestedFieldWithArrayIndex(table, "Phone", "value", "uri", uri, value, "entities", arrayIndex, ovCondition);
    }

    @Override
    public void checkTableInSyncByLocationAddressLine1Field(String table, String uri, String value) {
        checkTableInSyncByNestedFieldWithExists(table, "AddressLine1", "value", "uri", uri, value, "entities", "=");
    }

    @Override
    public void checkTableInSyncByEmailFromField(String table, String uri, String value) {
        checkTableInSyncByInteractionMember(table, uri, value, "email member");
    }

    @Override
    public void checkTableInSyncByEmailToField(String table, String uri, String value) {
        checkTableInSyncByEmailFromField(table, uri, value);
    }

    @Override
    public void checkTableInSyncByEmailFromFieldCount(String table, String uri, String value, int expectedCount) {
        String sql = String.format(
            "SELECT COUNT(*) as count FROM `%s` WHERE `uri` = ? " +
            "AND array_contains(" +
                "transform(flatten(transform(map_values(members), x -> x.members)), m -> m['objectURI']), " +
                "CONCAT('entities/', ?)" +
            ")",
            table.replace(".", "`.`")
        );

        JsonElement result = dps.executeQuery(sql, uri, value);
        assertFalse(result.getAsJsonArray().isEmpty(), "Query result is empty: " + sql);

        int actualCount = result.getAsJsonArray().get(0).getAsJsonObject().get("count").getAsInt();
        assertEquals(actualCount, expectedCount,
                String.format("Email member count mismatch for table %s where uri = %s, member id = %s. Expected: %d, Actual: %d",
                        table, uri, value, expectedCount, actualCount));
    }

    @Override
    public void checkTableInSyncByFirstNameFieldCount(String table, String uri, String value, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByNestedField(String table, String uri, String nestedFieldId, String fieldId, String value) {
        String jsonPath = nestedFieldId;
        if (nestedFieldId.contains(".")) {
            jsonPath = nestedFieldId.substring(nestedFieldId.lastIndexOf('.') + 1);
        }
        
        String baseTableName = table.contains(".") ? table.substring(table.lastIndexOf('.') + 1) : table;
        checkTableInSyncByNestedFieldWithExists(table, jsonPath, fieldId, "uri", uri, value, baseTableName, "=");
    }

    @Override
    public void checkTableInSyncByNestedPlainField(String table, String uri, String nestedFieldId, String fieldId, String value) {
    }

    @Override
    public void checkTableInSyncByEventHcpField(String table, String uri, String value) {
        checkTableInSyncByInteractionMember(table, uri, value, "HCP member");
    }

    @Override
    public void checkTableInSyncByEventHcoField(String table, String uri, String value) {
        checkTableInSyncByInteractionMember(table, uri, value, "HCO member");
    }

    @Override
    public void checkTableInSyncByCrosswalksRefEntityField(String table, String uri) {
    }

    @Override
    public void checkTableInSyncByCrosswalksUriFieldPartialMatch(String table, String uri, String value) {
    }

    @Override
    public void checkTableInSyncByCrosswalksSourceFieldPartialMatch(String table, String uri, String value) {
    }

    @Override
    public void checkTableInSyncByCrosswalksAttributesCount(String table, String uri, String crosswalksUri, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByCrosswalksAttributeFieldCount(String table, String uri, String value, String crosswalksUri, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByMergeType(String table, String uri, String value, String mergeType, int expectedCount) {
    }

    @Override
    public void checkTableInSyncByPhoneNumber(String table, String uri, String field1Value, String field2Value) {
        String jsonTable = table.replace("entities", "entities_json");
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `uri` = ? " +
            "AND exists(" +
                "from_json(get_json_object(attributes, '$.Phone'), " +
                "'array<struct<label:string,value:struct<Type:array<struct<value:string>>,Number:array<struct<value:string>>,AreaCode:array<struct<value:string>>>,uri:string,ov:boolean>>'), " +
                "phone -> phone.value.Type[0].value = ? AND phone.value.Number[0].value = ?" +
            ")",
            jsonTable.replace(".", "`.`")
        );

        JsonElement result = dps.executeQuery(sql, uri, field1Value, field2Value);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching Phone found in table %s where uri = %s, Phone.Type = %s, Phone.Number = %s",
                jsonTable, uri, field1Value, field2Value));
    }

    @Override
    public void checkTableInSyncByPhoneAreaCode(String table, String uri, String field1Value, String field2Value) {
        String jsonTable = table.replace("entities", "entities_json");
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `uri` = ? " +
            "AND exists(" +
                "from_json(get_json_object(attributes, '$.Phone'), " +
                "'array<struct<label:string,value:struct<Type:array<struct<value:string>>,Number:array<struct<value:string>>,AreaCode:array<struct<value:string>>>,uri:string,ov:boolean>>'), " +
                "phone -> phone.value.Type[0].value = ? AND phone.value.AreaCode[0].value = ?" +
            ")",
            jsonTable.replace(".", "`.`")
        );

        JsonElement result = dps.executeQuery(sql, uri, field1Value, field2Value);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching Phone found in table %s where uri = %s, Phone.Type = %s, Phone.AreaCode = %s",
                jsonTable, uri, field1Value, field2Value));
    }

    @Override
    public void checkTableInSyncByDeploymentExternalTeamField(String table, String uri, String value) {
    }

    @Override
    public void checkTableInSyncByActivitiesField(String table, String nestedFieldId, String fieldId, String uri, String value) {
        checkTableInSyncByDirectArrayField(table, "activityId", uri, "items", fieldId, "=", value);
    }

    @Override
    public void checkTableInSyncByActivitiesDeltaField(String table, String nestedFieldId, String uri, String value) {
        checkTableInSyncByDirectArrayField(table, "activityId", uri, "items", "delta", "LIKE", "%" + value + "%");
    }

    /**
     * Helper method to check that expected nested field value exists anywhere in the array using exists() function
     */
    private void checkTableInSyncByNestedFieldWithExists(String table, String nestedField, String selectField,
                                                        String whereField, String whereValue, String selectValue,
                                                        String tableType, String operator) {
        String jsonTable = table.replace(tableType, tableType + "_json");
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `%s` = ? " +
            "AND exists(" +
                "from_json(get_json_object(attributes, '$.%s'), 'array<struct<%s:string,ov:boolean>>'), " +
                "item -> item.%s %s ?" +
            ")",
            jsonTable.replace(".", "`.`"), whereField, nestedField, selectField, selectField, operator
        );

        JsonElement result = dps.executeQuery(sql, whereValue, selectValue);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching nested field found in table %s where %s = %s, attributes.$.%s.%s %s %s",
                jsonTable, whereField, whereValue, nestedField, selectField, operator, selectValue));
    }



    /**
     * Helper method to check that expected field value exists in direct array columns using exists() function
     */
    private void checkTableInSyncByDirectArrayField(String table, String whereField, String whereValue,
                                                   String arrayField, String selectField, String operator, String selectValue) {
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `%s` = ? " +
            "AND exists(%s, item -> item.%s %s ?)",
            table.replace(".", "`.`"), whereField, arrayField, selectField, operator
        );

        JsonElement result = dps.executeQuery(sql, whereValue, selectValue);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching array field found in table %s where %s = %s, %s.%s %s %s",
                table, whereField, whereValue, arrayField, selectField, operator, selectValue));
    }

    /**
     * Validates that an interaction member exists in the members map structure.
     */
    private void checkTableInSyncByInteractionMember(String table, String uri, String value, String memberType) {
        String sql = String.format(
            "SELECT * FROM `%s` WHERE `uri` = ? " +
            "AND array_contains(" +
                "transform(flatten(transform(map_values(members), x -> x.members)), m -> m['objectURI']), " +
                "CONCAT('entities/', ?)" +
            ")",
            table.replace(".", "`.`")
        );

        JsonElement result = dps.executeQuery(sql, uri, value);
        assertFalse(result.getAsJsonArray().isEmpty(),
            String.format("No matching %s found in table %s where uri = %s, member id = %s",
                memberType, table, uri, value));
    }



    /**
     * Helper method to check if an entity exists in an array column for another entity
     */
    private void checkEntityInArray(String table, String arrayField, String entityId1, String entityId2, String message) {
        String sql = String.format(
            "SELECT COUNT(*) as count FROM `%s` " +
            "WHERE `entityId` = ? AND exists(%s, item -> item.entityId = ?)",
            table.replace(".", "`.`"), arrayField
        );

        JsonElement result = dps.executeQuery(sql, entityId1, entityId2);
        assertFalse(result.getAsJsonArray().isEmpty(), "Query result is empty: " + sql);

        int count = result.getAsJsonArray().get(0).getAsJsonObject().get("count").getAsInt();
        assertTrue(count > 0, message);
    }

    @Override
    public void checkEntitiesNotMatch(String table, String entityId1, String entityId2) {
        checkEntityInArray(table, "not_matches", entityId1, entityId2,
                String.format("Entities %s and %s should not match in table %s", entityId1, entityId2, table));
    }

    @Override
    public void checkEntitiesManualMatch(String table, String entityId1, String entityId2) {
        checkEntityInArray(table, "manual_matches", entityId1, entityId2,
                String.format("Entities %s and %s should have a manual match in table %s", entityId1, entityId2, table));
    }

    @Override
    public void checkEntitiesPotentialMatch(String table, String entityId1, String entityId2) {
        // Implementation not needed for current Deltalake tests
    }

    @Override
    public void checkMatchesAfterMerge(String mergesTable, String matchesTable, List<String> mergeKeys) {
        if (mergeKeys != null) {
            mergeKeys.forEach(merge -> {
                checkTableInSyncByFieldWithCondition(mergesTable, WINNER_ID_FIELD, getWinnerId(merge),
                    String.format(" AND `%s` = '%s'", LOSER_ID_FIELD, getLoserId(merge)));
            });
        }
    }

    public void verifyTenantEntitiesCount(List<EntityModel> entities, String entitiesTable) { }
    public void verifyTenantRelationsCount(List<RelationModel> relations, String relationsTable) { }
    public void verifyTenantInteractionsCount(List<InteractionModel> interactions, String interactionsTable) { }
    public void verifyEntities(List<EntityModel> entities, String entitiesTable) { }
    public void verifyRelations(List<RelationModel> relations, String relationsTable) { }
    public void verifyInteractions(List<InteractionModel> interactions, String interactionsTable) { }
    public void verifyMatches(String matchesTable) { }
    public void verifyMerges(List<EntityModel> mergeList, String mergesTable) { }

    @Override
    public void checkMatchesAfterUnmerge(String mergesTable, String matchesTable, List<String> mergeKeys) {
        if (mergeKeys != null) {
            mergeKeys.forEach(merge -> {
                checkTableInSyncByCountWithCondition(mergesTable, WINNER_ID_FIELD, getWinnerId(merge),
                    String.format(" AND `%s` = '%s'", LOSER_ID_FIELD, getLoserId(merge)), 0);
                checkEntitiesNotMatch(matchesTable, getWinnerId(merge), getLoserId(merge));
            });
        }
    }

    public Map<String, String> getColumnTypes(String tableName) { return null; }
    public JsonElement executeQuery(String sqlQuery) { return null; }

    @Override
    public void checkGbqExpectedTablesOrViews(DataPipelineGBQTables... tables) {
    }
}