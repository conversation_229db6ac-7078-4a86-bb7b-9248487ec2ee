package com.reltio.qa.security;

import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.enums.devOps.TenantSize;
import com.reltio.qa.enums.match.MatchingStrategy;
import com.reltio.qa.enums.tasks.ExecutionType;
import com.reltio.qa.factories.DatabaseTestProviderFactory;
import com.reltio.qa.helpers.CommonStrings;
import com.reltio.qa.helpers.loaddata.LoadData;
import com.reltio.qa.model.*;
import com.reltio.qa.model.database.DatabaseResultSet;
import com.reltio.qa.service.retry.RetryAnalyzer;
import com.reltio.qa.services.*;
import com.reltio.qa.services.database.DatabaseTestProvider;
import com.reltio.qa.services.devOps.DevOpsTenantService;
import com.reltio.qa.services.pms.PlatformManagementTenantService;
import com.reltio.qa.utils.RandomGenerator;
import io.qameta.allure.TmsLink;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.reltio.qa.Config.getPlatformUrl;
import static com.reltio.qa.Config.isIsolatedEnvironment;
import static com.reltio.qa.services.AccountService.*;
import static com.reltio.qa.services.BusinessConfigurationService.updateConfig;
import static com.reltio.qa.services.PermissionService.deletePermissions;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForConsistency;
import static com.reltio.qa.services.devOps.DevOpsTenantService.deleteTenantUnchecked;
import static org.testng.Assert.assertNotNull;

public class SecurityTasks extends TestNGBaseTest {

    @TmsLink("RP-TC-7518")
    @Test(retryAnalyzer = RetryAnalyzer.class, description = "Secure and authorize global tasks endpoint")
    public void runRpTc7518() throws Exception {
        String tempTenant = RandomGenerator.getInstance().getRandomTenantId();
        String adminTenantUser = RandomGenerator.getInstance().getRandomName();
        String adminTempTenantUser = RandomGenerator.getInstance().getRandomName();
        TenantModel tenant;
        Account account = null;
        try {
            if (isIsolatedEnvironment()) {
                tenant = generateTenant(tempTenant, tempTenant);
            } else if (GlobalConfig.usePMS()) {
                String tenantConfig = PlatformManagementTenantService.generateTenantConfig(tempTenant, TenantSize.SMALL, MatchingStrategy.INCREMENTAL);
                tenant = TenantModel.fromJson(tenantConfig);
            } else {
                tenant = TenantManagementService.createTenant(DevOpsTenantService.generateTenantConfig(tempTenant, tempTenant, TenantSize.SMALL, MatchingStrategy.INCREMENTAL));
            }
            tenant.putCleanse(new CleanseModel().cleanseFunction("InitialsCleanser").type("com.reltio.sdk.cleanse.InitialsCleaser").module(Config.getS3BucketCleanse() + "/Sample/SampleCleanseFunctions.jar"));
            TenantManagementService.createTenant(tenant);
            updateConfig(tempTenant, loadFile("resources:config.json"));

            updateConfig(loadFile("resources:config.json"));
            deletePermissions();

            account = addNewAccount(new Account(adminTenantUser).setTenants(Config.getTenantId())
                    .setRoles("ROLE_USER", "ROLE_API", "ROLE_ADMIN_TENANT_" + Config.getTenantId()));
            Account accountTempTenant = addNewAccount(new Account(adminTempTenantUser).setTenants(Config.getTenantId())
                    .setRoles("ROLE_USER", "ROLE_API", "ROLE_ADMIN_TENANT_" + tempTenant));

            LoadData.loadEntitiesFromJsonTemplate(loadFile("7518-entity.json"), 30, Config.getTenantId());
            LoadData.loadEntitiesFromJsonTemplate(loadFile("7518-entity.json"), 30, tempTenant);

            //1
            try {
                HttpService.doGet(getPlatformUrl() + "/tasks", new HashMap<>());  // need request without auth token
                Assert.fail("Expected auth error");
            } catch (Exception e) {
                Assert.assertTrue(e.getMessage().contains("unauthorized"), "Expected auth error");
            }

            //2
            String task1 = TaskService.deleteEntities(new PostTaskRequestParameters().user(accountTempTenant.getUsername()).tenantId(tempTenant).entityType("HCP").mode("soft")).get(0).getId();
            String task2 = TaskService.deleteEntities(new PostTaskRequestParameters().user(account.getUsername()).tenantId(Config.getTenantId()).entityType("HCP").mode("soft")).get(0).getId();

            List<String> tasks = TaskService.getTasks(adminTenantUser, getPlatformUrl(), null, new GetTaskRequestParameters())
                    .stream().map(TaskModel::getId).toList();
            Assert.assertTrue(tasks.contains(task2), "No access to this tenant for adminTenant");
            Assert.assertFalse(tasks.contains(task1), "Unexpected access to another tenants for adminTenant");

            //3
            tasks = TaskService.getTasks(null, getPlatformUrl(), null, new GetTaskRequestParameters())
                    .stream().map(TaskModel::getId).toList();
            Assert.assertTrue(tasks.contains(task2), "No access to all tasks for admin");
            Assert.assertTrue(tasks.contains(task1), "No access to all tasks for admin");
        } finally {
            deleteTenantUnchecked(tempTenant);
            if (Objects.nonNull(account)) {
                account.close();
            }
        }
    }

    @TmsLink("RP-TC-8669")
    @Test(retryAnalyzer = RetryAnalyzer.class, description = "Run Duplicate crosswalks task by user with TENANT_ADMIN role and without ROLE_ADMIN")
    public void runRpTc8669() throws Exception {
        TenantModel tm = TenantManagementService.getTenant();
        int count = 50;
        String adminTenantUser = "adminTenant_Case1";
        deleteAccountIgnoreExceptions(adminTenantUser);
        try (Account account = createUser(adminTenantUser, "ROLE_USER", "ROLE_API", "ROLE_ADMIN_TENANT_" + Config.getTenantId())) {
            if (Config.isIsolatedEnvironment()) {
                TenantManagementService.cleanTenant(Config.getTenantId(), true);
            } else {
                TenantManagementService.cleanTenant();
            }

            updateConfig(loadFile("config.json"));

            List<String> eList = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                EntityModel eHCP1 = new EntityModel(CommonStrings.ENTITY_TYPE_HCP)
                        .addSimpleAttributeToModel("FirstName", ".FN1.AHA.ENTITY." + i + "." + RandomGenerator.getInstance().getRandomString(5));
                eList.add(eHCP1.toString());
            }

            // #
            List<EntityModel> entities = EntityService.postEntities("[" + String.join(",", eList) + "]");
            waitForConsistency(entities);

            PostTaskRequestParameters params = new PostTaskRequestParameters()
                    .user(account.getUsername())
                    .tenantId(Config.getTenantId())
                    .checkExistence(true)
                    .checkDeletedCrosswalks(false);

            TaskModel task = TaskService.duplicateCrosswalksCheck(ExecutionType.TENANT, params).waitWhileRunning().get(0);

            Assert.assertEquals(task.getCurrentState().numberOfProcessedObjects.intValue(), count);
            assertNotNull(task.getCurrentState().nonExistingCrosswalksFound);
            Assert.assertEquals(task.getCurrentState().nonExistingCrosswalksFound.intValue(), 0);

            // #
            try (DatabaseTestProvider<DatabaseResultSet> dbTestProvider = DatabaseTestProviderFactory.getDbTestProvider(tm)) {
                for (EntityModel entity : entities) {
                    dbTestProvider.insertExternalCrosswalk(
                            dbTestProvider.generateExternalCrosswalkId("Reltio", RandomGenerator.getInstance().getRandomString(5)),
                            entity.getId()
                    );
                }
            }

            task = TaskService.duplicateCrosswalksCheck(ExecutionType.TENANT, params).waitWhileRunning().get(0);
            Assert.assertEquals(task.getCurrentState().numberOfProcessedObjects.intValue(), count * 2);
            Assert.assertEquals(task.getCurrentState().nonExistingCrosswalksFound.intValue(), count);
        }
    }
}
