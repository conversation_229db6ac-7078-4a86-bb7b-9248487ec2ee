package com.reltio.qa;

import com.amazonaws.auth.BasicAWSCredentials;
import com.google.gson.reflect.TypeToken;
import com.reltio.qa.enums.common.CloudType;
import com.reltio.qa.services.VaultService;
import com.reltio.qa.services.cloud.CloudProviderService;
import com.reltio.qa.services.gcp.GCPCommon;
import com.reltio.qa.utils.GsonUtils;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Map;

import static com.reltio.qa.constants.EnvVarKeys.*;

@Log4j2
public class GlobalConfig { //NOSONAR: many fields and methods required

    protected static boolean initialize = false;
    protected static VaultService vault;

    // Credentials
    private static CredentialStorage ftpDnBApiCon;
    private static CredentialStorage dnbAccount;
    private static CredentialStorage mainUser;
    private static CredentialStorage rdmUser;
    private static CredentialStorage securityUser;
    private static CredentialStorage s3User;
    private static CredentialStorage s3CDUser;
    private static CredentialStorage s3SQSUser;
    private static CredentialStorage s3RiqUser;
    private static CredentialStorage s3RiqImport;
    private static Map<String, CredentialStorage> qaDynamoDBUsers;
    private static CredentialStorage qaDynamoDBCustomerRole;
    private static CredentialStorage qaDynamoDBCustomerCrossAccountRole;
    private static CredentialStorage qaDynamoDBSystemUser;
    private static CredentialStorage awsEMUser;
    private static CredentialStorage bitbucketApiCon;
    private static CredentialStorage deploymentToolCredentials;
    private static CredentialStorage apiSecretKey;
    private static BasicAWSCredentials basicAWSCredentials;
    private static CredentialStorage prometheusUser;
    private static CloudType cloudType;

    //Credentials for Google Cloud Platform
    private static String gcpApplicationCredentials;
    private static Map<String, String> gcpApplicationCredentialsMap;
    private static String gcpGoogleSheetsInternalCredentials;
    private static String gcpSpannerCredentials;
    private static String prometheusWorkspaceId;

    //Credentials for Azure
    private static CredentialStorage azureUser;
    private static CredentialStorage azureServiceBusAppClientExternal;
    private static CredentialStorage azureCosmosDBAppClient;
    private static CredentialStorage azureServiceBusAppClientInternal;
    private static CredentialStorage azureStorageUser;
    private static Map<String, String> azureSubscriptionIds;
    private static Map<String, String> azureSystemKeyspaceKey;
    private static Map<String, String> azureCommonKeyspaceKey;

    //RIQ-related credentials
    private static CredentialStorage quboleConnection;
    private static CredentialStorage databricksConnection;

    // Static tenants
    private static boolean useStaticTenants;
    private static long staticTenantsAmount;

    // PMS
    private static boolean usePMS;
    private static boolean useVelocityPack;
    private static String velocityPackConfig;

    // DBs
    private static boolean useCassandraDB;
    private static boolean useDynamoDBHistory;
    private static boolean useCosmosDBHistory;
    private static boolean useCustomDynamoDBIamRole;
    @Getter
    private static boolean skipHistoryCleanup;
    @Getter
    private static boolean useSpannerForInteractions;
    @Getter
    private static boolean useSpannerCloudFunction;

    // Eventhub namespaces
    private static boolean useServiceBusStandardNamespace;

    // Waiters
    private static boolean waitByAllPods;
    private static long waiterUpdateDelay;
    private static long waiterInitDelay;

    private static CredentialStorage mfaUser;

    // Other
    @Getter
    private static boolean useDevOpsEtalonUrl;
    private static String s3Region;
    private static boolean isInternalSnowflakeViews = getEnvPropertyAsBoolean("SNOWFLAKE_USE_INTERNAL_VIEWS", false);


    protected GlobalConfig() {
    }

    public static String getMfaUserEmail() {
        return mfaUser.getUrl();
    }

    public static String getMfaUserPassword() {
        return mfaUser.getKey();
    }

    public static CredentialStorage getftpDnBApiCon() {
        return ftpDnBApiCon;
    }

    public static CredentialStorage getDnBAccount() {
        return dnbAccount;
    }

    public static CredentialStorage getSecurityUser() {
        return securityUser;
    }

    public static CredentialStorage getMainUser() {
        return mainUser;
    }

    public static CredentialStorage getRdmUser() {
        return rdmUser;
    }

    public static BasicAWSCredentials getBasicAWSCredentials() {
        return basicAWSCredentials;
    }

    public static String getS3AccessKey() {
        return s3User.getUser();
    }

    public static String getS3SecretKey() {
        return s3User.getPassword();
    }

    public static String getPrometheusAccessKey() {
        return prometheusUser.getUser();
    }

    public static String getPrometheusSecretKey() {
        return prometheusUser.getPassword();
    }

    public static String getPrometheusWorkspaceId() {
        return prometheusWorkspaceId;
    }

    public static String getCDS3AccessKey() {
        return s3CDUser.getUser();
    }

    public static String getCDS3SecretKey() {
        return s3CDUser.getPassword();
    }

    public static String getS3Region() {
        return s3Region;
    }

    //Google application credentials

    /**
     * This method can return credentials, encoded credentials or link to file with credentials.
     * Use GlobalConfig.getGcpApplicationCredentials() instead to get parsed credentials
     */
    public static String getRawGcpApplicationCredentials() {
        if (gcpApplicationCredentials != null) {
            return gcpApplicationCredentials;
        }
        return gcpApplicationCredentialsMap.get("default");
    }

    public static String getRawGcpApplicationCredentials(String projectId) {
        return gcpApplicationCredentialsMap.get(projectId);
    }

    public static String getGcpApplicationCredentials() {
        return GCPCommon.getGCPCredentials(getRawGcpApplicationCredentials());
    }

    public static String getGcpApplicationCredentials(String projectId) {
        return GCPCommon.getGCPCredentials(getRawGcpApplicationCredentials(projectId));
    }

    public static String getGcpCredentialsForDPHTests() {
        return GCPCommon.getGCPCredentials(gcpApplicationCredentialsMap.get("idev-01-dph"));
    }

    public static String getGcpMessagingCredentials() {
        return GCPCommon.getGCPCredentials(gcpApplicationCredentialsMap.get("messaging"));
    }

    public static String getEncodedGcpApplicationCredentials() {
        return GCPCommon.getEncodedGCPCredentials();
    }

    public static String getEncodedGcpApplicationCredentials(String projectId) {
        return GCPCommon.getEncodedGCPCredentials(getRawGcpApplicationCredentials(projectId));
    }


    //Spanner

    public static synchronized String getGcpSpannerCredentials() {
        return GCPCommon.getGCPCredentials(gcpSpannerCredentials);
    }

    public static synchronized void setGcpSpannerCredentials(String gcpSpannerCredentials) {
        GlobalConfig.gcpSpannerCredentials = gcpSpannerCredentials;
    }

    public static String getGcpGoogleSheetsInternalCredentials() {
        return gcpGoogleSheetsInternalCredentials;
    }

    public static String getS3RiqImportAccessKey() {
        return s3RiqImport.getUser();
    }

    public static String getS3RiqImportSecretKey() {
        return s3RiqImport.getPassword();
    }

    public static String getS3RiqAccessKey() {
        return s3RiqUser.getUser();
    }

    public static String getS3RiqSecretKey() {
        return s3RiqUser.getPassword();
    }

    public static String getDynamoDBCustomerRole() {
        return qaDynamoDBCustomerRole.getUser();
    }

    public static String getDynamoDBCustomerExternalId() {
        return qaDynamoDBCustomerRole.getPassword();
    }

    public static String getDynamoDBCustomerCrossAccountRole() {
        return qaDynamoDBCustomerCrossAccountRole.getUser();
    }

    public static String getDynamoDBCustomerCrossAccountExternalId() {
        return qaDynamoDBCustomerCrossAccountRole.getPassword();
    }

    public static String getAwsEMAccessKey() {
        return awsEMUser.getUser();
    }

    public static String getAwsEMSecretKey() {
        return awsEMUser.getPassword();
    }

    // PMS
    public static boolean usePMS() {
        return usePMS;
    }

    public static boolean useVelocityPack() {
        return useVelocityPack;
    }

    public static String getVelocityPackConfig() {
        return velocityPackConfig;
    }

    // Databases
    public static boolean useCassandraDB() {
        return useCassandraDB;
    }

    public static boolean useDynamoDB() {
        return (getCloudType() == CloudType.AWS) && !useCassandraDB();
    }

    public static boolean useDynamoDBHistory() {
        return useDynamoDBHistory;
    }

    public static boolean useCosmosDBHistory() {
        return useCosmosDBHistory;
    }

    public static boolean useCustomDynamoDBIamRole() {
        return useCustomDynamoDBIamRole;
    }

    public static boolean useSpannerDB() {
        return (getCloudType() == CloudType.GCP) && !useCassandraDB();
    }

    public static boolean useCosmosDB() {
        return (getCloudType() == CloudType.AZURE) && !useCassandraDB();
    }

    public static boolean useServiceBusStandardNamespace() {
        return useServiceBusStandardNamespace;
    }

    public static CredentialStorage getAzureUser() {
        return azureUser;
    }

    public static String getAzureSBRole() {
        return azureUser.getUser();
    }

    public static String getAzureSBKey() {
        return azureUser.getPassword();
    }

    public static String getAzureNamespace() {
        return azureUser.getUrl();
    }

    // Azure Service Bus

    public static String getAzureServiceBusExternalClientId() {
        return azureServiceBusAppClientExternal.getUser();
    }

    public static String getAzureServiceBusExternalClientSecret() {
        return azureServiceBusAppClientExternal.getPassword();
    }

    public static String getAzureServiceBusExternalDirectory() {
        return azureServiceBusAppClientExternal.getUrl();
    }

    public static String getAzureServiceBusAppClientInternalClientId() {
        return azureServiceBusAppClientInternal.getUser();
    }

    public static String getAzureServiceBusAppClientInternalClientSecret() {
        return azureServiceBusAppClientInternal.getPassword();
    }

    public static String getAzureServiceBusAppClientInternalTenantId() {
        return azureServiceBusAppClientInternal.getUrl();
    }

    // Azure Cosmos DB

    public static String getAzureCosmosDBClientId() {
        return azureCosmosDBAppClient.getUser();
    }

    public static String getAzureCosmosDBClientSecret() {
        return azureCosmosDBAppClient.getPassword();
    }

    public static String getAzureCosmosDBTenantId() {
        return azureCosmosDBAppClient.getUrl();
    }

    // Azure Storage

    public static CredentialStorage getAzureStorageUser() {
        return azureStorageUser;
    }

    public static String getAzureStorageName() {
        return azureStorageUser.getUrl();
    }

    public static String getAzureStorageKey() {
        return azureStorageUser.getPassword();
    }

    public static Map<String, String> getAzureSubscription() {
        return azureSubscriptionIds;
    }

    public static Map<String, String> getAzureSystemKeyspaceKey() {
        return azureSystemKeyspaceKey;
    }

    public static Map<String, String> getAzureCommonKeyspaceKey() {
        return azureCommonKeyspaceKey;
    }

    public static String getApiSecretKey() {
        return apiSecretKey.getKey();
    }

    public static CredentialStorage getS3User() {
        return s3User;
    }

    public static CredentialStorage getS3SQSUser() {
        return s3SQSUser;
    }

    public static CredentialStorage getprometheusUser() {
        return prometheusUser;
    }

    public static CredentialStorage getBitbucketApiCon() {
        return bitbucketApiCon;
    }

    public static CredentialStorage getDeploymentToolCredentials() {
        return deploymentToolCredentials;
    }

    public static CredentialStorage getQaDynamoDBUser() {
        if (useCustomDynamoDBIamRole()) {
            return qaDynamoDBUsers.get("QA02");
        }
        return qaDynamoDBUsers.get("QA01");
    }

    public static CredentialStorage getQaDynamoDBSystemUser() {
        return qaDynamoDBSystemUser;
    }

    public static Map<String, CredentialStorage> getQaDynamoDBUsers() {
        return qaDynamoDBUsers;
    }

    public static CredentialStorage getQuboleConnection() {
        return quboleConnection;
    }

    public static CredentialStorage getDbcConnection() {
        return databricksConnection;
    }

    public static long getStaticTenantsAmount() {
        return staticTenantsAmount;
    }

    public static void init() {
        init(System.getenv(KEY_ENV_VAR_QA_VAULT_PATH));
    }

    public static boolean useStaticTenants() {
        return useStaticTenants;
    }

    public static void setUseStaticTenants(boolean value) {
        useStaticTenants = value;
    }

    /**
     * @param additionalVaultPath path can be specified for additional or overridden keys
     */
    public static void init(String additionalVaultPath) { //NOSONAR: too many lines
        if (!initialize) {

            // Vault service (main path is "common")
            CredentialStorage vaultConfig = new CredentialStorage(
                    "https://vault.reltio.com:8200",
                    System.getenv(KEY_ENV_VAR_QA_VAULT_TOKEN)
            );
            vault = new VaultService(vaultConfig, additionalVaultPath);
            cloudType = System.getenv(KEY_ENV_VAR_CLOUD_TYPE) != null ?
                    CloudType.valueOf(System.getenv(KEY_ENV_VAR_CLOUD_TYPE)) : null;

            useCassandraDB = getEnvPropertyAsBoolean("USE_CASSANDRA_DATA", false);

            useDynamoDBHistory = getEnvPropertyAsBoolean("USE_DYNAMODB_HISTORY", false);
            useCosmosDBHistory = getEnvPropertyAsBoolean("USE_COSMOS_HISTORY", false);

            useCustomDynamoDBIamRole = getEnvPropertyAsBoolean("USE_CUSTOM_DYNAMODB_IAM_ROLE", false);

            // Temporary flag to disable in case of issues with Spanner
            useSpannerCloudFunction = getEnvPropertyAsBoolean(KEY_ENV_VAR_USE_SPANNER_CLOUD_FUNCTION, true);

            useSpannerForInteractions = getEnvPropertyAsBoolean(KEY_ENV_VAR_NEW_SPANNER_INTERACTIONS, false);

            useServiceBusStandardNamespace = getEnvPropertyAsBoolean("USE_SERVICE_BUS_STANDARD_NAMESPACE", true);

            usePMS = getEnvPropertyAsBoolean("USE_PMS", false);
            useVelocityPack = getEnvPropertyAsBoolean("USE_VELOCITY_PACK", false);
            velocityPackConfig = getEnvProperty("VELOCITY_PACK_CONFIG");

            azureUser = new CredentialStorage(
                    getEnvProperty("QA_AZURE_NAMESPACE"),
                    getEnvProperty("QA_AZURE_ROLE"),
                    getEnvProperty("QA_AZURE_KEY")
            );

            azureServiceBusAppClientExternal = new CredentialStorage(
                    getEnvProperty("AZURE_SERVICE_BUS_EXTERNAL_DIRECTORY"),
                    getEnvProperty("AZURE_SERVICE_BUS_EXTERNAL_CLIENT_ID"),
                    getEnvProperty("AZURE_SERVICE_BUS_EXTERNAL_CLIENT_SECRET")
            );

            azureServiceBusAppClientInternal = new CredentialStorage(
                    getEnvProperty("AZURE_TENANT_ID"),
                    getEnvProperty("AZURE_SERVICE_BUS_INTERNAL_CLIENT_ID"),
                    getEnvProperty("AZURE_SERVICE_BUS_INTERNAL_CLIENT_SECRET")
            );

            azureCosmosDBAppClient = new CredentialStorage(
                    getEnvProperty("AZURE_TENANT_ID"),
                    getEnvProperty("AZURE_COSMOSDB_CLIENT_ID"),
                    getEnvProperty("AZURE_COSMOSDB_CLIENT_SECRET")
            );

            azureStorageUser = new CredentialStorage(
                    getEnvProperty("QA_AZURE_NAMESPACE"),
                    getEnvProperty("QA_AZURE_ROLE"),
                    getEnvProperty("QA_AZURE_STORAGE_ACCOUNT_KEY")
            );

            azureSubscriptionIds = getJsonMap("AZURE_SUBSCRIPTION");

            azureSystemKeyspaceKey = getJsonMap("AZURE_COSMOSDB_SYSTEM_KEYSPACE_KEY");

            azureCommonKeyspaceKey = getJsonMap("AZURE_COSMOSDB_COMMON_KEYSPACE_KEY");

            s3Region = getEnvProperty("QA_S3_REGION", "us-east-1");

            s3User = new CredentialStorage(
                    null,
                    getEnvProperty("QA_S3_ACCESS_KEY"),
                    getEnvProperty("QA_S3_SECRET_KEY")
            );

            s3CDUser = new CredentialStorage(
                    null,
                    getEnvProperty("AWS_CD_ACCESS_KEY_ID"),
                    getEnvProperty("AWS_CD_SECRET_KEY")
            );

            s3RiqImport = new CredentialStorage(
                    null,
                    getEnvProperty("AWS_RIQ_IMPORT_ACCESS_KEY"),
                    getEnvProperty("AWS_RIQ_IMPORT_SECRET_KEY")
            );

            s3RiqUser = new CredentialStorage(
                    null,
                    getEnvProperty("AWS_RIQ_ACCESS_KEY"),
                    getEnvProperty("AWS_RIQ_SECRET_KEY")
            );

            s3SQSUser = new CredentialStorage(
                    null,
                    getEnvProperty("QA_SQS_ACCESS_KEY"),
                    getEnvProperty("QA_SQS_SECRET_KEY")
            );

            prometheusUser = new CredentialStorage(
                    null,
                    getEnvProperty("QA_AWS_PROMETHEUS_ACCESS_KEY"),
                    getEnvProperty("QA_AWS_PROMETHEUS_SECRET_KEY")
            );

            qaDynamoDBUsers = getCredentialsMap("QA_DYNAMODB_CREDENTIALS");

            qaDynamoDBSystemUser = new CredentialStorage(
                    null,
                    getEnvProperty("QA_DYNAMODB_SYSTEM_ACCESS_KEY"),
                    getEnvProperty("QA_DYNAMODB_SYSTEM_SECRET_KEY")
            );

            awsEMUser = new CredentialStorage(
                    null,
                    getEnvProperty("AWS_EM_ACCESS_KEY"),
                    getEnvProperty("AWS_EM_SECRET_KEY")
            );

            qaDynamoDBCustomerRole = new CredentialStorage(
                    null,
                    getEnvProperty("QA_DYNAMODB_CUSTOMER_ROLE"),
                    getEnvProperty("QA_DYNAMODB_CUSTOMER_EXTERNAL_ID")
            );

            qaDynamoDBCustomerCrossAccountRole = new CredentialStorage(
                    null,
                    getEnvProperty("QA_DYNAMODB_CUSTOMER_CROSS_ACCOUNT_ROLE"),
                    getEnvProperty("QA_DYNAMODB_CUSTOMER_EXTERNAL_ID")
            );

            skipHistoryCleanup = getEnvPropertyAsBoolean(KEY_ENV_VAR_SKIP_HISTORY_CLEANUP, false);

            gcpApplicationCredentials = getEnvProperty("QA_GOOGLE_APPLICATION_CREDENTIALS", getEnvProperty("GOOGLE_APPLICATION_CREDENTIALS"));

            gcpApplicationCredentialsMap = getJsonMap("QA_GOOGLE_APPLICATION_CREDENTIALS_MAP");

            gcpGoogleSheetsInternalCredentials = getEnvProperty("QA_GOOGLE_SHEETS_INTERNAL_CREDENTIALS", getEnvProperty("QA_GOOGLE_SHEETS_INTERNAL_CREDENTIALS"));

            gcpSpannerCredentials = getEnvProperty("QA_SPANNER_CREDENTIALS");

            prometheusWorkspaceId = getEnvProperty("QA_AWS_PROMETHEUS_WORKSPACE_ID");

            bitbucketApiCon = new CredentialStorage(
                    "https://api.bitbucket.org/2.0/repositories/",
                    getEnvProperty("BITBUCKET_USER", "autotests-user"),
                    getEnvProperty("BITBUCKET_PASS", "WgvxdG7Sb9mHPbTHZ9sF")
            );

            deploymentToolCredentials = new CredentialStorage(
                    getEnvProperty("DEPLOYMENT_TOOL_KEY", "F0asqF/QU4SvtpZhn/ZXRfoDf9Vi1Ut8X8HbvbYr8HQPgks+arXnseRC5Hk8K02O6u1Cc3JX86X2kA+wsK2lDSTNzvDvFl7yCvNwWHI9EvZL9zDoq35Dhhxsx/LcPQKAXOAfxPoUS7T5jy5dAamQRHtRQ4oO0ITAIMLL6sTd6H029xQe3x5swR8GqHRRnazAeMoQMG/0kpoOUW1cnPRjtU62I5YRKJnNJ9vUEDDRDApnTG1x8URaDtpUbR5qKp6bT6lBu7wDX0oE2KKB6AIX5Q=="));

            ftpDnBApiCon = new CredentialStorage(
                    "https://ftp.dnb.com",
                    getEnvProperty("DNB_FTP_USER", "reltio"),
                    getEnvProperty("DNB_FTP_PASSWORD", "KrWzU76@uSr$a57reQw")
            );

            dnbAccount = new CredentialStorage(
                    "",
                    getEnvProperty("DNB_USER", ftpDnBApiCon.getUser()),
                    getEnvProperty("DNB_PASSWORD", ftpDnBApiCon.getPassword())
            );

            mainUser = new CredentialStorage(
                    null,
                    getEnvProperty("OAUTH_USER", "autotestadmin"),
                    getEnvProperty("OAUTH_PASSWORD", "!ReltioAdmin1")
            );

            rdmUser = new CredentialStorage(
                    "https://auth-stg.reltio.com/oauth/token",
                    getEnvProperty("OAUTH_RDM_USER", "rdm.admin"),
                    getEnvProperty("OAUTH_RDM_PASSWORD", "beRightFaster!23")
            );

            securityUser = new CredentialStorage(
                    null,
                    getEnvProperty("OAUTH_SECURITY_USER", "testsecurityadmin"),
                    getEnvProperty("OAUTH_SECURITY_PASSWORD", "Reltio@1234")
            );

            quboleConnection = new CredentialStorage(
                    "https://api.qubole.com",
                    GlobalConfig.getEnvProperty("QUBOLE_TOKEN")
            );

            databricksConnection = new CredentialStorage(
                    "https://dbc-5aef38be-4be0.cloud.databricks.com",
                    GlobalConfig.getEnvProperty("DBC_TOKEN")
            );

            apiSecretKey = new CredentialStorage(
                    null,
                    "cmVsdGlvX3VpOm1ha2l0YQ=="
            );

            mfaUser = new CredentialStorage(
                    getEnvProperty("MFA_USER_EMAIL", "<EMAIL>"),
                    getEnvProperty("MFA_USER_SECRET", "wibjeqcarvxcxjdz")
            );

            setUseStaticTenants(getEnvPropertyAsBoolean("USE_STATIC_TENANTS", false));
            staticTenantsAmount = getEnvPropertyAsLong("STATIC_TENANTS_AMOUNT", -1);

            useDevOpsEtalonUrl = getEnvPropertyAsBoolean(KEY_ENV_VAR_USE_DEVOPS_ETALON_URL, false);

            waitByAllPods = getEnvPropertyAsBoolean("WAIT_BY_ALL_PODS", true);

            initialize = true;
        } else {
            //Load additional vault path
            vault.loadVaultPath(additionalVaultPath);
        }

        // Post config
        if (s3User.getUser() != null && s3User.getPassword() != null) {
            basicAWSCredentials = new BasicAWSCredentials(s3User.getUser(), s3User.getPassword());
        }

        // Waiters
        waiterUpdateDelay = getEnvPropertyAsLong("WAITER_UPDATE_DELAY", 60_000);
        waiterInitDelay = getEnvPropertyAsLong("WAITER_INIT_DELAY", 120_000);
    }

    private static CloudType getCloudType() {
        if (cloudType == null) {
            cloudType = CloudProviderService.CLOUD_TYPE;
        }
        return cloudType;
    }

    public static String getVaultKey(String key, String... defaultValue) {
        if (vault != null && vault.isInitialized() && vault.hasValue(key)) {
            log.info("Key with name {} has been invoked from Vault Service", key);
            return vault.extractSecret(key);
        } else if (defaultValue.length != 0) {
            return defaultValue[0];
        }
        return null;
    }

    public static Map<String, String> getVaultMap(String key, Map<String, String>... defaultValue) {
        if (vault != null && vault.isInitialized()) {
            log.info("Key with name {} has been invoked from Vault Service", key);
            return vault.extractSecretData().getDataObjectMap(key);
        } else if (defaultValue.length != 0) {
            return defaultValue[0];
        }
        return null;
    }

    public static List<String> getVaultList(String key, List<String>... defaultValue) {
        if (vault != null && vault.isInitialized()) {
            log.info("Key with name {} has been invoked from Vault Service", key);
            return vault.extractSecretData().getDataObjectList(key);
        } else if (defaultValue.length != 0) {
            return defaultValue[0];
        }
        return null;
    }

    public static String getEnvProperty(String key, String... defaultValue) { //NOSONAR: too many returns
        //Check System env first
        if (System.getenv(key) != null) {
            log.info("{} is loaded from Environment variable", key);
            return System.getenv(key);
        }
        //Try get value from Vault
        if (vault != null && vault.isInitialized() && vault.hasValue(key)) {
            log.info("{} is loaded from Vault service", key);
            return vault.extractSecretData().getData().get(key);
        }
        //Check default value
        if (defaultValue != null && defaultValue.length != 0) {
            log.info("Default value for {} is used", key);
            return defaultValue[0];
        }
        //Return null if no values found
        return null;
    }

    public static long getEnvPropertyAsLong(String key, long defaultValue) {
        String value = getEnvProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    public static boolean getEnvPropertyAsBoolean(String key, boolean defaultValue) { //NOSONAR: not an instance method
        String value = getEnvProperty(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }

    private static Map<String, CredentialStorage> getCredentialsMap(String key) {
        String json = GlobalConfig.getEnvProperty(key);
        return GsonUtils.getGson().fromJson(json, new TypeToken<Map<String, CredentialStorage>>() {
        }.getType());
    }

    private static Map<String, String> getJsonMap(String key) {
        String json = GlobalConfig.getEnvProperty(key);
        return GsonUtils.getGson().fromJson(json, new TypeToken<Map<String, String>>() {
        }.getType());
    }

    public static boolean isInitialized() {
        return initialize;
    }

    public static long getWaiterUpdateDelay() {
        return waiterUpdateDelay;
    }

    public static long getWaiterUpdateDelaySeconds() {
        return waiterUpdateDelay / 1000;
    }

    public static long getWaiterInitDelay() {
        return waiterInitDelay;
    }

    public static void setWaiterUpdateDelay(long waiterUpdateDelay) {
        GlobalConfig.waiterUpdateDelay = waiterUpdateDelay;
    }

    public static boolean isWaitByAllPods() {
        return waitByAllPods;
    }

    public static boolean isInternalSnowflakeViews() {
        return isInternalSnowflakeViews;
    }
}
