<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Interactions, AnalyticsAttributes tests" parallel="tests" thread-count="4" allow-return-values="true"
       configfailurepolicy="continue">
    <parameter name="required_services" value="AUTH,API"/>
    <parameter name="execution_team" value="Data Nile"/>
    <parameter name="suite_id" value="DN"/>

    <listeners>
        <listener class-name="com.epam.reportportal.testng.ReportPortalTestNGListener"/>
        <listener class-name="com.reltio.qa.listeners.RetryAnnotationListener"/>
        <listener class-name="com.reltio.qa.listeners.TestListener"/>
        <listener class-name="com.reltio.qa.listeners.XMLReporter"/>
        <listener class-name="com.reltio.qa.listeners.EmailReport"/>
        <listener class-name="com.reltio.qa.listeners.FailedReporter"/>
    </listeners>

    <test name="Interactions">
        <classes>
            <class name="com.reltio.qa.api.entity.Interactions"/>
        </classes>
    </test>
    <test name="AnalyticsAttributes">
        <classes>
            <class name="com.reltio.qa.api.attributes.AnalyticsAttributes"/>
        </classes>
    </test>
    <test name="SegmentationSearch">
        <classes>
            <class name="com.reltio.qa.api.search.SegmentationSearch"/>
        </classes>
    </test>
    <test name="SegmentsManagement">
        <classes>
            <class name="com.reltio.qa.api.segments.SegmentsManagementTest"/>
        </classes>
    </test>
</suite>