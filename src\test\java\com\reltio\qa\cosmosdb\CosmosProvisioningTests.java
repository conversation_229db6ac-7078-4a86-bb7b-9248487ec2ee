package com.reltio.qa.cosmosdb;

import com.azure.core.util.Context;
import com.azure.resourcemanager.AzureResourceManager;
import com.reltio.qa.Config;
import com.reltio.qa.TestNGBaseTest;
import com.reltio.qa.enums.common.CloudType;
import com.reltio.qa.enums.common.Service;
import com.reltio.qa.enums.devOps.TenantSize;
import com.reltio.qa.enums.match.MatchingStrategy;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.model.Account;
import com.reltio.qa.model.BusinessModel.BusinessConfigModel;
import com.reltio.qa.model.EntityModel;
import com.reltio.qa.model.TenantModel;
import com.reltio.qa.model.devOps.DevOpsTenantModel;
import com.reltio.qa.services.*;
import com.reltio.qa.services.cloud.azure.AzureResourceManagerService;
import com.reltio.qa.services.devOps.DevOpsService;
import com.reltio.qa.services.devOps.DevOpsTenantService;
import com.reltio.qa.services.pms.PlatformManagementTenantService;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.utils.ProvisioningUtils;
import com.reltio.qa.utils.ThreadUtils;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.testng.Assert;
import org.testng.SkipException;
import org.testng.annotations.*;
import org.testng.annotations.Optional;

import java.util.*;
import java.util.concurrent.ForkJoinPool;

import static com.reltio.qa.Config.getEnvName;
import static com.reltio.qa.Config.getTenantUrl;
import static com.reltio.qa.GlobalConfig.*;
import static com.reltio.qa.enums.common.Service.DEVOPS_API;
import static com.reltio.qa.enums.common.Service.PMS;
import static com.reltio.qa.helpers.CommonStrings.ENTITY_TYPE_HCP;
import static com.reltio.qa.services.TenantManagementService.generateInfoForTenantName;
import static com.reltio.qa.services.TenantManagementService.getTenant;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForValue;
import static com.reltio.qa.utils.tenant.TenantIdUtils.generateTenantId;

@Log4j2
public class CosmosProvisioningTests extends TestNGBaseTest {
    //real tenant that always exists on na03-tst-01 and -hf environment. Needed here to avoid NPE on admin account initialization
    private static final String STATIC_TENANT_ID = "dontdelete";
    protected ForkJoinPool executor = ThreadUtils.threadPool(4);
    private static final String RESOURCE_GROUP_SUFFIX = "reltio-platform-sb-internal-queues-rg";

    @BeforeClass(alwaysRun = true)
    public void beforeClass() {
        if (!useCosmosDB()) {
            throw new SkipException("The Test is only supported on CosmosDB");
        }
    }

    @BeforeTest(alwaysRun = true, dependsOnMethods = {"btGroupTestMethod"})
    @Override
    public void btInitEnvironment() {
        log.info("Initialize Environment");
        if (Config.isIsolatedEnvironment() || !Config.isDevOpsApiEnabled()) {
            setHosts();
        }
    }

    @Parameters({"allowedEnvironments"})
    @BeforeClass(alwaysRun = true)
    @Override
    protected void bcDefineParameters(@Optional("all") String allowedEnvironments) throws CommandException {
        log.info("Define parameters");
        setVars(new HashMap<>(Config.getAsMap("")));
        setDataFolder("resources:" + this.getClass().getName().replace("com.reltio.qa.", "").replace(".", "/"));
        //Strict environments where tests should be executed
        if (!allowedEnvironments.equals("all")) {
            if (Arrays.stream(allowedEnvironments.split(",")).noneMatch(Config.getCurrentConfigName()::equals)) {
                log.info("Skip the test as it shouldn't run on this environment");
                throw new SkipException("The test shouldn't run on this environment");
            }
        }
        //Check main user roles
        Account admin = AccountService.getAdminAccount();
        List<String> missingRoles = new ArrayList<>(AccountService.ADMIN_ROLES);
        missingRoles.removeAll(admin.getRoles());
        if (!missingRoles.isEmpty()) {
            log.warn("List of roles for admin user " + admin.getUsername() + " doesn't have some roles: " + String.join(",", missingRoles));
            admin.addRoles(missingRoles);
        }
        if (!admin.getTenants().contains(Config.getTenantId())) {
            log.info("Set tenant for " + admin.getUsername() + " user");
            admin.addTenant(STATIC_TENANT_ID);
        }
        AccountService.updateAccount(admin);

        log.info("Class started " + this.getClass().getName() + " at " + Thread.currentThread().getName());
    }

    @AfterClass
    public void acCommonMessaging() throws InterruptedException {
        executor.shutdown();
        boolean terminated = executor.awaitTermination(600, java.util.concurrent.TimeUnit.SECONDS);
        if (!terminated) {
            log.warn("Executor did not terminate within the specified time");
        }
    }

    @TmsLink("RP-TC-12489")
    @Test(groups = {"regression"}, description = "Check tenant creation via devOps-api. useSpannerCloudFunction = true, useServiceBusNamespace = true")
    public void runRpTc12489() throws Exception {
        String tenantId = generateTenantId();
        checkTenantResourceCreation(
                DEVOPS_API,
                true,
                true,
                tenantId,
                String.format("%s-%s", getEnvName(), tenantId)
        );
    }

    @TmsLink("RP-TC-13073")
    @Test(groups = {"regression"}, description = "Check tenant creation via devOps-api. useSpannerCloudFunction = false, useServiceBusNamespace = true")
    public void runRpTc13073() throws Exception {
        String tenantId = generateTenantId();
        checkTenantResourceCreation(
                DEVOPS_API,
                false,
                true,
                tenantId,
                String.format("%s-%s", getEnvName(), tenantId)
        );
    }

    @TmsLink("RP-TC-13074")
    @Test(groups = {"regression"}, description = "Check tenant creation via devOps-api. useSpannerCloudFunction = true, useServiceBusNamespace = false")
    public void runRpTc13074() throws Exception {
        checkTenantResourceCreation(
                DEVOPS_API,
                true,
                false,
                generateTenantId(),
                null // no need to specify namespace in case if useServiceBusNamespace = false
        );
    }

    @TmsLink("RP-TC-13075")
    @Test(groups = {"regression"}, description = "Check tenant creation via devOps-api. useSpannerCloudFunction = false, useServiceBusNamespace = false")
    public void runRpTc13075() throws Exception {
        checkTenantResourceCreation(
                DEVOPS_API,
                false,
                false,
                generateTenantId(),
                null // no need to specify namespace in case if useServiceBusNamespace = false
        );
    }

    @TmsLink("RP-TC-12490")
    @Test(groups = {"regression"}, description = "Check tenant creation via PMS. useSpannerCloudFunction = true, useServiceBusNamespace = true", enabled = false)
    public void runRpTc12490() throws Exception {
        String tenantId = generateTenantId();
        checkTenantResourceCreation(
                PMS,
                true,
                true,
                tenantId,
                String.format("%s-%s", getEnvName(), tenantId)
        );
    }

    @TmsLink("RP-TC-13076")
    @Test(groups = {"regression"}, description = "Check tenant creation via PMS. useSpannerCloudFunction = false, useServiceBusNamespace = true", enabled = false)
    public void runRpTc13076() throws Exception {
        String tenantId = generateTenantId();
        checkTenantResourceCreation(
                PMS,
                true,
                true,
                tenantId,
                String.format("%s-%s", getEnvName(), tenantId)
        );
    }

    @TmsLink("RP-TC-13077")
    @Test(groups = {"regression"}, description = "Check tenant creation via PMS. useSpannerCloudFunction = true, useServiceBusNamespace = false", enabled = false)
    public void runRpTc13077() throws Exception {
        checkTenantResourceCreation(
                PMS,
                true,
                false,
                generateTenantId(),
                null // no need to specify namespace in case if useServiceBusNamespace = false
        );
    }

    @TmsLink("RP-TC-13078")
    @Test(groups = {"regression"}, description = "Check tenant creation via PMS. useSpannerCloudFunction = false, useServiceBusNamespace = false", enabled = false)
    public void runRpTc13078() throws Exception {
        checkTenantResourceCreation(
                PMS,
                false,
                false,
                generateTenantId(),
                null // no need to specify namespace in case if useServiceBusNamespace = false
        );
    }

    private void checkTenantResourceCreation(Service service, boolean useSpannerCloudFunction, boolean useServiceBusNamespace, String tenantId, String serviceBusNamespace) throws Exception {
        TenantModel tm = null;
        try {
            switch (service) {
                case DEVOPS_API ->
                        createTenantViaDevOpsApi(tenantId, useSpannerCloudFunction, useServiceBusNamespace, serviceBusNamespace);
                case PMS ->
                        createTenantViaPMS(tenantId, useSpannerCloudFunction, useServiceBusNamespace, serviceBusNamespace);
            }
            tm = getTenant(tenantId);
            TestNGBaseTest.tenants.add(tenantId);

            log.info("Validating Cosmos Tenant Resource");
            validateCosmosResource(tenantId);

            log.info("Validate ServiceBus");
            validateServiceBusNamespace(serviceBusNamespace);

            log.info("Check entity processing");
            waitForValue(() -> BusinessConfigurationService.putBusinessConfig(tenantId, BusinessConfigModel.fromJson(loadFile("resources:config.json"))).getUri(),
                    "configuration", "Wait for business config is successfully loaded");
            EntityService.postEntities(Collections.singleton(new EntityModel(ENTITY_TYPE_HCP)).toString(), tenantId);
            SmartWaiting.waitForValue(() -> SearchService.searchTotal(getTenantUrl(tenantId)), 1);
        } finally {
            if (serviceBusNamespace != null && tm != null && useServiceBusNamespace) {
                DevOpsService.deleteServiceBusResources(tenantId, serviceBusNamespace);
            }
            executor.execute(() -> TenantManagementService.deleteTenantUnchecked(tenantId));
        }
    }

    private void createTenantViaDevOpsApi(String tenantId, boolean useSpannerCloudFunction, boolean useServiceBusNamespace, String serviceBusNamespace) throws CommandException {
        log.info("Generate tenantId config via devOps-api");
        String tenantConfig = DevOpsTenantService
                .generateTenantConfig(generateInfoForTenantName(), tenantId, TenantSize.XX_SMALL, MatchingStrategy.INCREMENTAL,
                        ProvisioningUtils.getStoragePriorityList(CloudType.AZURE), useSpannerCloudFunction, false, useServiceBusNamespace, serviceBusNamespace, DevOpsTenantModel.Tier.PREMIUM);

        log.info("Create tenant directly on Platform");
        TenantManagementService.createNewTenantWithWait(tenantId, tenantConfig);
    }

    private void createTenantViaPMS(String tenantId, boolean useSpannerCloudFunction, boolean useServiceBusNamespace, String serviceBusNamespace) throws Exception {
        log.info("Create tenant via PMS");
        PlatformManagementTenantService.createTenant(tenantId, TenantSize.XX_SMALL, MatchingStrategy.INCREMENTAL,
                ProvisioningUtils.getStoragePriorityList(CloudType.AZURE), useSpannerCloudFunction, useServiceBusNamespace, serviceBusNamespace, null, true);
    }

    private static void validateCosmosResource(String tenantId) {
        try (CosmosDBService cosmosDBService = new CosmosDBService(getTenant(tenantId))) {
            Assert.assertEquals(cosmosDBService.getListOfTables(tenantId).size(), 19, "The number of tables does not match the expected value");
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage());
        }
    }

    private static void validateServiceBusNamespace(String serviceBusNamespace) {
        if (serviceBusNamespace == null) {
            return; //no need to validate namespace if it hasn't been created
        }
        AzureResourceManager azureResourceManager = AzureResourceManagerService.getAzureResourceManager(getAzureServiceBusAppClientInternalClientId(),
                getAzureServiceBusAppClientInternalClientSecret(),
                getAzureServiceBusAppClientInternalTenantId());
        String provisioningState;
        try {
            provisioningState = azureResourceManager.serviceBusNamespaces().
                    manager().serviceClient().getNamespaces()
                    .getByResourceGroupWithResponse(getResourceGroupName(),
                            serviceBusNamespace, Context.NONE).getValue().provisioningState();
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage());
        }
        Assert.assertEquals(provisioningState, "Succeeded", "Service Bus is Provisioning State is not succeeded");
    }

    @NotNull
    private static String getResourceGroupName() {
        return String.format("%s-%s", getEnvName(), RESOURCE_GROUP_SUFFIX);
    }
}
