{"uri": "configuration", "schemaVersion": "1", "referenceConfigurationURI": "configuration/_vertical/life-sciences-autotest", "entityTypes": [{"uri": "configuration/entityTypes/HCP", "label": "HCP", "description": "HCP", "attributes": [{"uri": "configuration/entityTypes/HCP/attributes/AutoGeneratedSimple", "name": "AutoGeneratedSimple", "label": "AutoGeneratedSimple", "type": "String", "autoGenerated": true, "generateIfEmpty": true, "generatedValueUniqueForCrosswalk": true, "generator": "UUID4_generator_simple", "autoGenerationPattern": "{value}"}, {"uri": "configuration/entityTypes/HCP/attributes/AutoGeneratedNested", "name": "AutoGeneratedNested", "label": "Auto-generated Nested", "type": "Nested", "attributes": [{"uri": "configuration/entityTypes/HCP/attributes/AutoGeneratedNested/attributes/Type", "name": "Type", "label": "Type", "type": "String"}, {"uri": "configuration/entityTypes/HCP/attributes/AutoGeneratedNested/attributes/ID", "name": "ID", "label": "ID", "type": "String", "autoGenerated": true, "generateIfEmpty": true, "generatedValueUniqueForCrosswalk": true, "generator": "UUID4_generator_nested", "autoGenerationPattern": "{value}"}]}]}], "sources": [{"uri": "configuration/sources/Auto", "abbreviation": "Auto", "label": "Auto", "autoGenerated": true, "generator": "UUID4_generator_crosswalk", "autoGenerationPattern": "Source_{value}"}]}