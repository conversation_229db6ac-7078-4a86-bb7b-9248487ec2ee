package com.reltio.qa.datapipeline;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.reltio.qa.Config;
import com.reltio.qa.enums.common.Service;
import com.reltio.qa.enums.match.MatchingStrategy;
import com.reltio.qa.exceptions.ApplicationGlobalException;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.exceptions.ReltioObjectException;
import com.reltio.qa.helpers.CleanseHelper;
import com.reltio.qa.helpers.datapipeline.EntitiesHelper;
import com.reltio.qa.model.*;
import com.reltio.qa.model.BusinessModel.BusinessConfigModel;
import com.reltio.qa.model.Interactions.InteractionMemberModel;
import com.reltio.qa.model.Interactions.InteractionModel;
import com.reltio.qa.model.Interactions.InteractionSubMember;
import com.reltio.qa.model.version.ComponentVersion;
import com.reltio.qa.services.*;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import com.reltio.qa.utils.RandomGenerator;
import com.reltio.qa.utils.TimeUtils;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.*;

import static com.reltio.qa.helpers.CommonStrings.*;
import static com.reltio.qa.helpers.LocationStrings.CITY;
import static com.reltio.qa.helpers.LocationStrings.COUNTRY;
import static com.reltio.qa.model.SearchFilterExpression.equalsFilter;
import static com.reltio.qa.model.TaskModel.Status.COMPLETED;
import static com.reltio.qa.services.ActivityLogService.getActivities;
import static com.reltio.qa.services.SearchService.searchTotal;
import static com.reltio.qa.services.TaskService.syncToDataPipeline;
import static com.reltio.qa.services.TenantManagementService.createTenant;
import static com.reltio.qa.services.TenantManagementService.getTenant;
import static com.reltio.qa.services.VersionService.getServiceVersion;
import static com.reltio.qa.services.datapipeline.DataPipelineGbqProvider.IS_LEGACY;
import static com.reltio.qa.services.datapipeline.DataPipelineGbqProvider.IS_SPLIT_TABLE;
import static com.reltio.qa.services.datapipeline.MonitoringService.*;
import static com.reltio.qa.services.gcp.GBQConstants.GBQ_TABLE_ENTITY_MYCOMPANY_ID;
import static com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformPeriodicTasks.waitForTaskStatus;
import static com.reltio.qa.utils.TimeUtils.sleep;
import static org.testng.Assert.*;

@Log4j2
public class DataPipelineTests extends DataPipelineBase {

    private static final String ENTITY_MONITORING_PARAM_ID = "entityId=";
    private static final int MONITORING_TABLES_CREATION_SLEEP = 180_000;

    @BeforeClass(alwaysRun = true)
    @Parameters({"data-pipeline-adapter"})
    protected void beforeDataPipelineTests(@Optional(UNDEFINED_ADAPTER_TYPE) String dataPipelineAdapterFromSuite) throws Exception {
        AccountService.setDefaultAccountName(AccountService.ADMIN_NAME);  //TODO remove this line and set explicitly account name in all tests. NB! Admin account will not be allowed as a default account in the future. RP-151889

        if (dps == null) {
            beforeDataPipelineBaseTest(dataPipelineAdapterFromSuite);
        }
        log.info("beforeDataPipelineTests: Adapter = " + dataPipelineAdapter);
        try {
            log.info("Precondition: 1.Enable Streaming and Data Pipeline");
            updateBusinessConfig("config/Config/L3-config-wscore.json");
            preconditions();
        } catch (Exception e) {
            throw new ApplicationGlobalException("beforeDataPipelineTests() failed!", e);
        }
    }

    protected void preconditions() throws Exception {
        log.info("Precondition 1. Enable Streaming and Data Pipeline");
        bcEnableStreamingAndDataPipeline();
        // Stability
        sleep(10_000);

        log.info("Precondition 2. Create views");
        assertTrue(dps.createDataPipelineTablesViews(true), "Data pipeline tables/views creation failed.");
        dpCheck.checkGbqExpectedTablesOrViews(GBQ_VIEW_LIST);
        // Small delay for table creation
        sleep(10_000);
        dpCheck.checkGbqExpectedTablesOrViews(GBQ_TABLE_LIST);
    }

    @TmsLink("RP-TC-11387")
    @Test(groups = {"smoke", "regression", "gbq2", "snowflake2", "deltalake1", "pipeline"}, description = "Data pipeline: create and delete entity")
    public void runRpTc11387() throws ReltioObjectException {
        log.info("Step 1.1. Create entity");
        EntityModel entity = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.2. Validate entity exists in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity.getUri(), 1);
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity.getUri(), entity.getAttribute(FIRST_NAME).getValue().toString());

        log.info("Step 1.3. Validate entity attributes for RP-99059");
        dpCheck.checkTableInSyncByCrosswalksUriFieldPartialMatch(splittedOrCommon_entitiesView_HCP, entity.getUri(), entity.getUri() + "/crosswalks/");
        dpCheck.checkTableInSyncByCrosswalksSourceFieldPartialMatch(splittedOrCommon_entitiesView_HCP, entity.getUri(), "configuration/sources/");
        dpCheck.checkTableInSyncByCrosswalksRefEntityField(splittedOrCommon_entitiesView_HCP, entity.getUri());

        log.info("Step 1.4. Validate entity attributes for RP-102919");
        Long now = System.currentTimeMillis();
        entity.getCrosswalks().get(0).updateAttribute(DELETE_DATE_FIELD, now.toString());
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        dpCheck.checkTableInSyncByFieldByEndDate(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity.getUri(), now.toString());

        log.info("Step 2.1. Delete entity");
        entity.delete(true);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate deleted entity exists in external table");
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? entitiesTable_HCP : entitiesTable, URI_FIELD, entity.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity.getUri(), 0);
    }

    @TmsLink("RP-TC-11388")
    @Test(groups = {"smoke", "regression", "gbq2", "snowflake2", "deltalake1","pipeline"}, description = "Data pipeline: update simple attribute")
    public void runRpTc11388() throws ReltioObjectException {
        String updatedFirstName = "RP-TC-11388-updated";
        String nonOvFirstName = "RP-TC-11388-nonOV";
        log.info("Step 1.1. Create entity");
        EntityModel entity = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Update attribute");
        entity.updateOrCreateSimpleAttributeInModel(FIRST_NAME, updatedFirstName);
        entity.addSimpleAttributeToModel(FIRST_NAME, nonOvFirstName);
        entity.post();

        log.info("Step 2.2. Set OV=false for one of the values");
        entity.getAttribute(FIRST_NAME, nonOvFirstName).ignoreSet();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.3. Validate updated attribute with OV=true in external view");
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity.getUri(), updatedFirstName);
        dpCheck.checkTableInSyncByHcpFirstNameFieldOvCondition(splittedOrCommon_entitiesView_HCP, entity.getUri(), updatedFirstName, "true");

        log.info("Step 2.4. Validate updated attribute with OV=false in external view");
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity.getUri(), nonOvFirstName);
        dpCheck.checkTableInSyncByHcpFirstNameFieldOvCondition(splittedOrCommon_entitiesView_HCP, entity.getUri(), nonOvFirstName, "false");
    }

    @TmsLink("RP-TC-11389")
    @Test(groups = {"smoke", "regression", "gbq2", "snowflake2", "deltalake1", "pipeline"}, description = "Data pipeline: add and remove relation")
    public void runRpTc11389() throws ReltioObjectException {
        log.info("Step 1. Create entity");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Add relation");
        EntityModel relation = EntitiesHelper.createEntity(ENTITY_TYPE_LOCATION, List.of(ADDRESS_LINE_ONE), null);
        RelationModel relationModel = new RelationModel(RELATION_TYPE_HASADDRESS);
        relationModel.setStartObjectUri(entity1.getUri());
        relationModel.setEndObjectUri(relation.getUri());
        relationModel.addSimpleAttributeToModel(ADDRESS_RANK, "1").post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate relation exists in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_relationsView_HasAddress, URI_FIELD, relationModel.getUri(), 1);
        dpCheck.checkTableInSyncByLocationAddressLine1Field(splittedOrCommon_entitiesView_Location, relation.getUri(),
                relation.getAttribute(ADDRESS_LINE_ONE).getAsSimple().getValue());

        log.info("Step 3.1. Delete relation");
        relationModel.delete();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate deleted relation does not exist in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_relationsView_HasAddress, URI_FIELD, relationModel.getUri(), 0);
    }

    @TmsLink("RP-TC-11395")
    @Test(groups = {"smoke", "regression", "gbq2", "snowflake2", "pipeline"}, description = "Data pipeline: merge and unmerge entities - manual")
    public void runRpTc11395() throws Exception {
        log.info("Step 1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        String splittedOrCommon_entitiesTable_HCO = IS_LEGACY_OR_SPLITTED ? entitiesTable_HCO : entitiesTable;

        dpCheck.checkTableInSyncByField(splittedOrCommon_entitiesView_HCO, URI_FIELD, entity1.getUri());
        dpCheck.checkTableInSyncByField(splittedOrCommon_entitiesView_HCO, URI_FIELD, entity2.getUri());

        log.info("Step 2.1. Merge entities");
        EntityModel merge = EntityService.mergeEntitiesWithWaiter(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate merge record exists in external view");
        dpCheck.checkTableInSyncByField(mergesView, WINNER_ID_FIELD, merge.getId());
        dpCheck.checkTableInSyncByField(mergesView, LOSER_ID_FIELD,
                merge.getId().equals(entity1.getId()) ? entity2.getId() : entity1.getId());
        log.info("Step 2.3. Validate entities records count in external table");
        dpCheck.checkTableInSyncByCountDistinctWithDeletedFalse(splittedOrCommon_entitiesTable_HCO, ID_FIELD, merge.getId(), ID_FIELD, 1);
        dpCheck.checkTableInSyncByCountDistinctWithDeletedTrue(splittedOrCommon_entitiesTable_HCO, ID_FIELD,
                merge.getId().equals(entity1.getInitialId()) ? entity2.getInitialId() : entity1.getInitialId(), ID_FIELD, 1);

        log.info("Step 2.4. Validate winnerId exists and loserId was deleted in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, ID_FIELD, merge.getInitialId(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, ID_FIELD, merge.getId().equals(entity1.getInitialId()) ? entity2.getInitialId() : entity1.getInitialId(), 0);

        log.info("Step 3.1. Unmerge entities");
        Map<String, EntityModel> unmergedEntities = EntityService.unmergeEntitiesWithWaiter(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate winnerId record was deleted in external view");
        dpCheck.checkTableInSyncByCount(mergesView, WINNER_ID_FIELD, merge.getId(), 0);

        log.info("Step 3.3. Validate entities 'not_matches' each other in external view");
        dpCheck.checkEntitiesNotMatch(matchesView, entity1.getInitialId(), entity2.getInitialId());

        log.info("Step 3.4. Validate original entities exist in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, entity1.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, entity2.getUri(), 1);
    }

    @TmsLink("RP-TC-11396")
    @Test(groups = {"smoke", "regression", "gbq2", "snowflake3", "deltalake2", "pipeline"}, description = "Data pipeline: match and unmatch entities")
    public void runRpTc11396() {
        log.info("Step 1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Match entities");
        EntitiesHelper.setAsMatches(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate entities 'manual_matches' each other in external view");
        dpCheck.checkEntitiesManualMatch(matchesView, entity1.getId(), entity2.getId());

        log.info("Step 3.1. Unmatch entities");
        EntitiesHelper.setAsNotMatches(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate entities 'not_matches' each other in external view");
        dpCheck.checkTableInSyncByField(matchesView, IS_LEGACY_OR_SPLITTED ? MATCHES_SOURCE_ID : ENTITY_ID, entity1.getId());
        dpCheck.checkTableInSyncByField(matchesView, IS_LEGACY_OR_SPLITTED ? MATCHES_SOURCE_ID : ENTITY_ID, entity2.getId());
        dpCheck.checkEntitiesNotMatch(matchesView, entity1.getId(), entity2.getId());
    }

    @TmsLink("RP-TC-12073")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake5", "pipeline"}, description = "Data pipeline: match, delete entity and data sync")
    public void runRpTc12073() throws ReltioObjectException {
        log.info("Step 1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Match entities");
        EntitiesHelper.setAsMatches(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Delete entity1");
        entity1.delete();
        dps.waitForEmptyQueueAndStagingBucketUpdated();


        log.info("Step 3.1. Create Views");
        dps.createDataPipelineTablesViews(false, false);

        String matchesViewField_targetId = (IS_LEGACY && "GBQ".equalsIgnoreCase(dataPipelineAdapter)) ? MATCHES_TARGET_ID : ENTITY_ID;
        String matchesViewField_sourceId = (IS_LEGACY && "GBQ".equalsIgnoreCase(dataPipelineAdapter)) ? MATCHES_SOURCE_ID : ENTITY_ID;

        log.info("Step 3.2. Validate Manual Match records for all entities");
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity2.getId(), 1);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity1.getId(), 1);
        dpCheck.checkEntitiesManualMatch(matchesView, entity1.getId(), entity2.getId());

        log.info("Step 3.3. Recreate views after deleting entities");
        assertTrue(dps.createDataPipelineTablesViews(true), "Data pipeline tables/views creation failed.");


        log.info("Step 4.1. Run Sync Jobs");
        PostTaskRequestParameters params = new PostTaskRequestParameters()
                .tenantId(Config.getTenantId());
        syncToDataPipeline(params).getListOfTasks().parallelStream().forEach(t -> waitForTaskStatus(t, TaskModel.Status.COMPLETED));

        log.info("Step 3.2. Validate Manual Match records for all entities after sync");
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity2.getId(), 1);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity1.getId(), 1);
        dpCheck.checkEntitiesManualMatch(matchesView, entity1.getId(), entity2.getId());

    }

    @TmsLink("RP-TC-12074")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake5", "pipeline"}, description = "Data pipeline: match, merge and data sync")
    public void runRpTc12074() throws ReltioObjectException {
        log.info("Step 1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity3 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity4 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity5 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Match entities");
        EntitiesHelper.setAsMatches(entity1, entity2);
        EntitiesHelper.setAsMatches(entity3, entity4);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Merge entities");
        EntityService.mergeEntitiesWithWaiter(entity1, entity2);
        EntityService.mergeEntities(entity4, entity5, entity5);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.1. Create Views");
        dps.createDataPipelineTablesViews(false, false);

        String matchesViewField_targetId = (IS_LEGACY && "GBQ".equalsIgnoreCase(dataPipelineAdapter)) ? MATCHES_TARGET_ID : ENTITY_ID;
        String matchesViewField_sourceId = (IS_LEGACY && "GBQ".equalsIgnoreCase(dataPipelineAdapter)) ? MATCHES_SOURCE_ID : ENTITY_ID;

        log.info("Step 3.2. Validate Manual Match records for all entities");
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity1.getId(), 0);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity2.getId(), 0);

        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity3.getId(), 1);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity4.getId(), 0);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity5.getId(), 1);
        dpCheck.checkEntitiesManualMatch(matchesView, entity3.getId(), entity5.getId());

        log.info("Step 3.3. Recreate views after deleting entities");
        assertTrue(dps.createDataPipelineTablesViews(true), "Data pipeline tables/views creation failed.");


        log.info("Step 4.1. Run Sync Jobs");
        PostTaskRequestParameters params = new PostTaskRequestParameters().tenantId(Config.getTenantId());
        syncToDataPipeline(params).getListOfTasks().parallelStream().forEach(t -> waitForTaskStatus(t, TaskModel.Status.COMPLETED));

        log.info("Step 3.2. Validate Manual Match records for all entities after sync");
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity2.getId(), 0);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity1.getId(), 0);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_targetId, entity3.getId(), 1);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity4.getId(), 0);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField_sourceId, entity5.getId(), 1);
        dpCheck.checkEntitiesManualMatch(matchesView, entity3.getId(), entity5.getId());

    }

    @TmsLink("RP-TC-11397")
    @Test(groups = {"smoke", "regression", "gbq2", "snowflake3", "deltalake2", "pipeline"}, description = "Data pipeline: create interaction")
    public void runRpTc11397() throws Exception {
        log.info("Step 1. Create entities");
        EntityModel hcp = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel hco = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Create interactions");
        // Create EMAIL interaction
        InteractionModel interaction = new InteractionModel(INTERACTION_TYPE_EMAIL)
                .addMember("From", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/From")
                        .addSubMember(new InteractionSubMember(hcp.getUri())));
        interaction.addMember("To", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/To")
                .addSubMember(new InteractionSubMember(hco.getUri())));
        interaction.post();

        // Create EXHIBITION interaction
        InteractionModel interaction2 = new InteractionModel(INTERACTION_TYPE_EXHIBITION_EVENT);
        interaction2.addAttributeToModel("EXHBT_NAME", new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        interaction2.addAttributeToModel("EXHBT_GLOBAL", new SimpleAttributeModel("false"));
        interaction2.addMember("HCP", new InteractionMemberModel(ENTITY_TYPE_HCP)
                .addSubMember(new InteractionSubMember(hcp.getUri())));
        interaction2.addMember("HCO", new InteractionMemberModel(ENTITY_TYPE_HCO)
                .addSubMember(new InteractionSubMember(hco.getUri())));
        interaction2.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate EMAIL interaction exists in external view");
        dpCheck.checkTableInSyncByField(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction.getUri());
        dpCheck.checkTableInSyncByEmailFromField(splittedOrCommon_interactionsView_Email, interaction.getUri(), hcp.getId());
        dpCheck.checkTableInSyncByEmailToField(splittedOrCommon_interactionsView_Email, interaction.getUri(), hco.getId());

        log.info("Step 2.3. Validate EXHIBITION interaction exists in external view");
        dpCheck.checkTableInSyncByField(splittedOrCommon_interactionsView_ExhibitionEvent, URI_FIELD, interaction2.getUri());
        dpCheck.checkTableInSyncByEventHcpField(splittedOrCommon_interactionsView_ExhibitionEvent, interaction2.getUri(), hcp.getId());
        dpCheck.checkTableInSyncByEventHcoField(splittedOrCommon_interactionsView_ExhibitionEvent, interaction2.getUri(), hco.getId());

        log.info("Step 3.1. Delete interactions");
        interaction.delete();
        interaction2.delete();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate deleted interaction exists in external table");
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? interactionsTable_Email : interactionsTable, URI_FIELD, interaction.getUri(), 1);
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? interactionsTable_ExhibitionEvent : interactionsTable, URI_FIELD, interaction2.getUri(), 1);

        log.info("Step 3.3. Validate interaction were deleted from external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_ExhibitionEvent, URI_FIELD, interaction2.getUri(), 0);
    }

    @TmsLink("RP-TC-11409")
    @Test(groups = {"smoke", "regression", "pipeline"}, description = "Data pipeline: verify version")
    public void runRpTc11409() {
        log.info("Step 1.1. Get Data Pipeline Hub version");
        ComponentVersion versionModel = getServiceVersion(Service.DATAPIPELINEHUB, false).getComponent("DATA-PIPELINE-HUB");
        log.info("Step 1.2. Verify Data Pipeline Hub version is not empty");
        dpCheck.checkDataPipelineVersionIsNotEmpty(versionModel);
    }

    @TmsLink("RP-TC-11411")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake3", "pipeline"}, description = "Data pipeline: check interactions after unmerge two entities")
    public void runRpTc11411() throws Exception {
        log.info("Step 1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel hco1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);
        EntityModel hco2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Create EMAIL interactions");
        InteractionModel interaction1 = new InteractionModel(INTERACTION_TYPE_EMAIL)
                .addMember("From", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/From")
                        .addSubMember(new InteractionSubMember(entity1.getUri())));
        interaction1.addMember("To", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/To")
                .addSubMember(new InteractionSubMember(hco1.getUri())));
        interaction1.post();
        InteractionModel interaction2 = new InteractionModel(INTERACTION_TYPE_EMAIL)
                .addMember("From", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/From")
                        .addSubMember(new InteractionSubMember(entity2.getUri())));
        interaction2.addMember("To", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/To")
                .addSubMember(new InteractionSubMember(hco2.getUri())));
        interaction2.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate EMAIL interaction exists in external view");
        dpCheck.checkTableInSyncByField(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction1.getUri());
        dpCheck.checkTableInSyncByField(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction2.getUri());
        dpCheck.checkTableInSyncByEmailFromField(splittedOrCommon_interactionsView_Email, interaction1.getUri(), entity1.getId());
        dpCheck.checkTableInSyncByEmailFromField(splittedOrCommon_interactionsView_Email, interaction2.getUri(), entity2.getId());

        log.info("Step 3.1. Merge entities");
        EntityModel merge = EntityService.mergeEntitiesWithWaiter(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate merge record exists in external view");
        dpCheck.checkTableInSyncByField(mergesView, WINNER_ID_FIELD, merge.getId());
        dpCheck.checkTableInSyncByField(mergesView, DIRECT_WINNER_FIELD, merge.getId());
        dpCheck.checkTableInSyncByField(mergesView, LOSER_ID_FIELD,
                merge.getId().equals(entity1.getInitialId()) ? entity2.getInitialId() : entity1.getInitialId());

        // Validation skipped because of to-be-rejected bug RP-99996
        // log.info("Step 3.3. Validate interactions contain only winnerId member in external table");
        // dpc.checkTableInSyncByNestedFieldCountWithCondition(interactionsView, MEMBERS_EMAIL_FROM_FIELD, ID_FIELD, merge.getId(), " AND deleted = false", 2);

        log.info("Step 4.1. Unmerge entities");
        Map<String, EntityModel> unmergedEntities = EntityService.unmergeEntitiesWithWaiter(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 4.2. Validate interactions contain both members in external view");
        dpCheck.checkTableInSyncByEmailFromFieldCount(splittedOrCommon_interactionsView_Email, interaction1.getUri(), entity1.getInitialId(), 1);
        dpCheck.checkTableInSyncByEmailFromFieldCount(splittedOrCommon_interactionsView_Email, interaction2.getUri(), entity2.getInitialId(), 1);
    }

    @TmsLink("RP-TC-11412")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake3", "deltalake2"}, description = "Data pipeline: merge and unmerge entities - automatic")
    public void runRpTc11412() throws Exception {
        log.info("Step 1.1. Wait for empty queue after previous tests");
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        TenantManagementService.cleanTenant();
        dps.createDataPipelineTablesViews(true);
        sleep(60_000);

        log.info("Step 1.2. Import entities, auto-match and auto-merge will trigger");
        setDataFolder("resources:api/entity/Merges");
        List<EntityModel> entities = EntityService.postEntities(loadFile("EntityAutomerge1.json"));
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        SmartWaiting.waitForQueueStayEmpty(Config.getTenantId(), true, true, 10);
        SmartWaiting.waitForValue(SearchService::searchTotal, 20, "Entities were not automerged as expected", true);
        dpCheck.checkTableInSyncByCountAll(splittedOrCommon_entitiesView_HCP, IS_LEGACY_OR_SPLITTED ? searchTotal(new GetEntityRequestParameters().
                filter(new SearchFilterGroup(equalsFilter("type", ENTITY_TYPE_HCP)))) : searchTotal());
        List<EntityModel> entities2 = EntityService.postEntities(loadFile("EntityAutomerge2.json"));
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        SmartWaiting.waitForQueueStayEmpty(Config.getTenantId(), true, true, 10);
        SmartWaiting.waitForValue(SearchService::searchTotal, 40, "Entities were not loaded as expected", true);
        dpCheck.checkTableInSyncByCountAll(splittedOrCommon_entitiesView_HCP, IS_LEGACY_OR_SPLITTED ? searchTotal(new GetEntityRequestParameters().
                filter(new SearchFilterGroup(equalsFilter("type", ENTITY_TYPE_HCP)))) : searchTotal());

        List<String> entityKeys = new ArrayList<>();
        for (int i = 0; i < entities.size(); i++) {
            entityKeys.add(entities.get(i).getId() + ":" + entities2.get(i).getId());
        }

        dpCheck.checkMatchesBeforeMerge(matchesView, entityKeys);

        EntityService.postEntities(loadFile("EntityAutomerge3.json"));
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        SmartWaiting.waitForQueueStayEmpty(Config.getTenantId(), true, true, 10);

        log.info("Step 1.3. Validate entities count in external view");
        dpCheck.checkTableInSyncByCountAll(mergesView, 10);
        SmartWaiting.waitForValue(SearchService::searchTotal, 40, "Entities were not loaded as expected", true);
        dpCheck.checkTableInSyncByCountAll(splittedOrCommon_entitiesView_HCP, IS_LEGACY_OR_SPLITTED ? searchTotal(new GetEntityRequestParameters().
                filter(new SearchFilterGroup(equalsFilter("type", ENTITY_TYPE_HCP)))) : searchTotal());

        log.info("Step 2.1. Validate merge records exist in external view");
        List<String> mergeKeys = EntitiesHelper.getMergeKeys(entities);
        assertEquals(mergeKeys.size(), 10, "Merge keys count is wrong after merge");
        dpCheck.checkMatchesAfterMerge(mergesView, matchesView, mergeKeys);

        log.info("Step 3.1. Unmerge all entities");
        for (String merge : mergeKeys) {
            EntitiesHelper.unmergeTwoEntities("entities/".concat(dpCheck.getWinnerId(merge)), "entities/".concat(dpCheck.getLoserId(merge)));
        }
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        SmartWaiting.waitForQueueStayEmpty(Config.getTenantId(), true, true, 10);

        log.info("Step 3.2. Validate merge records and not_match records exist in external view");
        dpCheck.checkMatchesAfterUnmerge(mergesView, matchesView, mergeKeys);

        log.info("Step 3.3. Validate entities record count in external view");
        SmartWaiting.waitForValue(SearchService::searchTotal, 50, "Entities were not unmerged as expected", true);
        dpCheck.checkTableInSyncByCountAll(splittedOrCommon_entitiesView_HCP, IS_LEGACY_OR_SPLITTED ? searchTotal(new GetEntityRequestParameters().
                filter(new SearchFilterGroup(equalsFilter("type", ENTITY_TYPE_HCP)))) : searchTotal());
    }

    @TmsLink("RP-TC-11444")
    @Test(groups = {"smoke", "regression", "gbq4", "snowflake4", "deltalake3", "pipeline"}, description = "Data pipeline: create and update entity having nested attribute")
    public void runRpTc11444() throws ReltioObjectException {
        log.info("Step 1.1. Create entity");
        EntityModel entity = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 2.1. Add nested attribute");
        String phoneType1 = "Work";
        NestedAttributeModel phone1 = new NestedAttributeModel(new HashMap<>());
        phone1.addAttributeToModel(PHONE_TYPE, new SimpleAttributeModel(phoneType1));
        phone1.addAttributeToModel(PHONE_AREA_CODE, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomNumbers()));
        phone1.addAttributeToModel(PHONE_NUMBER, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        entity.addAttributeToModel(PHONE, phone1);

        String phoneType2 = "Home";
        NestedAttributeModel phone2 = new NestedAttributeModel(new HashMap<>());
        phone2.addAttributeToModel(PHONE_TYPE, new SimpleAttributeModel(phoneType2));
        phone2.addAttributeToModel(PHONE_AREA_CODE, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomNumbers()));
        phone2.addAttributeToModel(PHONE_NUMBER, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        entity.addAttributeToModel(PHONE, phone2);
        entity.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate 1st nested attribute value exists in external view");
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity.getUri(), entity.getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByPhoneNumber(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType1, phone1.getAttribute(PHONE_NUMBER).getValue().toString());
        dpCheck.checkTableInSyncByPhoneAreaCode(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType1, phone1.getAttribute(PHONE_AREA_CODE).getValue().toString());

        log.info("Step 2.3. Validate 2nd nested attribute value exists in external view");
        dpCheck.checkTableInSyncByPhoneNumber(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType2, phone2.getAttribute(PHONE_NUMBER).getValue().toString());
        dpCheck.checkTableInSyncByPhoneAreaCode(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType2, phone2.getAttribute(PHONE_AREA_CODE).getValue().toString());
    }

    @TmsLink("RP-TC-11491")
    @Test(groups = {"smoke", "regression", "gbq4", "snowflake4", "deltalake3"}, description = "Data pipeline: potential matches")
    public void runRpTc11491() throws IOException, ReltioObjectException, CommandException {
        log.info("Step 1.1. Create potential matches");
        setDataFolder("resources:api/entity/Matches");
        List<EntityModel> entities1 = EntityService.postEntities(loadFile("EntityMatches1.json"));
        List<EntityModel> entities2 = EntityService.postEntities(loadFile("EntityMatches2.json"));
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.2. Validate potential matches exist in external view");
        dpCheck.checkTableInSyncByCountWithPotentialMatches(matchesView, "'true'", "true", 20);

        log.info("Step 2.1. Manual match some of the entities");
        EntitiesHelper.setAsMatches(entities1.get(0), entities2.get(0));
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        String matchesViewField = IS_LEGACY ? MATCHES_TARGET_ID : ENTITY_ID;

        log.info("Step 2.2. Validate entities 'manual_matches' each other in external view. Potential match is separate record for LEGACY");
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField, entities1.get(0).getId(), IS_LEGACY ? 2 : 1);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField, entities2.get(0).getId(), IS_LEGACY ? 2 : 1);
        dpCheck.checkEntitiesManualMatch(matchesView, entities1.get(0).getId(), entities2.get(0).getId());

        log.info("Step 3.1. Delete one of the entities");
        entities1.get(0).delete();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate 'manual_matches' still exist for both entities in external view. Potential match should be deleted");
        // Deltalake physically removes deleted entities from matches table, unlike GBQ/Snowflake soft deletion
        int expectedCountForDeletedEntity = "DELTALAKE".equals(dataPipelineAdapter) ? 0 : 1;
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField, entities1.get(0).getId(), expectedCountForDeletedEntity);
        dpCheck.checkTableInSyncByCount(matchesView, matchesViewField, entities2.get(0).getId(), 1);
        if (!"DELTALAKE".equals(dataPipelineAdapter)) {
            dpCheck.checkEntitiesManualMatch(matchesView, entities1.get(0).getId(), entities2.get(0).getId());
        }

        log.info("Step 4.1. Manual match/unmatch some other entities");
        EntitiesHelper.setAsMatches(entities1.get(1), entities2.get(1));
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        dpCheck.checkEntitiesManualMatch(matchesView, entities1.get(1).getId(), entities2.get(1).getId());
        EntitiesHelper.setAsNotMatches(entities1.get(1), entities2.get(1));
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 4.2. Validate entities 'not_matches' each other in external view; potential matches and manual matches are empty");
        dpCheck.checkEntitiesNotMatch(matchesView, entities1.get(1).getId(), entities2.get(1).getId());
        dpCheck.checkTableInSyncByCountWithMissingMatches(matchesView, matchesViewField, entities1.get(1).getId(), IS_LEGACY ? 0 : 1);
        dpCheck.checkTableInSyncByCountWithMissingMatches(matchesView, matchesViewField, entities2.get(1).getId(), IS_LEGACY ? 0 : 1);
    }

    @TmsLink("RP-TC-11660")
    @Test(groups = {"smoke", "regression", "gbq1", "snowflake4", "deltalake3"}, description = "Data pipeline: OV for deep nested attribute")
    public void runRpTc11660() throws ReltioObjectException {
        String areaCode = "AREA-CODE-FOR-TC-11444";
        String phoneWork = "PHONE-123-FOR-TC-11444";
        String phoneHome = "PHONE-456-FOR-TC-11444";
        String Address1 = "ADDRESS-ONE-FOR-TC-11444";
        String Address2 = "ADDRESS-TWO-FOR-TC-11444";

        log.info("Step 1.1. Set 'survivorshipStrategy: MaxValue' for Address and Phone in tenant config");
        updateBusinessConfig("config/Config/L3-config-new-entityType.json");

        log.info("Step 2.1. Create entity having nested attributes");
        EntityModel entity = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        NestedAttributeModel address1 = new NestedAttributeModel(new HashMap<>());
        address1.addAttributeToModel(ADDRESS_LINE_ONE, new SimpleAttributeModel(Address1));
        entity.addAttributeToModel(ADDRESS, address1);

        NestedAttributeModel address2 = new NestedAttributeModel(new HashMap<>());
        address2.addAttributeToModel(ADDRESS_LINE_ONE, new SimpleAttributeModel(Address2));
        entity.addAttributeToModel(ADDRESS, address2);

        String phoneType1 = "Work";
        NestedAttributeModel phone1 = new NestedAttributeModel(new HashMap<>());
        phone1.addAttributeToModel(PHONE_TYPE, new SimpleAttributeModel(phoneType1));
        phone1.addAttributeToModel(PHONE_AREA_CODE, new SimpleAttributeModel(areaCode));
        phone1.addAttributeToModel(PHONE_NUMBER, new SimpleAttributeModel(phoneWork));
        entity.addAttributeToModel(PHONE, phone1);

        String phoneType2 = "Home";
        NestedAttributeModel phone2 = new NestedAttributeModel(new HashMap<>());
        phone2.addAttributeToModel(PHONE_TYPE, new SimpleAttributeModel(phoneType2));
        phone2.addAttributeToModel(PHONE_AREA_CODE, new SimpleAttributeModel(areaCode));
        phone2.addAttributeToModel(PHONE_NUMBER, new SimpleAttributeModel(phoneHome));
        entity.addAttributeToModel(PHONE, phone2);

        entity.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Validate 1st nested attribute value exists in external view");
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity.getUri(), entity.getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByPhoneNumber(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType1, phoneWork);
        dpCheck.checkTableInSyncByPhoneAreaCode(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType1, areaCode);

        log.info("Step 2.3. Validate 2nd nested attribute value exists in external view");
        dpCheck.checkTableInSyncByPhoneNumber(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType2, phoneHome);
        dpCheck.checkTableInSyncByPhoneAreaCode(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneType2, areaCode);

        log.info("Step 3.1. Validate attribute with OV=true in external view");
        dpCheck.checkTableInSyncByHcpAddressFieldOvCondition(splittedOrCommon_entitiesView_HCP, entity.getUri(), Address2, "true", "true");
        dpCheck.checkTableInSyncByHcpPhoneFieldOvCondition(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneHome, "true", "true");

        log.info("Step 3.2. Validate attribute with OV=false in external view");
        dpCheck.checkTableInSyncByHcpAddressFieldOvCondition(splittedOrCommon_entitiesView_HCP, entity.getUri(), Address1, "false", "false");
        dpCheck.checkTableInSyncByHcpPhoneFieldOvCondition(splittedOrCommon_entitiesView_HCP, entity.getUri(), phoneWork, "false", "false");
    }

    @TmsLink("RP-TC-11662")
    @Test(groups = {"smoke", "regression", "gbq1", "snowflake4"}, description = "Data pipeline: add new entityType to tenant config")
    public void runRpTc11662() throws ReltioObjectException {
        log.info("Step 1.1. Update Business config with new entity type");
        updateBusinessConfig("config/Config/L3-config-new-entityType.json");

        log.info("Step 2.1. Create an entity with a new type");
        EntityModel entity = EntitiesHelper.createEntity(ENTITY_TYPE_PREFIX + "/MyCompanyType", List.of(NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(NAME), null);
        NestedAttributeModel license = new NestedAttributeModel(new HashMap<>());
        license.addAttributeToModel("Category", new SimpleAttributeModel("CORRECT_VALUE"));
        license.addAttributeToModel("MyCategory", new SimpleAttributeModel("WRONG_VALUE"));
        entity2.addAttributeToModel("License", license);
        entity2.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        String splittedOrCommon_entitiesTable_MyCompanyType = IS_LEGACY_OR_SPLITTED ?
                dps.getFullTableName(GBQ_TABLE_ENTITY_MYCOMPANY_ID) : entitiesTable;
        String myCompanyType_Name_field = ATTRIBUTES_PREFIX + "MyCompanyType.Name";

        log.info("Step 2.2. Validate new entities exist in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesTable_MyCompanyType, URI_FIELD, entity.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), 1);

        if ("GBQ".equals(dataPipelineAdapter)) {
            if (IS_SPLIT_TABLE) {
                log.info("Step 2.3. SPLIT tables: new entity exists in a separate table for new entity type");
                dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesTable_MyCompanyType, entity.getUri(), myCompanyType_Name_field, VALUE_FIELD, entity.getAttribute(NAME).getValue().toString());
            } else {
                log.info("Step 2.3. SINGLE table: 'conflictedAttributes' field contains a new entity type in external view");
                dpCheck.checkTableInSyncByFieldPartialMatch(entitiesView, URI_FIELD, entity.getUri(), CONFLICTED_ATTRIBUTES_FIELD, "Type not present in GBQ Schema");
                dpCheck.checkTableInSyncByFieldPartialMatch(entitiesView, URI_FIELD, entity.getUri(), CONFLICTED_ATTRIBUTES_FIELD, entity.getAttribute(NAME).getUri());
            }
            log.info("Step 2.4. Validate 'conflictedAttributes' field contains a missing attribute in external view");
            dpCheck.checkTableInSyncByFieldPartialMatch(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), CONFLICTED_ATTRIBUTES_FIELD, "GBQ field is not found for current attribute");
            dpCheck.checkTableInSyncByFieldPartialMatch(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), CONFLICTED_ATTRIBUTES_FIELD, "WRONG_VALUE");
        }
    }

    @TmsLink("RP-TC-11756")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake4", "deltalake3", "pipeline"}, description = "Data pipeline: syncToDataPipeline with options")
    public void runRpTc11756() {
        log.info("Step 1.1. Disable DataPipeline and Streaming");
        dps.setDataPipelineState(queueName, false);
        dps.setStreamingConfig(getTenant().getStreamingConfig().streamingEnabled(false));
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        sleep(10_000);
        EntityModel entity3 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity4 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity1.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity3.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity4.getUri(), 0);

        log.info("Step 1.2. Send syncToDataPipeline request with 'updatedSince' parameter");
        dps.setStreamingConfig(getTenant().getStreamingConfig().streamingEnabled(true));
        dps.setDataPipelineState(queueName, true);

        JsonElement result = dps.syncToDataPipeline("updatedSince=" + entity2.getUpdatedTime());
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.3. Verify only records newer than 'updatedSince' were synced");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity1.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity3.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity4.getUri(), 1);

        log.info("Step 2.1. Send syncToDataPipeline request with 'parallelExecution' option");
        result = dps.syncToDataPipeline("options=parallelExecution");

        log.info("Step 2.2. Verify 5 jobs started with parallelExecution=true");
        assertNotNull(result.getAsJsonArray(), "syncToDataPipeline result is null");
        assertEquals(result.getAsJsonArray().size(), DATAPIPELINE_SYNC_JOBS_COUNT, "syncToDataPipeline jobs number is wrong.");
        result.getAsJsonArray().forEach(job -> assertEquals(job.getAsJsonObject().get("parallelExecution").getAsBoolean(), true));
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.1. Send syncToDataPipeline request without 'parallelExecution' option");
        result = dps.syncToDataPipeline();

        log.info("Step 3.2. Verify 5 jobs started with parallelExecution=false");
        assertEquals(result.getAsJsonArray().size(), DATAPIPELINE_SYNC_JOBS_COUNT, "syncToDataPipeline jobs number is wrong.");
        result.getAsJsonArray().forEach(job -> assertEquals(job.getAsJsonObject().get("parallelExecution").getAsBoolean(), false));
    }

    @TmsLink("RP-TC-11757")
    @Test(groups = {"smoke", "regression", "gbq3"}, description = "Data pipeline: ovOnly parameter")
    public void runRpTc11757() throws ReltioObjectException {
        log.info("Step 1. Set BigqueryAdapterConfig.ovOnly=true in tenant configuration");
        tenantModel = getTenant();
        tenantModel.getDataPipelineConfig().getBigqueryAdapterConfig().setOvOnly(true);
        tenantModel = createTenant(tenantModel);

        String ovFirstName = "RP-TC-11757-OV";
        String nonOvFirstName = "RP-TC-11757-nonOV";
        log.info("Step 2.1. Create entity, add attributes");
        EntityModel entity = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        entity.updateOrCreateSimpleAttributeInModel(FIRST_NAME, ovFirstName);
        entity.addSimpleAttributeToModel(FIRST_NAME, nonOvFirstName);
        entity.post();

        log.info("Step 2.2. Set OV=false for one of the values");
        entity.getAttribute(FIRST_NAME, nonOvFirstName).ignoreSet();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.3. Validate updated attribute with OV=true exists in external view");
        dpCheck.checkTableInSyncByFirstNameFieldCount(splittedOrCommon_entitiesView_HCP, entity.getUri(), ovFirstName, 1);
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity.getUri(), ovFirstName);

        log.info("Step 2.4. Validate updated attribute with OV=false does not exist in external view");
        dpCheck.checkTableInSyncByFirstNameFieldCount(splittedOrCommon_entitiesView_HCP, entity.getUri(), nonOvFirstName, 0);
    }

    @TmsLink("RP-TC-11770")
    @Test(groups = {"smoke", "regression", "gbq7", "snowflake5"}, description = "Data pipeline: bulk insert entities")
    public void runRpTc11770() throws IOException, ReltioObjectException, CommandException {
        log.info("Step 1.1. Clean tenant");
        TenantManagementService.cleanTenant();
        dps.createDataPipelineTablesViews(true);

        log.info("Step 1.2. Bulk insert HCP entities");
        setDataFolder("resources:api/bulkupdate/BulkUpdates");
        EntityService.postEntities(loadFile("HMS_400K_HCPs_p1.json"));
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        SmartWaiting.waitForQueueStayEmpty(Config.getTenantId(), true, true, 10);

        log.info("Step 2. Validate entities count in external view");
        SmartWaiting.waitForValue((() -> searchTotal() < 300), true, "Entities were not automerged as expected", true);
        SmartWaiting.waitForValue(() -> dpCheck.getRowCountAll(splittedOrCommon_entitiesView_HCP), IS_LEGACY_OR_SPLITTED ?
                searchTotal(new GetEntityRequestParameters().
                        filter(new SearchFilterGroup(equalsFilter("type", ENTITY_TYPE_HCP))))
                : searchTotal(), "Entities were not bulk inserted as expected", true);
        log.info("Entities count: " + dpCheck.getRowCountAll(splittedOrCommon_entitiesView_HCP));
    }

    @TmsLink("RP-TC-11863")
    @Test(groups = {"smoke", "regression", "gbq7", "snowflake5"}, description = "Data pipeline: crosswalks validation")
    public void runRpTc11863() throws Exception {
        log.info("Step 1.1. Set preconditionsForMerge");
        preconditionsForMerge();

        log.info("Step 1.2. Create entity");
        EntityModel e1 = new EntityModel(loadFile("entity1_1.json")).post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.1. Validate entity exists in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getUri(), 1);

        log.info("Step 2.2. Validate all crosswalks attributes exists in external view");
        e1.getCrosswalks().forEach(crosswalk -> {
            dpCheck.checkTableInSyncByCrosswalksAttributesCount(splittedOrCommon_entitiesView_HCP, e1.getInitialUri(), crosswalk.getUri(), crosswalk.getAttributes().size());
            crosswalk.getAttributes().forEach(attribute -> {
                dpCheck.checkTableInSyncByCrosswalksAttributeFieldCount(splittedOrCommon_entitiesView_HCP, e1.getInitialUri(), attribute, crosswalk.getUri(), 1);
            });
        });
    }

    @TmsLink("RP-TC-11864")
    @Test(groups = {"smoke", "regression", "gbq7", "snowflake5"}, description = "Data pipeline: complex merge/unmerge")
    public void runRpTc11864() throws Exception {
        log.info("Step 1.1. Set preconditionsForMerge");
        preconditionsForMerge();

        log.info("Step 2.1. Create 5 entities and validate 5 entities exist in external view");
        EntityModel e1 = new EntityModel(loadFile("entity1_1.json")).post();
        EntityModel e2 = new EntityModel(loadFile("entity2_1.json")).post();
        EntityModel e3 = new EntityModel(loadFile("entity3_1.json")).post();
        EntityModel e4 = new EntityModel(loadFile("entity4_1.json")).post();
        EntityModel e5 = new EntityModel(loadFile("entity5_1.json")).post();

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e2.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e3.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e4.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e5.getUri(), 1);

        log.info("Step 2.2. Merge all 5 entities and validate 1 entity exists in external view");
        EntityService.mergeEntities(e3, e4);
        EntityService.mergeEntities(e3, e5);
        EntityService.mergeEntities(e2, e3);
        EntityService.mergeEntities(e1, e2);

        SmartWaiting.waitForEntitiesToBeMerged(Arrays.asList(e1, e2, e3, e4, e5));
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e2.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e3.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e4.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e5.getInitialUri(), 0);

        log.info("Step 3.1. Unmerge 2 entities and validate 2 entities exist in external view");
        EntityService.unmergeEntities(e1.getUri(), e2.getInitialUri());
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e2.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e3.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e4.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e5.getInitialUri(), 0);

        log.info("Step 3.2. Unmerge 3 entities and validate 3 entities exist in external view");
        EntityService.unmergeEntities(e1.getUri(), e3.getInitialUri());
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e2.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e3.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e4.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e5.getInitialUri(), 0);

        log.info("Step 3.3. Unmerge 4 entities and validate 4 entities exist in external view");
        EntityService.unmergeEntities(e1.getUri(), e4.getInitialUri());
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e2.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e3.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e4.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e5.getInitialUri(), 0);

        log.info("Step 3.4. Unmerge 5 entities and validate 5 entities exist in external view");
        EntityService.unmergeEntities(e1.getUri(), e5.getInitialUri());
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e1.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e2.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e3.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e4.getInitialUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, e5.getInitialUri(), 1);
    }

    @TmsLink("RP-TC-11659")
    @Test(priority = 10, groups = {"smoke", "regression", "gbq1", "snowflake6", "deltalake5"}, description = "Data pipeline: syncToDataPipeline endpoint test")
    public void runRpTc11659() {
        log.info("Step 1.0. Disable DPH");
        dps.setDataPipelineState(queueName, false);
        TenantManagementService.cleanTenant();
        sleep(60_000);
        log.info("Step 1.1. Generate data in tenant before DataPipelineHub was enabled");
        updateBusinessConfig("config/Config/L3-config-wscore.json");
        generateDataInTenant();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.2. Enable Streaming and Data Pipeline");
        bcEnableStreamingAndDataPipeline();
        // Stabilization - wait before and after re-creating tables
        sleep(120_000);
        dps.createDataPipelineTablesViews(true);
        sleep(120_000);

        log.info("Step 2.1. Send syncToDataPipeline request");
        JsonElement result = dps.syncToDataPipeline();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2. Verify syncToDataPipeline result contains all kinds of reindex tasks");
        assertNotNull(result.getAsJsonArray(), "syncToDataPipeline result is null");
        assertEquals(result.getAsJsonArray().size(), DATAPIPELINE_SYNC_JOBS_COUNT, "syncToDataPipeline jobs number is wrong.");
        assertEquals(result.getAsJsonArray().get(0).getAsJsonObject().get("type").getAsString(), "com.reltio.businesslogic.tasks.reindex.ReindexDataTask");
        assertEquals(result.getAsJsonArray().get(1).getAsJsonObject().get("type").getAsString(), "com.reltio.businesslogic.tasks.reindex.ReindexRelationsTask");
        assertEquals(result.getAsJsonArray().get(2).getAsJsonObject().get("type").getAsString(), "com.reltio.businesslogic.tasks.reindex.ReindexInteractionsTask");
        assertEquals(result.getAsJsonArray().get(3).getAsJsonObject().get("type").getAsString(), "com.reltio.businesslogic.tasks.reindex.PotentialMatchesReindexTask");
        assertEquals(result.getAsJsonArray().get(4).getAsJsonObject().get("type").getAsString(), "com.reltio.businesslogic.tasks.reindex.ReindexMergesTask");

        log.info("Step 3.1. Verify HCP entities");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(0).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(1).getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(2).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(3).getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(4).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(5).getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcpList.get(6).getUri(), 1);
        log.info("Step 3.2. Verify HCO entities");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hcoList.get(0).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hcoList.get(1).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hcoList.get(2).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hcoList.get(3).getUri(), 1);
        log.info("Step 3.3. Verify relations");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_relationsView_HasAddress, URI_FIELD, relationList.get(0).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_relationsView_HasAddress, URI_FIELD, relationList.get(1).getUri(), 1);
        log.info("Step 3.4. Verify interactions");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_ExhibitionEvent, URI_FIELD, interactionList.get(0).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_Email, URI_FIELD, interactionList.get(1).getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_Email, URI_FIELD, interactionList.get(2).getUri(), 1);
        log.info("Step 3.5. Verify merges");
        dpCheck.checkTableInSyncByCount(mergesView, WINNER_ID_FIELD, mergeList.get(0).getId(), 0);
        dpCheck.checkTableInSyncByCount(mergesView, WINNER_ID_FIELD, mergeList.get(1).getId(), 1);
        log.info("Step 3.6. Verify matches");
        dpCheck.checkEntitiesNotMatch(matchesView, hcoList.get(0).getId(), hcoList.get(1).getId());
        dpCheck.checkEntitiesManualMatch(matchesView, hcpList.get(2).getId(), hcpList.get(6).getId());
    }

    @TmsLink("RP-TC-13785")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake2", "deltalake3"}, description = "Deltalake adapter - links data")
    public void runRpTc13785() throws Exception {
        // This test is implemented in DataPipelineDeltalakeTests.java for Deltalake-specific validation
        // Adding group here for test execution coordination
        throw new SkipException("RP-TC-13785 is implemented in DataPipelineDeltalakeTests.java");
    }

    @TmsLink("RP-TC-11796")
    @Test(groups = {"smoke", "regression", "gbq1", "snowflake6", "deltalake5", "pipeline-base"}, description = "Data pipeline: syncToDataPipeline results test")
    public void runRpTc11796() throws Exception {
        log.info("Step1.0. Disable dph config");
        dps.setDataPipelineState(queueName, false);

        log.info("Step 1.1. Generate data in tenant before DataPipelineHub was enabled");
        updateBusinessConfig("config/Config/L3-config-wscore.json");

        EntityModel hcp = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel hco = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);
        EntityModel hco1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);
        EntityModel hco2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);

        EntityModel location = EntitiesHelper.createEntity(ENTITY_TYPE_LOCATION, List.of(ADDRESS_LINE_ONE), null);
        RelationModel relationModel = new RelationModel(RELATION_TYPE_HASADDRESS);
        relationModel.setStartObjectUri(hco1.getUri());
        relationModel.setEndObjectUri(location.getUri());
        relationModel.addSimpleAttributeToModel(ADDRESS_RANK, "1").post();

        InteractionModel interaction = new InteractionModel(INTERACTION_TYPE_EMAIL)
                .addMember("From", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/From")
                        .addSubMember(new InteractionSubMember(hcp.getUri())));
        interaction.addMember("To", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/To")
                .addSubMember(new InteractionSubMember(hco.getUri()))).post();

        EntityModel merge = EntityService.mergeEntitiesWithWaiter(hco1, hco2);
        hco.setMatch(hco1);

        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.2. Enable Data Pipeline");
        dps.setDataPipelineState(queueName, true);

        log.info("Step 2.1. Send syncToDataPipeline request");
        dps.syncToDataPipeline();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.1. Verify entities");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcp.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hco.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hco1.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hco2.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_Location, URI_FIELD, location.getUri(), 1);
        log.info("Step 3.2. Verify relations");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_relationsView_HasAddress, URI_FIELD, relationModel.getUri(), 1);
        log.info("Step 3.3. Verify interactions");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction.getUri(), 1);
        log.info("Step 3.4. Verify merges");
        dpCheck.checkTableInSyncByCount(mergesView, WINNER_ID_FIELD, merge.getId(), 1);
        log.info("Step 3.5. Verify matches");
        dpCheck.checkEntitiesManualMatch(matchesView, hco.getId(), hco1.getId());
        log.info("Step 3.6 Validate links in external table");
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, hco1.getId(), 1);
    }

    @TmsLink("RP-TC-11865")
    @Test(priority = 1, groups = {"smoke", "regression", "gbq1", "snowflake6"}, description = "Data pipeline: hidden reference attribute")
    public void runRpTc11865() throws ReltioObjectException {
        log.info("Step 1.1. Enable Streaming and Data Pipeline");
        bcEnableStreamingAndDataPipeline();

        log.info("Step 1.2. Update tenant configuration");
        updateBusinessConfig("config/Config/L3-configuration-360.json");


        log.info("Step 1.3. Create tables and views");
        assertTrue(dps.createDataPipelineTablesViews(true), "Data pipeline tables/views creation failed.");

        log.info("Step 1.4. Create entity having hidden reference attribute");
        EntityModel entity_contact = new EntityModel("configuration/entityTypes/Contact")
                .addSimpleAttributeToModel(NAME, "Ref Contact")
                .post();

        EntityModel entity_deployment = new EntityModel("configuration/entityTypes/Deployment")
                .addSimpleAttributeToModel(NAME, "Deployment Name")
                .post();

        ReferenceAttributeModel refer = new ReferenceAttributeModel();
        refer.addSimpleAttributeToModel(NAME, "Ref Name");
        refer.setRefEntity(new RefObject(entity_contact.getUri()));

        entity_deployment.postAttribute("ExternalTeam", refer);
        entity_deployment.refresh(true);

        log.info("Step 2.1. Validate a hidden attribute exists in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_Deployment, URI_FIELD, entity_deployment.getUri(), 1);
        dpCheck.checkTableInSyncByDeploymentExternalTeamField(splittedOrCommon_entitiesView_Deployment, entity_deployment.getUri(), entity_deployment.getAttribute("ExternalTeam").getId());
    }

    @TmsLink("RP-TC-11383")
    @Test(groups = {"smoke", "regression", "gbq2", "pipeline"}, description = "Data pipeline: Enable Data pipeline with wrong Streaming configuration")
    public void runRpTc11383() {
        log.info("Step 1. Disable Data pipeline");
        tenantModel = TenantModel.fromJson(dps.setDataPipelineState(queueName, false));

        assertFalse(tenantModel.getDataPipelineConfig().getEnabled(), "Data pipeline was not disabled for tenant " + Config.getTenantId());

        log.info("Step 2. Enable Data pipeline should fail when a wrong Queue name provided");
        String result = dps.setDataPipelineState("WRONG_QUEUE_NAME", true);
        assertEquals(result, "TENANT_CONFIGURATION_VALIDATION_ERROR", "Data pipeline was enabled yet wrong queueName was provided for tenant " + Config.getTenantId());

        log.info("Step 3. Enable Data pipeline");
        tenantModel = TenantModel.fromJson(dps.setDataPipelineState(queueName, true));
        assertTrue(tenantModel.getDataPipelineConfig().getEnabled(), "Data pipeline was not enabled for tenant " + Config.getTenantId());
    }

    @TmsLink("RP-TC-11874")
    @Test(groups = {"smoke", "regression", "gbq7", "snowflake5"}, description = "Check eventMonitoring returns success events")
    public void runRpTc11874() {

        log.info("Wait for monitoring tables creation");
        sleep(MONITORING_TABLES_CREATION_SLEEP);

        JsonElement eventMonitoringResultInitial = monitoringRequest(EVENT_MONITORING_URL);
        int expectedCount = eventMonitoringResultInitial.getAsJsonObject().get("total_events_count").getAsInt() + 1;
        String eventMonitoringResultExpected = String.format("{\"total_events_count\":%s,\"total_incomplete_events_count\":0}", expectedCount);

        log.info("Create HCP entity and store id");
        EntityModel em = EntitiesHelper.createHCP();
        SmartWaiting.waitForEmptyEventQueue();

        String entityId = em.getId();
        Long fromTime = em.getCreatedTime();

        log.info("Check eventMonitoring request with default params returns expected results");
        waitMonitoringReturnsExpectedResult(eventMonitoringResultExpected, EVENT_MONITORING_URL);
        log.info("Check eventMonitoring request with 'from' param returns expected results");
        JsonElement eventMonitoringResult = monitoringRequest(EVENT_MONITORING_URL, "from=" + fromTime);
        Assert.assertEquals(eventMonitoringResult.getAsJsonObject().get("total_events_count").getAsInt(), 1);

        log.info("Check entityMonitoring request with entityId param returns expected results");
        JsonElement entityMonitoringResult = monitoringRequest(ENTITY_MONITOR_URL, ENTITY_MONITORING_PARAM_ID + entityId);
        Assert.assertEquals(entityMonitoringResult.getAsJsonObject().get("total_incomplete_events_count").getAsInt(), 0);
        Assert.assertEquals(entityMonitoringResult.getAsJsonObject().get("events").getAsJsonArray().size(), 4);
    }

    @TmsLink("RP-TC-11911")
    @Test(groups = {"smoke", "regression", "gbq4", "snowflake6", "deltalake5"}, description = "Data pipeline: entity having a crosswalk with dataprovider=false")
    public void runRpTc11911() throws ReltioObjectException {
        log.info("Step 1.1. Create 2 entities having the same crosswalk");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        CrosswalkModel crosswalk1 = new CrosswalkModel(CROSSWALK_TYPE_HMS, "1234");
        crosswalk1.setDataProvider(false);
        entity1.getCrosswalks().add(crosswalk1);
        entity1.post();
        String phantomEntityUri = entity1.getCrosswalkTree().getUriOfContributorByCrosswalk(crosswalk1);

        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        CrosswalkModel crosswalk2 = new CrosswalkModel(CROSSWALK_TYPE_HMS, "1234");
        crosswalk2.setDataProvider(false);
        entity2.getCrosswalks().add(crosswalk2);
        entity2.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.1. Validate only 1 entity exists in external table");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity1.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getInitialUri(), 0);

        log.info("Step 2.2. Validate ON_THE_FLY merge exists in external table");
        dpCheck.checkTableInSyncByMergeType(mergesView, WINNER_ID_FIELD, entity1.getId(), "ON_THE_FLY", 1);
        dpCheck.checkTableInSyncByMergeType(mergesView, LOSER_ID_FIELD, phantomEntityUri.replaceFirst("entities/", ""), "ON_THE_FLY", 1);

        log.info("Step 2.3. Validate CROSSWALKS merge exists in external table");
        dpCheck.checkTableInSyncByMergeType(mergesView, WINNER_ID_FIELD, entity1.getId(), "CROSSWALKS", 1);
        dpCheck.checkTableInSyncByMergeType(mergesView, LOSER_ID_FIELD, entity2.getInitialId(), "CROSSWALKS", 1);

        log.info("Step 2.4. Validate links in external table");
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, entity1.getId(), 2);
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, entity2.getInitialId(), 0);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, entity1.getId(), 0);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, entity2.getInitialId(), 1);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, phantomEntityUri.replaceFirst("entities/", ""), 1);
    }

    @TmsLink("RP-TC-11994")
    @Test(priority = 9, groups = {"smoke", "regression", "gbq4", "snowflake6", "deltalake5"}, description = "Data pipeline: attributes filtering")
    public void runRpTc11994() throws Exception {
        log.info("Step 1.1. Update Business config with filtered attributes");
        tenantModel = getTenant();
        switch (dataPipelineAdapter) {
            case DPH_GBQ_ADAPTER_NAME: {
                tenantModel.getDataPipelineConfig().getBigqueryAdapterConfig().setDataFilteringEnabled(true);
                break;
            }
            case DPH_SNOWFLAKE_ADAPTER_NAME: {
                tenantModel.getDataPipelineConfig().getSnowflakeAdapterConfig().setDataFilteringEnabled(true);
                break;
            }
            case DPH_DELTALAKE_ADAPTER_NAME: {
                tenantModel.getDataPipelineConfig().getDeltalakeAdapterConfig().setDataFilteringEnabled(true);
                break;
            }
            default: {
                throw new ApplicationGlobalException("Unknown data pipeline adapter: " + dataPipelineAdapter);
            }
        }
        tenantModel = createTenant(tenantModel);

        setDataFolder("resources:api");
        BusinessConfigurationService.updateConfig(Config.getTenantId(), loadFile("config/Config/L3-config-filter.json"));
        dps.createDataPipelineTablesViews(true, true);
        sleep(120_000);

        log.info("Step 2.1. Create 3 filtered entities");
        EntityModel loc = EntitiesHelper.createEntity(ENTITY_TYPE_LOCATION, List.of(ADDRESS_LINE_ONE, COUNTRY, CITY), null);
        EntityModel org = EntitiesHelper.createEntity(ENTITY_TYPE_ORGANIZATION, List.of(NAME, STATUS, PHONE), null);
        EntityModel hco = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(NAME, STATUS, PROTOCOL, HOSPITAL_TYPE, PHONE), null);
        EntityModel hcp = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel hcp2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        hco.refresh(true);

        log.info("Step 2.2. Create 2 filtered interactions");
        // Create EMAIL interaction
        InteractionModel email = new InteractionModel(INTERACTION_TYPE_EMAIL)
                .addMember("From", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/From")
                        .addSubMember(new InteractionSubMember(hcp.getUri())));
        email.addMember("To", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/To")
                .addSubMember(new InteractionSubMember(hco.getUri())));
        email.addAttributeToModel(SUBJECT_FIELD, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        email.addAttributeToModel(LOCATION_FIELD, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        email.post();

        // Create EXHIBITION interaction
        InteractionModel event = new InteractionModel(INTERACTION_TYPE_EXHIBITION_EVENT);
        event.addAttributeToModel(EXHBT_NAME, new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        event.addAttributeToModel(EXHBT_DT, new SimpleAttributeModel(TimeUtils.getCurrentDate()));
        event.addAttributeToModel(EXHBT_GLOBAL, new SimpleAttributeModel("false"));
        event.addMember(HCP_FIELD, new InteractionMemberModel(ENTITY_TYPE_HCP)
                .addSubMember(new InteractionSubMember(hcp.getUri())));
        event.addMember(HCO_FIELD, new InteractionMemberModel(ENTITY_TYPE_HCO)
                .addSubMember(new InteractionSubMember(hco.getUri())));
        event.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.3. Create 3 filtered relations");
        RelationModel family = new RelationModel(RELATION_TYPE_FAMILY);
        family.setStartObjectUri(hcp.getUri());
        family.setEndObjectUri(hcp2.getUri());
        family.addSimpleAttributeToModel(COMMENTERS_FIELD, "1234").post();

        RelationModel sibling = new RelationModel(RELATION_TYPE_SIBLING);
        sibling.setStartObjectUri(hcp.getUri());
        sibling.setEndObjectUri(hcp2.getUri());
        sibling.addSimpleAttributeToModel(COMMENTERS_FIELD, "5678").post();

        RelationModel address = new RelationModel(RELATION_TYPE_HASADDRESS);
        address.setStartObjectUri(hco.getUri());
        address.setEndObjectUri(loc.getUri());
        address.addSimpleAttributeToModel(ADDRESS_TYPE, "Home").post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        family.refresh(true);
        sibling.refresh(true);

        log.info("Step 2.4. Validate column count in external table");
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_Location), ATTRIBUTES_LOCATION, 9);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_Organization), ATTRIBUTES_ORGANIZATION, 9);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_Company), ATTRIBUTES_COMPANY, 9);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_IDN), ATTRIBUTES_IDN, 9);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_GPO), ATTRIBUTES_GPO, 9);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_HCO), ATTRIBUTES_HCO, 249);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_entitiesView_HCP), ATTRIBUTES_HCP, 0);

        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_interactionsView_Email), ATTRIBUTES_EMAIL, 3);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_interactionsView_ExhibitionEvent), ATTRIBUTES_EXHIBITIONEVENT, 3);

        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_DomesticPartner), ATTRIBUTES_DOMESTICPARTNER, 5);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_Family), ATTRIBUTES_FAMILY, 5);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_HasAddress), ATTRIBUTES_HASADDRESS, 5);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_Parent), ATTRIBUTES_PARENT, 5);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_Relative), ATTRIBUTES_RELATIVE, 5);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_Sibling), ATTRIBUTES_SIBLING, 5);
        dpCheck.checkTableInSyncBySchemaFieldCount(schemaTable, dps.getShortTableName(splittedOrCommon_relationsView_Spouse), ATTRIBUTES_SPOUSE, 5);

        log.info("Step 2.5. Validate attributes exists in external table");
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_Location, loc.getUri(), LOCATION_ADDRESSLINE1_FIELD, VALUE_FIELD, loc.getAttribute(ADDRESS_LINE_ONE).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_Location, loc.getUri(), LOCATION_COUNTRY_FIELD, VALUE_FIELD, loc.getAttribute(COUNTRY).getValue().toString());
        dpCheck.checkTableInSyncByMissingAttribute(schemaTable, splittedOrCommon_entitiesView_Location, CITY);
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_Organization, org.getUri(), ORGANIZATION_NAME_FIELD, VALUE_FIELD, org.getAttribute(NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_Organization, org.getUri(), ORGANIZATION_STATUS_FIELD, VALUE_FIELD, org.getAttribute(STATUS).getValue().toString());
        dpCheck.checkTableInSyncByMissingAttribute(schemaTable, splittedOrCommon_entitiesView_Organization, PHONE);
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_HCO, hco.getUri(), HCO_NAME_FIELD, VALUE_FIELD, hco.getAttribute(NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_HCO, hco.getUri(), HCO_STATUS_FIELD, VALUE_FIELD, hco.getAttribute(STATUS).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_HCO, hco.getUri(), HCO_PROTOCOL_FIELD, VALUE_FIELD, hco.getAttribute(PROTOCOL).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_entitiesView_HCO, hco.getUri(), HCO_HOSPITALTYPE_FIELD, VALUE_FIELD, hco.getAttribute(HOSPITAL_TYPE).getValue().toString());
        dpCheck.checkTableInSyncByMissingAttribute(schemaTable, splittedOrCommon_entitiesView_HCO, PHONE);

        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_Email, email.getUri(), EMAIL_SUBJECT_FIELD, VALUE_FIELD, email.getAttribute(SUBJECT_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_Email, email.getUri(), EMAIL_LOCATION_FIELD, VALUE_FIELD, email.getAttribute(LOCATION_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_ExhibitionEvent, event.getUri(), EXHIBITIONEVENT_EXHBT_DT_FIELD, VALUE_FIELD, event.getAttribute(EXHBT_DT).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_ExhibitionEvent, event.getUri(), EXHIBITIONEVENT_EXHBT_NAME_FIELD, VALUE_FIELD, event.getAttribute(EXHBT_NAME).getValue().toString());

        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_relationsView_Family, family.getUri(), FAMILY_COMMENTERS_FIELD, VALUE_FIELD, family.getAttribute(COMMENTERS_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_relationsView_Sibling, sibling.getUri(), SIBLING_COMMENTERS_FIELD, VALUE_FIELD, sibling.getAttribute(COMMENTERS_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedField(splittedOrCommon_relationsView_HasAddress, address.getUri(), HASADDRESS_ADDRESS_TYPE_FIELD, VALUE_FIELD, address.getAttribute(ADDRESS_TYPE).getValue().toString());

        log.info("Step 3. Revert the config settings");
        tenantModel.getDataPipelineConfig().setDataFilteringEnabled(false);
        tenantModel = createTenant(tenantModel);
    }

    @TmsLink("RP-TC-12004")
    @Test(priority = 10, groups = {"smoke", "regression", "gbq4", "snowflake6"}, description = "Data pipeline: attributes flattening")
    public void runRpTc12004() throws Exception {
        log.info("Step 1.1. Update Business config with flattened attributes");
        tenantModel = getTenant();

        switch (dataPipelineAdapter) {
            case "GBQ": {
                tenantModel.getDataPipelineConfig().getBigqueryAdapterConfig().setFlattenAttribute(true);
                break;
            }
            case "SNOWFLAKE": {
                tenantModel.getDataPipelineConfig().getSnowflakeAdapterConfig().setAttributeFormat("FLATTEN_SINGLE_VALUE");
                break;
            }
            default: {
                throw new ApplicationGlobalException("Unknown data pipeline adapter: " + dataPipelineAdapter);
            }
        }

        tenantModel = createTenant(tenantModel);
        dps.createDataPipelineTablesViews(true, true);
        TenantManagementService.cleanTenant();
        sleep(60_000);
        generateDataInTenant();

        log.info("Step 1.2. Validate attributes have no extra fields in external table");
        dpCheck.checkTableInSyncBySchemaFieldCountIdField(schemaTable, splittedOrCommon_entitiesView_HCP, 0);
        dpCheck.checkTableInSyncBySchemaFieldCountIsOvField(schemaTable, splittedOrCommon_entitiesView_HCP, 0);
        dpCheck.checkTableInSyncBySchemaFieldCountIdField(schemaTable, splittedOrCommon_interactionsView_Email, 0);
        dpCheck.checkTableInSyncBySchemaFieldCountIsOvField(schemaTable, splittedOrCommon_interactionsView_Email, 0);
        dpCheck.checkTableInSyncBySchemaFieldCountIdField(schemaTable, splittedOrCommon_relationsView_HasAddress, 0);
        dpCheck.checkTableInSyncBySchemaFieldCountIsOvField(schemaTable, splittedOrCommon_relationsView_HasAddress, 0);

        log.info("Step 3.1. Verify flattened entities data exists in external table");
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCP, hcpList.get(0).getUri(), HCP_FIRST_NAME_FIELD, "", hcpList.get(0).getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCP, hcpList.get(2).getUri(), HCP_FIRST_NAME_FIELD, "", hcpList.get(2).getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCP, hcpList.get(4).getUri(), HCP_FIRST_NAME_FIELD, "", hcpList.get(4).getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCP, hcpList.get(6).getUri(), HCP_FIRST_NAME_FIELD, "", hcpList.get(6).getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCO, hcoList.get(0).getUri(), HCO_NAME_FIELD, "", hcoList.get(0).getAttribute(NAME_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCO, hcoList.get(1).getUri(), HCO_NAME_FIELD, "", hcoList.get(1).getAttribute(NAME_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCO, hcoList.get(2).getUri(), HCO_NAME_FIELD, "", hcoList.get(2).getAttribute(NAME_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_entitiesView_HCO, hcoList.get(3).getUri(), HCO_NAME_FIELD, "", hcoList.get(3).getAttribute(NAME_FIELD).getValue().toString());
        log.info("Step 3.2. Verify flattened relations data exists in external table");
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_relationsView_HasAddress, relationList.get(0).getUri(), HASADDRESS_ADDRESS_TYPE_FIELD, "", relationList.get(0).getAttribute(ADDRESS_TYPE).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_relationsView_HasAddress, relationList.get(1).getUri(), HASADDRESS_ADDRESS_TYPE_FIELD, "", relationList.get(1).getAttribute(ADDRESS_TYPE).getValue().toString());
        log.info("Step 3.3. Verify flattened interactions data exists in external table");
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_ExhibitionEvent, interactionList.get(0).getUri(), EXHIBITIONEVENT_EXHBT_NAME_FIELD, "", interactionList.get(0).getAttribute(EXHBT_NAME).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_Email, interactionList.get(1).getUri(), EMAIL_SUBJECT_FIELD, "", interactionList.get(1).getAttribute(SUBJECT_FIELD).getValue().toString());
        dpCheck.checkTableInSyncByNestedPlainField(splittedOrCommon_interactionsView_Email, interactionList.get(2).getUri(), EMAIL_SUBJECT_FIELD, "", interactionList.get(2).getAttribute(SUBJECT_FIELD).getValue().toString());
    }

    @TmsLink("RP-TC-12023")
    @Test(groups = {"smoke", "regression", "gbq3", "snowflake2", "deltalake1"}, description = "Data pipeline: merges and links")
    public void runRpTc12023() throws ReltioObjectException {
        log.info("Step 1.1. Create 3 entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME, LAST_NAME, MIDDLE_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME, LAST_NAME), null);
        EntityModel entity3 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.2. Merge entities");
        EntityModel merge32 = EntityService.mergeEntitiesWithWaiter(entity3, entity2);
        EntityModel merge21 = EntityService.mergeEntitiesWithWaiter(entity2, entity1);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.1. Validate only 1 entity exists in external table");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity1.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getInitialUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity3.getInitialUri(), 0);

        log.info("Step 2.2. Validate merges in external table");
        dpCheck.checkTableInSyncByCount(mergesView, MERGE_KEY_FIELD, entity1.getId(), 0);
        dpCheck.checkTableInSyncByCount(mergesView, MERGE_KEY_FIELD, entity2.getInitialId(), 1);
        dpCheck.checkTableInSyncByCount(mergesView, MERGE_KEY_FIELD, entity3.getInitialId(), 1);
        dpCheck.checkTableInSyncByMergeType(mergesView, WINNER_ID_FIELD, entity1.getId(), "MANUAL", 1);
        dpCheck.checkTableInSyncByMergeType(mergesView, WINNER_ID_FIELD, entity2.getInitialId(), "MANUAL", 1);
        dpCheck.checkTableInSyncByMergeType(mergesView, LOSER_ID_FIELD, entity2.getInitialId(), "MANUAL", 1);
        dpCheck.checkTableInSyncByMergeType(mergesView, LOSER_ID_FIELD, entity3.getInitialId(), "MANUAL", 1);

        log.info("Step 2.3. Validate links in external table");
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, entity1.getId(), 2);
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, entity2.getInitialId(), 0);
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, entity3.getInitialId(), 0);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, entity1.getId(), 0);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, entity2.getInitialId(), 1);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, entity3.getInitialId(), 1);
    }

    @TmsLink("RP-TC-12065")
    @Test(groups = {"smoke", "regression", "gbq4", "snowflake3", "deltalake2"}, description = "Data pipeline: activities table")
    public void runRpTc12065() throws CommandException, ReltioObjectException {
        log.info("Step 1. Set activityLogEnabled = true");
        tenantModel = getTenant();
        tenantModel.getDataPipelineConfig().setActivityLogEnabled(true);
        tenantModel = createTenant(tenantModel);

        log.info("Step 2.1. Create 3 entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME, LAST_NAME, MIDDLE_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME, LAST_NAME), null);
        EntityModel entity3 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        waitForObjectsInActivityLog(entity1, 1);
        waitForObjectsInActivityLog(entity2, 1);
        waitForObjectsInActivityLog(entity3, 1);

        log.info("Step 2.2. Validate records in activities table");
        ActivityLogModel activity1 = getActivities(entity1);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity1.get(0).getId(), entity1.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity1.get(0).getId(), entity1.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity1.get(0).getId(), String.valueOf(EventType.ENTITY_CREATED));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity1.get(0).getId(), entity1.getLabel());
        ActivityLogModel activity2 = getActivities(entity2);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity2.get(0).getId(), entity2.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity2.get(0).getId(), entity2.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity2.get(0).getId(), String.valueOf(EventType.ENTITY_CREATED));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity2.get(0).getId(), entity2.getLabel());
        ActivityLogModel activity3 = getActivities(entity3);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity3.get(0).getId(), entity3.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity3.get(0).getId(), entity3.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity3.get(0).getId(), String.valueOf(EventType.ENTITY_CREATED));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity3.get(0).getId(), entity3.getLabel());

        log.info("Step 3.1. Merge entities");
        EntityModel merge32 = EntityService.mergeEntitiesWithWaiter(entity3, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        waitForObjectsInActivityLog(entity2, 2);

        log.info("Step 3.2. Validate merge events in activities table");
        activity2 = getActivities(entity2, EventType.ENTITIES_MERGED_MANUALLY);
        assertEquals(activity2.getObjectsList().size(), 1);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity2.get(0).getId(), entity2.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity2.get(0).getId(), entity2.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity2.get(0).getId(), String.valueOf(EventType.ENTITIES_MERGED_MANUALLY));

        activity2 = getActivities(entity2, EventType.ENTITY_CHANGED);
        assertEquals(activity2.getObjectsList().size(), 1);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity2.get(0).getId(), entity2.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity2.get(0).getId(), entity2.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity2.get(0).getId(), String.valueOf(EventType.ENTITY_CHANGED));
        dpCheck.checkTableInSyncByActivitiesDeltaField(activitiesTable, ITEMS_FIELD, activity2.get(0).getId(), "configuration/entityTypes/HCP/attributes/FirstName");

        activity3 = getActivities(entity3, EventType.ENTITY_LOST_MERGE);
        assertEquals(activity3.getObjectsList().size(), 1);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity3.get(0).getId(), entity3.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity3.get(0).getId(), entity3.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity3.get(0).getId(), String.valueOf(EventType.ENTITY_LOST_MERGE));

        log.info("Step 4.1. Unmerge entities");
        Map<String, EntityModel> unmergedEntities = EntityService.unmergeEntitiesWithWaiter(entity3, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();
        waitForObjectsInActivityLog(entity2, 3);
        waitForObjectsInActivityLog(entity3, 3);

        log.info("Step 4.2. Validate unmerge events in activities table");
        activity2 = getActivities(entity2, EventType.NOT_MATCHES_SET);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity2.get(0).getId(), entity2.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity2.get(0).getId(), entity2.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity2.get(0).getId(), String.valueOf(EventType.NOT_MATCHES_SET));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity2.get(0).getId(), entity2.getLabel());

        activity2 = getActivities(entity2, EventType.ENTITY_CHANGED);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity2.get(0).getId(), entity2.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity2.get(0).getId(), entity2.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity2.get(0).getId(), String.valueOf(EventType.ENTITY_CHANGED));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity2.get(0).getId(), entity2.getLabel());
        dpCheck.checkTableInSyncByActivitiesDeltaField(activitiesTable, ITEMS_FIELD, activity2.get(0).getId(), "configuration/entityTypes/HCP/attributes/FirstName");

        activity2 = getActivities(entity2, EventType.ENTITIES_SPLITTED);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity2.get(0).getId(), entity2.getUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity2.get(0).getId(), entity2.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity2.get(0).getId(), String.valueOf(EventType.ENTITIES_SPLITTED));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity2.get(0).getId(), entity2.getLabel());

        activity3 = getActivities(entity3, EventType.NOT_MATCHES_SET);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity3.get(0).getId(), entity3.getInitialUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity3.get(0).getId(), entity3.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity3.get(0).getId(), String.valueOf(EventType.NOT_MATCHES_SET));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity3.get(0).getId(), entity3.getLabel());

        activity3 = getActivities(entity3, EventType.ENTITY_CREATED);
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTURI_FIELD, activity3.get(0).getId(), entity3.getInitialUri());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTTYPE_FIELD, activity3.get(0).getId(), entity3.getType());
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, EVENTTYPE_FIELD, activity3.get(0).getId(), String.valueOf(EventType.ENTITY_CREATED));
        dpCheck.checkTableInSyncByActivitiesField(activitiesTable, ITEMS_FIELD, OBJECTLABEL_FIELD, activity3.get(0).getId(), entity3.getLabel());
    }

    @TmsLink("RP-TC-12079")
    @Test(groups = {"smoke", "regression", "gbq4", "snowflake3"}, description = "Data Pipeline: Whether the sync task sends merges for deleted entities")
    public void runRpTc12079() throws ReltioObjectException {
        log.info("Step 1.1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 1.2. Merge entities");
        EntitiesHelper.mergeTwoEntities(entity1, entity2);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 1.3. Delete one of the entities");
        entity1.delete();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        // Set the query condition based on the data pipeline adapter
        String queryCondition = dataPipelineAdapter.equalsIgnoreCase("SNOWFLAKE")
                ? " AND \"active\"=FALSE"
                : "AND deleted = true";

        dpCheck.checkTableInSyncByCountWithCondition(IS_SPLIT_TABLE ? entitiesTable_HCP : entitiesTable, URI_FIELD,
                entity1.getUri(), queryCondition, 1);

        dps.createDataPipelineTablesViews(false, false);
        int rowCountActiveMerges = dpCheck.getRowCountAll(mergesView);
        log.info("Merge View row count before reindex = " + rowCountActiveMerges);
        int rowCountActiveLinks = dpCheck.getRowCountAll(linksView);
        log.info("Links View row count before reindex = " + rowCountActiveLinks);

        log.info("Step 2.1 Reindex tenant after delete entity");
        PostTaskRequestParameters params = new PostTaskRequestParameters()
                .tenantId(Config.getTenantId());
        syncToDataPipeline(params).getListOfTasks().parallelStream().forEach(t -> waitForTaskStatus(t, COMPLETED));

        int rowCountActiveMergeAfterReindexTask = dpCheck.getRowCountAll(mergesView);
        log.info("Merge view row count after reindex = " + rowCountActiveMergeAfterReindexTask);
        int rowCountActiveLinksAfterReindexTask = dpCheck.getRowCountAll(linksView);
        log.info("Links view row count after reindex = " + rowCountActiveLinksAfterReindexTask);

        assertEquals(rowCountActiveMerges, rowCountActiveMergeAfterReindexTask);
        assertEquals(rowCountActiveLinks, rowCountActiveLinksAfterReindexTask);
    }

    @TmsLink("RP-TC-12080")
    @Test(groups = {"smoke", "regression", "gbq4", "snowflake3"}, description = "Data Pipeline: Whether merges are affected by entity deletion")
    public void runRpTc12080() throws ReltioObjectException {
        log.info("Step 1.1. Create entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);

        log.info("Step 1.2. Merge entities and set winner");
        EntityService.mergeEntities(entity1, entity2, entity1);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        dps.createDataPipelineTablesViews(false, false);
        int rowCountActiveMerges = dpCheck.getRowCountAll(mergesView);
        log.info("Merge view row count before reindex = " + rowCountActiveMerges);
        int rowCountActiveLinks = dpCheck.getRowCountAll(linksView);
        log.info("Links view row count before reindex = " + rowCountActiveLinks);

        log.info("Step 2.1 Reindex tenant after delete entity");
        PostTaskRequestParameters params = new PostTaskRequestParameters()
                .tenantId(Config.getTenantId());

        syncToDataPipeline(params).getListOfTasks().parallelStream().forEach(t -> waitForTaskStatus(t, COMPLETED));

        log.info("Step 2.2. Delete entity which is winner");
        entity1.delete();

        dps.waitForEmptyQueueAndStagingBucketUpdated();

        int rowCountActiveMergeAfterReindexTask = dpCheck.getRowCountAll(mergesView);
        log.info("Merge view row count after reindex = " + rowCountActiveMergeAfterReindexTask);
        int rowCountActiveLinksAfterReindexTask = dpCheck.getRowCountAll(linksView);
        log.info("Links view row count after reindex = " + rowCountActiveLinksAfterReindexTask);
        assertEquals(rowCountActiveMerges, rowCountActiveMergeAfterReindexTask);
        assertEquals(rowCountActiveLinks, rowCountActiveLinksAfterReindexTask);
    }

    private void preconditionsForMerge() throws Exception {
        setDataFolder("resources:datapipeline/DataPipelineTests");
        updateMatchGroupsConfig("matchGroup_split.json");
        CleanseModel cleanseFunction = new CleanseModel()
                .cleanseFunction(CleanseHelper.LOQATE)
                .options(new CleanseOptionsModel()
                        .returnUnverifiedStatus(false)
                        .process(null)
                );

        TenantManagementService.setTenantParameters(t -> {
            t.putCleanse(cleanseFunction);
            t.getMatchingConfiguration().setResolveLookupStrategy(TenantModel.LookupStrategy.NONE);
            t.getMatchingConfiguration().setStrategy(MatchingStrategy.INCREMENTAL);
        });
        addVars();
        BusinessConfigurationService.updateUseInCleansing(false, ENTITY_TYPE_LOCATION);
    }

    private void updateMatchGroupsConfig(String matchGroup) throws Exception {
        setDataFolder("resources:datapipeline/DataPipelineTests");
        BusinessConfigModel busConf = BusinessConfigModel.fromJson(loadFile("config_jazz_v2.json"));
        busConf.getEntityTypeByUri(ENTITY_TYPE_HCP).setSingleDefaultCrosswalk(true);
        BusinessConfigurationService.updateMatchGroups(new GsonBuilder().create().toJson(busConf), ENTITY_TYPE_HCP, true,
                loadFile(matchGroup));
    }

    private void addVars() {
        setVar("CROSSWALK_TYPE1", CROSSWALK_TYPE1);
        setVar("CROSSWALK_TYPE2", CROSSWALK_TYPE2);
        setVar("CROSSWALK_TYPE3", CROSSWALK_TYPE3);
        setVar("POSTFIX", POSTFIX);
        setVar("USER", AccountService.getAdminAccount().getUsername());
        setVar("RND_VAL", "Rnd_" + String.valueOf(new Random().nextInt(100000)));
    }

}
