package com.reltio.qa.services.datapipeline;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.reltio.qa.Config;
import com.reltio.qa.GlobalConfig;
import com.reltio.qa.enums.common.DataPipelineGBQTables;
import com.reltio.qa.enums.common.Service;
import com.reltio.qa.exceptions.ApplicationGlobalException;
import com.reltio.qa.exceptions.CommandException;
import com.reltio.qa.model.TenantModel;
import com.reltio.qa.model.platform.config.phys.DataPipelineConfig;
import com.reltio.qa.request.Request;
import com.reltio.qa.services.DataPipelineService;
import com.reltio.qa.services.HttpService;
import com.reltio.qa.services.datapipeline.secrets.DeltalakeAwsSecretsProvider;
import com.reltio.qa.services.datapipeline.secrets.DeltalakeAzureSecretsProvider;
import com.reltio.qa.services.gcp.GCPCommon;
import com.reltio.qa.services.smartwaiting.SmartWaiting;
import lombok.extern.log4j.Log4j2;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.reltio.qa.Config.getTenantId;
import static com.reltio.qa.enums.common.EnableDisable.DISABLE;
import static com.reltio.qa.enums.common.EnableDisable.ENABLE;
import static com.reltio.qa.request.Request.Type.POST;
import static com.reltio.qa.services.EnvironmentService.isGCP;
import static com.reltio.qa.services.TenantManagementService.createTenant;
import static com.reltio.qa.services.TenantManagementService.getTenant;
import static com.reltio.qa.services.gcp.GBQConstants.*;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Properties;

@Log4j2
public class DataPipelineDeltalakeProvider extends DataPipelineService implements IDataPipelineProvider<DataPipelineConfig.DeltalakeAdapterConfig> {

    private static final Map<String, String> deltalakeTableMapping = new HashMap<>();

    static {
        deltalakeTableMapping.put(GBQ_TABLE_ENTITIES, "entities");
        deltalakeTableMapping.put(GBQ_TABLE_RELATIONS, "relations");
        deltalakeTableMapping.put(GBQ_TABLE_INTERACTIONS, "interactions");
        deltalakeTableMapping.put(GBQ_TABLE_MATCHES, "matches");
        deltalakeTableMapping.put(GBQ_TABLE_MERGES, "merges");
        deltalakeTableMapping.put(GBQ_TABLE_LINKS, "links");
        deltalakeTableMapping.put(GBQ_TABLE_ACTIVITIES, "activities");
        deltalakeTableMapping.put(GBQ_TABLE_COMPACTION_LOG, "");

        deltalakeTableMapping.put(GBQ_VIEW_ENTITIES_ID, "entities");
        deltalakeTableMapping.put(GBQ_VIEW_RELATIONS_ID, "relations");
        deltalakeTableMapping.put(GBQ_VIEW_INTERACTIONS_ID, "interactions");
        deltalakeTableMapping.put(GBQ_VIEW_MATCHES_ID, "matches");
        deltalakeTableMapping.put(GBQ_VIEW_MERGES_ID, "merges");
        deltalakeTableMapping.put(GBQ_VIEW_LINKS_ID, "links");
        deltalakeTableMapping.put(GBQ_VIEW_ACTIVITIES_ID, "activities");
    }

    private static final String DELTALAKE_DATAPIPELINE_ENDPOINT = "%s/api/tenants/%s/adapters/deltalake";
    private static final String DELTALAKE_SECRETS_ENDPOINT = "secrets";
    private static final String DELTALAKE_VALIDATION_ENDPOINT = "validate";
    private static final String DELTALAKE_TRIGGER_PIPELINE_ENDPOINT = "actions/trigger_pipeline";
    private static final String DELTALAKE_GET_PIPELINE_RUN_ENDPOINT = "actions/get_pipeline_run";
    private static final String DELTALAKE_DELETE_PIPELINE_ENDPOINT = "actions/delete_pipeline";
    private static final String ACTION_URL_TEMPLATE = "%s/%s";

    private static final String JDBC_DRIVER_NAME = "com.databricks.client.jdbc.Driver";
    private static final String JDBC_URL_PATTERN = "***********************************************************************************";
    private static final String DELTALAKE_WAREHOUSE_PATH = "/sql/1.0/warehouses/956e050efac018da";

    private DataPipelineConfig.DeltalakeAdapterConfig adapterConfig;
    private final String jdbcUrl;
    private Connection dbConnection;

    public DataPipelineDeltalakeProvider(DataPipelineConfig dpConfig, String cloudProvider) throws Exception {
        super("", isGCP() ? GCPCommon.getGCPCredentialsAsModel().getProjectId() : null, dpConfig.getMessagingName());

        this.adapterConfig = dpConfig.getDeltalakeAdapterConfig();
        String databricksHostRaw = GlobalConfig.getEnvProperty("DELTALAKE_DATABRICKS_HOST");
        if (databricksHostRaw == null || databricksHostRaw.isEmpty()) {
            throw new ApplicationGlobalException("DELTALAKE_DATABRICKS_HOST is null");
        }
        String databricksHostClean = databricksHostRaw;
        if (databricksHostClean.startsWith("https://")) {
            databricksHostClean = databricksHostClean.substring("https://".length());
        }
        if (databricksHostClean.endsWith("/")) {
            databricksHostClean = databricksHostClean.substring(0, databricksHostClean.length() - 1);
        }
        String httpPath = Config.getAsString("deltalakeHttpPath");
        if (httpPath == null || httpPath.isEmpty()) {
                httpPath = DELTALAKE_WAREHOUSE_PATH;
        }

        this.jdbcUrl = String.format(JDBC_URL_PATTERN, databricksHostClean, httpPath);

        try {
             Class.forName(JDBC_DRIVER_NAME);
        } catch (ClassNotFoundException e) {
            throw new ApplicationGlobalException("Databricks JDBC driver not found: " + JDBC_DRIVER_NAME, e);
        }

        this.dbConnection = getDbConnection();

        String dataPipelineEndpoint = String.format(DELTALAKE_DATAPIPELINE_ENDPOINT, Config.getUrl(Service.DATAPIPELINEHUB), getTenantId());
        setSecretsEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DELTALAKE_SECRETS_ENDPOINT));
        setValidateEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DELTALAKE_VALIDATION_ENDPOINT));
        setActionsEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DPH_ACTIONS_ENDPOINT));
        setConfigureEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DPH_CONFIGURE_ENDPOINT));
        setTriggerPipelineEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DELTALAKE_TRIGGER_PIPELINE_ENDPOINT));
        setGetPipelineRunEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DELTALAKE_GET_PIPELINE_RUN_ENDPOINT));
        setDeletePipelineEndpoint(String.format(ACTION_URL_TEMPLATE, dataPipelineEndpoint, DELTALAKE_DELETE_PIPELINE_ENDPOINT));

        this.secretsProvider = ("AZURE").equalsIgnoreCase(cloudProvider)
                ? new DeltalakeAzureSecretsProvider(getSecretsEndpoint()) : new DeltalakeAwsSecretsProvider(getSecretsEndpoint());

    }

    @Override
    public TenantModel prepareAdapterConfig(TenantModel preparedModel, DataPipelineConfig.DeltalakeAdapterConfig otherConfig, boolean newState) {
        TenantModel localModel = null;
        try {
            localModel = (preparedModel == null) ? getTenant() : preparedModel;
            DataPipelineConfig.DeltalakeAdapterConfig saConfig = (preparedModel == null) ? adapterConfig : localModel.getDataPipelineConfig().getDeltalakeAdapterConfig();
            if (saConfig == null) {
                saConfig = new DataPipelineConfig.DeltalakeAdapterConfig();
            }
            saConfig.setEnabled(newState);
            localModel.getDataPipelineConfig().getAdapters().clear();
            localModel.getDataPipelineConfig().getAdapters().add(saConfig);
        } catch (UnsupportedOperationException | ClassCastException | IllegalArgumentException e) {
            log.error("Exception when preparing adapter config", e);
            throw new ApplicationGlobalException("Could not prepare Deltalake config due to " + e, e);
        }
        return localModel;
    }

    @Override
    public TenantModel prepareAdapterConfigAllParams(TenantModel preparedModel, DataPipelineConfig.DeltalakeAdapterConfig otherConfig, boolean newState) {
        return prepareAdapterConfig(preparedModel, otherConfig, newState);
    }

    @Override
    public TenantModel setAdapterConfig(String tenantId, DataPipelineConfig.DeltalakeAdapterConfig
            otherConfig, boolean newState) {
        String action = String.valueOf(newState ? ENABLE : DISABLE);
        log.info("{} Deltalake adapter for tenant {}", action, tenantId);
        tenantModel = prepareAdapterConfig(null, otherConfig, newState);
        tenantModel = createTenant(tenantModel);
        return tenantModel;
    }

    @Override
    public boolean createDataProvider(DataPipelineConfig.DeltalakeAdapterConfig otherConfig) {
        return false;
    }

    @Override
    public boolean createDataset(DataPipelineConfig.DeltalakeAdapterConfig otherConfig) {
        return false;
    }

    @Override
    public boolean deleteDataPipelineTablesViews() {
//        try {
//            DeltalakeHelper.pipelineDeletion(this);
//
//            String dropSchemaSQL = String.format("DROP SCHEMA IF EXISTS `%s` CASCADE", getSchemaName());
//            executeCommand(dropSchemaSQL);

            return true;
//        } catch (Exception e) {
//            log.warn("Failed to delete Deltalake resources: {}", e.getMessage(), e);
//            return false;
//        }
    }

    @Override
    public boolean deleteDataPipelineTablesViews(DataPipelineGBQTables table) {
        return false;
    }

    @Override
    public boolean createDataPipelineTablesViews(Boolean force) {
        try {
            SmartWaiting.waitForValue(() -> validate().getAsJsonObject().get("statusCode").getAsInt(), 200, "Waiting for validate to succeed");

            JsonObject triggerBody = new JsonObject();
            triggerBody.addProperty("fullRefresh", Boolean.TRUE.equals(force));

            DeltalakeHelper.configureTriggerAndWaitForRun(this, triggerBody);
            return true;
        } catch (Exception e) {
            log.warn("Failed to create Deltalake tables and views: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean createDataPipelineTablesViews(Boolean force, Boolean checkSuccess) {
        return createDataPipelineTablesViews(force);
    }

    @Override
    public boolean createDataPipelineTablesViews(Boolean force, String tableName, Boolean checkSuccess) {
        return false;
    }

    @Override
    public List<String> getNonExistingTables(DataPipelineGBQTables... tables) {
        return Collections.emptyList();
    }

    @Override
    public String getFullTableName(String tableId) {
        String deltalakeTableName = deltalakeTableMapping.get(tableId);

        if (getSchemaName() != null) {
            return getSchemaName() + "." + deltalakeTableName;
        }
        return deltalakeTableName;
    }

    @Override
    public String getShortTableName(String tableId) {
        return deltalakeTableMapping.get(tableId);
    }

    @Override
    public String truncateTable(String tableId) {
        return null;
    }

    @Override
    public JsonElement compactTables(Boolean checkSuccess) {
        return null;
    }

    @Override
    public JsonElement actions() {
        return null;
    }

    @Override
    public JsonElement validate() {
        try {
            CloseableHttpResponse response = new Request(null, POST, getValidateEndpoint()).getResponse(false, false);
            String result = HttpService.getContentNoCheck(response);
            return result.isEmpty() ?
                    JsonParser.parseString(String.format("{\"statusCode\": %s}", response.getStatusLine().getStatusCode())) :
                    JsonParser.parseString(result);
        } catch (CommandException e) {
            log.error("Validate adapter request failed: {}", e.getMessage());
            return new JsonObject();
        }
    }

    public JsonElement configurePipeline() {
        try {
            CloseableHttpResponse response = new Request(null, POST, getConfigureEndpoint()).getResponse(false, false);
            String result = HttpService.getContentNoCheck(response);
            return result.isEmpty() ?
                    JsonParser.parseString(String.format("{\"statusCode\": %s}", response.getStatusLine().getStatusCode())) :
                    JsonParser.parseString(result);
        } catch (CommandException e) {
            log.error("Configure pipeline request failed: {}", e.getMessage());
            return new JsonObject();
        }
    }

    public JsonElement putSecrets(String databricksToken, String cloudToken, int retries) {
        return secretsProvider.putSecrets("", databricksToken, "", cloudToken, "", "", retries);
    }

    @Override
    public JsonElement putSecrets(String userName, String databricksToken, String privateKey, String cloudToken,
                                  int retries) {
        return putSecrets(databricksToken, cloudToken, retries);
    }

    @Override
    public JsonElement putSecrets(String userName, String databricksToken, String privateKey, String cloudToken,
                                  String accessKey, String secretKey, int retries) {
        return putSecrets(databricksToken, cloudToken, retries);
    }

    @Override
    public JsonElement putDefaultSecrets(int retries) {
        if (secretsProvider instanceof DeltalakeAwsSecretsProvider) {
            TenantModel tenantModel = getTenant();
            DataPipelineConfig.DeltalakeAdapterConfig currentAdapterConfig = tenantModel.getDataPipelineConfig().getDeltalakeAdapterConfig();
            DataPipelineConfig.DeltalakeAdapterConfig.AwsCloudProviderConfig awsConfig = currentAdapterConfig.getAwsConfig();

            prepareAdapterConfig(tenantModel, currentAdapterConfig.setAwsCloudProviderConfig(
                    awsConfig.setAwsCredentials(awsConfig.getAwsCredentials().setExternalId(GlobalConfig.getEnvProperty("DELTALAKE_DATABRICKS_AWS_EXTERNALID")))
            ), true);

            createTenant(tenantModel);
        }

        return putSecrets(getDefaultDbcToken(), getDefaultCloudSecret(), retries);
    }

    @Override
    public JsonElement deleteSecrets() {
        return secretsProvider.deleteSecrets();
    }

    public String getSchemaName() {
        if (adapterConfig != null) {
            return adapterConfig.getSchemaName();
        }
        return null;
    }

    @Override
    public void waitForEmptyQueueAndStagingBucketUpdated() {
        SmartWaiting.waitForEmptyEventQueue();

        TenantModel tenantModel = getTenant();
        if (tenantModel.getDataPipelineConfig().getEnabled() &&
                tenantModel.getDataPipelineConfig().getDeltalakeAdapterConfig() != null &&
                tenantModel.getDataPipelineConfig().getDeltalakeAdapterConfig().getEnabled()) {

            log.info("Data Pipeline enabled, configuring and triggering pipeline to ingest new data");

            DeltalakeHelper.configureTriggerAndWaitForRun(this, null);
            log.info("Pipeline run completed successfully");
        } else {
            log.info("Data Pipeline is disabled, skipping pipeline configuration and triggering");
        }

    }

    @Override
    public void cleanupResourcesBeforeTests() {
        deleteDataPipelineTablesViews();
    }

    @Override
    public String getDefaultUserName() {
        return null;
    }

    @Override
    public String getDefaultPrivateKey() {
        return null;
    }

    @Override
    public String getDefaultDbcToken() {
        return GlobalConfig.getEnvProperty("DELTALAKE_DATABRICKS_TOKEN");
    }

    @Override
    public String getDefaultCloudSecret() {
        if (secretsProvider instanceof DeltalakeAzureSecretsProvider) {
            return GlobalConfig.getEnvProperty("DELTALAKE_AZURE_SAS_TOKEN");
        }
        return "";
    }

    @Override
    public String createAssumeRole() {
        return null;
    }

    @Override
    public JsonElement updateAssumeRole(String customerRole) {
        return null;
    }

    @Override
    public JsonElement deleteAssumeRole() {
        return null;
    }


    /**
     * Executes a SQL COUNT query against the Databricks database
     */
    public long executeCountQuery(String tableName, String whereClause, Object... parameters) {
        StringBuilder sqlBuilder = new StringBuilder("SELECT COUNT(*) FROM `")
                .append(tableName.replace(".", "`.`"))
                .append('`');

        if (whereClause != null && !whereClause.isEmpty()) {
            sqlBuilder.append(" WHERE ").append(whereClause);
        }

        String sql = sqlBuilder.toString();

        log.info("Executing count query: {}", createLogSql(sql, parameters));

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) { //NOSONAR

            if (parameters != null) {
                for (int i = 0; i < parameters.length; i++) {
                    stmt.setObject(i + 1, parameters[i]);
                }
            }

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                } else {
                    log.error("Could not retrieve count from query: {}", sql);
                    return -1;
                }
            }
        } catch (SQLException e) {
            log.error("SQL Error executing count query [{}]: {}", sql, e.getMessage(), e);
            throw new ApplicationGlobalException("Failed to execute count query on Databricks", e);
        }
    }


    /**
     * Executes a SQL query against the Databricks database
     */
    public JsonElement executeQuery(String sql, Object... parameters) {
        JsonArray results = new JsonArray();

        log.info("Executing SQL query: {}", createLogSql(sql, parameters));

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) { //NOSONAR

            if (parameters != null) {
                for (int i = 0; i < parameters.length; i++) {
                    stmt.setObject(i + 1, parameters[i]);
                }
            }

            try (ResultSet rs = stmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (rs.next()) {
                    JsonObject row = new JsonObject();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = rs.getObject(i);

                        if (value == null) {
                            row.add(columnName, null);
                        } else if (value instanceof Number number) {
                            row.addProperty(columnName, number);
                        } else if (value instanceof Boolean bool) {
                            row.addProperty(columnName, bool);
                        } else {
                            row.addProperty(columnName, value.toString());
                        }
                    }
                    results.add(row);
                }
            }
        } catch (SQLException e) {
            log.error("SQL Error executing query [{}]: {}", sql, e.getMessage(), e);
            throw new ApplicationGlobalException("Failed to execute query on Databricks", e);
        }
        return results;
    }


    /**
     * Executes a SQL command against the Databricks database
     */
    public void executeCommand(String sql, Object... parameters) {
        log.info("Executing SQL command: {}", createLogSql(sql, parameters));

        try (PreparedStatement stmt = dbConnection.prepareStatement(sql)) { //NOSONAR

            if (parameters != null) {
                for (int i = 0; i < parameters.length; i++) {
                    stmt.setObject(i + 1, parameters[i]);
                }
            }

            stmt.execute();
        } catch (SQLException e) {
            log.error("SQL Error executing command [{}]: {}", sql, e.getMessage(), e);
            throw new ApplicationGlobalException("Failed to execute command on Databricks", e);
        }
    }

    private Connection getDbConnection() throws SQLException {
         Properties properties = new Properties();
         properties.put("UID", "token");
         properties.put("PWD", getDefaultDbcToken());

         log.info("Attempting to connect to Databricks JDBC URL: {}", this.jdbcUrl);
         Connection conn = DriverManager.getConnection(this.jdbcUrl, properties);
         log.info("Successfully connected to Databricks.");
         return conn;
    }

    /**
     * Creates a loggable version of a SQL statement with parameters
     */
    private static String createLogSql(String sql, Object... parameters) {
        if (parameters == null || parameters.length == 0) {
            return sql;
        }
        StringBuilder result = new StringBuilder(sql);
        int paramIndex = parameters.length - 1;
        int pos = result.lastIndexOf("?");

        while (pos >= 0 && paramIndex >= 0) {
            Object param = parameters[paramIndex];
            String replacement;
            if (param == null) {
                replacement = "NULL";
            } else if (param instanceof String str) {
                replacement = "'" + str + "'";
            } else {
                replacement = param.toString();
            }

            result.replace(pos, pos + 1, replacement);
            paramIndex--;
            pos = result.lastIndexOf("?", pos - 1);
        }
        return result.toString();
    }

    public JsonElement triggerPipeline(JsonElement body) throws CommandException {
        Request request = new Request(null, POST, getTriggerPipelineEndpoint());
        if (body != null) {
            String jsonBodyString = body.toString();
            StringEntity requestEntity = new StringEntity(
                jsonBodyString,
                ContentType.APPLICATION_JSON);
            request.setBody(requestEntity);
        }
        return request.executeJson();
    }

    public JsonElement getPipelineRun() throws CommandException {
        return new Request(null, POST, getGetPipelineRunEndpoint()).executeJson();
    }


    public JsonElement deletePipeline() {
        try {
            return new Request(null, POST, getDeletePipelineEndpoint()).executeJson();
        } catch (CommandException e) {
            log.warn("Delete pipeline request failed: {}", e.getMessage());
            return new JsonObject();
        }
    }

}
