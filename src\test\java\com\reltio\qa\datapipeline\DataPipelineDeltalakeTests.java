package com.reltio.qa.datapipeline;

import java.util.List;

import com.google.gson.JsonElement;
import com.reltio.qa.Config;
import com.reltio.qa.exceptions.ApplicationGlobalException;
import com.reltio.qa.helpers.datapipeline.EntitiesHelper;
import com.reltio.qa.model.*;
import com.reltio.qa.model.BusinessModel.BusinessConfigModel;
import com.reltio.qa.model.Interactions.InteractionMemberModel;
import com.reltio.qa.model.Interactions.InteractionModel;
import com.reltio.qa.model.Interactions.InteractionSubMember;
import com.reltio.qa.model.platform.config.phys.DataPipelineConfig;
import com.reltio.qa.model.platform.config.phys.DataPipelineConfig.DeltalakeAdapterConfig;
import com.reltio.qa.services.AccountService;
import com.reltio.qa.services.BusinessConfigurationService;
import com.reltio.qa.services.datapipeline.DataPipelineDeltalakeProvider;
import com.reltio.qa.utils.RandomGenerator;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.SkipException;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static com.reltio.qa.helpers.CommonStrings.*;
import static com.reltio.qa.model.TaskModel.Status.COMPLETED;
import static com.reltio.qa.services.TaskService.syncToDataPipeline;
import static com.reltio.qa.services.TenantManagementService.createTenant;
import static com.reltio.qa.services.TenantManagementService.getTenant;
import static com.reltio.qa.services.datapipeline.DeltalakeHelper.*;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForValue;
import static com.reltio.qa.services.smartwaiting.platform.SmartWaitingPlatformPeriodicTasks.waitForTaskStatus;
import static com.reltio.qa.utils.TimeUtils.sleep;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@Log4j2
public class DataPipelineDeltalakeTests extends DataPipelineBase {

    @BeforeClass(alwaysRun = true)
    protected void beforeDataPipelineDeltalakeTests() throws Exception {
        AccountService.setDefaultAccountName(AccountService.ADMIN_NAME);  //TODO remove this line and set explicitly account name in all tests. NB! Admin account will not be allowed as a default account in the future. RP-151889

        if (dps == null) {
            beforeDataPipelineBaseTest("DELTALAKE");
        }
        log.info("beforeDataPipelineDeltalakeTests: Adapter = " + dataPipelineAdapter);
        try {
            setDataFolder("resources:api");
            waitForValue(() -> BusinessConfigurationService.putBusinessConfig(Config.getTenantId(),
                            BusinessConfigModel.fromJson(loadFile("config/Config/L3-config-wscore.json"))).getUri(),
                    "configuration", "Wait for business config is successfully loaded");
            precondition();
        } catch (Exception e) {
            log.error("beforeDataPipelineDeltalakeTests() failed! Tests will be skipped: " + e);
            throw new SkipException("beforeDataPipelineDeltalakeTests() failed! Tests will be skipped.", e);
        }
    }

    protected void precondition() throws Exception {
        log.info("Precondition 1. Enable Streaming");
        dps.setStreamingConfig(queueName, true, true);

        log.info("Precondition 2. Enable Deltalake adapter");
        prepareDeltaLakeAdapterConfig(true);

        log.info("Precondition 3. Enable Data pipeline");
        dps.setDataPipelineState(queueName, true);
        log.info("Wait for DataPipeline is enabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getEnabled(), true);
    }

    @TmsLink("RP-TC-12058")
    @Test(groups = {"smoke", "regression", "deltalake", "pipeline-deltalake"}, description = "Data pipeline: verify Deltalake disable/enable with validation")
    public void runRpTc12058() {
        log.info("Step 1. Disable Data pipeline: Deltalake Adapter");
        dps.setAdapterConfig(Config.getTenantId(), getTenant().getDataPipelineConfig().getDeltalakeAdapterConfig(), false);

        log.info("Wait for Deltalake adapter is disabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getDeltalakeAdapterConfig().getEnabled(), false);


        log.info("Step2. Execute validate adapter request. Validation should fail with \"Adapter deltalake not enabled for tenant\"");
        waitForValue(() -> validateAdapter(dps.validate(), "errorMessage", String.format("Adapter deltalake not enabled for tenant %s.",
                Config.getTenantId()), "Adapter validation errorMessage is incorrect"), true);

        log.info("Step 3. Disable Data pipeline config");
        tenantModel = TenantModel.fromJson(dps.setDataPipelineState(queueName, false));

        log.info("Wait for Datapipeline config is disabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getEnabled(), false);

        log.info("Step 4. Execute validate adapter request. Validation should fail with \"No supported adapter deltalake found for tenant\"");
        waitForValue(() -> validateAdapter(dps.validate(), "errorMessage", String.format("No supported adapter deltalake found for tenant %s.",
                Config.getTenantId()), "Adapter validation errorMessage is incorrect"), true);

        log.info("Step 5. Set Deltalake Adapter config");

        dps.prepareAdapterConfig(tenantModel, tenantModel.getDataPipelineConfig().getDeltalakeAdapterConfig(), true);
        tenantModel = createTenant(tenantModel);

        log.info("Wait for Deltalake adapter is enabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getDeltalakeAdapterConfig().getEnabled(), true);

        log.info("Step 6. Enable Data pipeline config");
        dps.setDataPipelineState(queueName, true);
        waitForValue(() -> getTenant().getDataPipelineConfig().getEnabled(), true);

    }

    @TmsLink("RP-TC-12114")
    @Test(groups = {"smoke", "regression", "deltalake", "pipeline-deltalake"}, description = "Deltalake adapter: Databricks configuration with AWS cloudProvider")
    public void runRpTc12114() {
        if (!"AWS".equals(DELTALAKE_CLOUD_PROVIDER)) {
            throw new SkipException("Test will be skipped.The test can be run with AWS cloud provider only!\"");
        }
        log.info("Step 1. Set Deltalake Adapter config");
        tenantModel = dps.prepareAdapterConfig(null, dpConfig.getDeltalakeAdapterConfig(), true);
        tenantModel = createTenant(tenantModel);

        log.info("Wait for Deltalake adapter is enabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getDeltalakeAdapterConfig().getEnabled(), true);

        log.info("Step 2. Enable Data pipeline config");
        dps.setDataPipelineState(queueName, true);
        waitForValue(() -> getTenant().getDataPipelineConfig().getEnabled(), true);

        log.info("Step 3. Execute validate adapter request w/o externalId in awsCredentials. Validation should fail");

        waitForValue(() -> validateAdapter(dps.validate(), "errorMessage", String.format("Validation failed for adapter deltalake and tenant %s.",
                Config.getTenantId()), "Adapter validation errorMessage is incorrect"), true);

        log.info("Step 4. Set externalId in awsCredentials");

        tenantModel = getTenant();
        DataPipelineConfig.DeltalakeAdapterConfig adapterConfig = tenantModel.getDataPipelineConfig().getDeltalakeAdapterConfig();
        DeltalakeAdapterConfig.AwsCloudProviderConfig adapterAwsConfig = adapterConfig.getAwsConfig();

        dps.prepareAdapterConfig(tenantModel, adapterConfig.setAwsCloudProviderConfig(
                adapterAwsConfig.setAwsCredentials(adapterAwsConfig.getAwsCredentials().setExternalId(DELTALAKE_DATABRICKS_AWS_EXTERNALID)
                )), true);
        createTenant(tenantModel);

        log.info("Step 5. Set deltalake secret with Databricks token only");
        dps.putSecrets("", DELTALAKE_DATABRICKS_TOKEN, "", null, 5);

        log.info("Step 6. Execute validation request. Validation should pass");

        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200", "Adapter validate request statusCode is not 200"), true);

    }

    @TmsLink("RP-TC-12115")
    @Test(groups = {"smoke", "regression", "deltalake", "pipeline-deltalake"}, description = "Deltalake adapter: Databricks configuration with Azure cloudProvider")
    public void runRpTc12115() throws Exception {
        if (!"AZURE".equals(DELTALAKE_CLOUD_PROVIDER)) {
            DataPipelineDeltalakeProvider deltalakeProvider = (DataPipelineDeltalakeProvider) dps;
            pipelineDeletion(deltalakeProvider);

            log.warn("Cloud provider will be changed!");
            DELTALAKE_CLOUD_PROVIDER = "AZURE";
            setDeltalakeAdapterConfigAzure();
            // Stabilization - wait for provider resources cleanup after cloud switch
            sleep(60000);
            dps = new DataPipelineDeltalakeProvider(dpConfig, DELTALAKE_CLOUD_PROVIDER);
            prepareDeltaLakeAdapterConfig(true);

        }
        log.info("Step 1. Set Deltalake Adapter config");
        tenantModel = dps.prepareAdapterConfig(null, dpConfig.getDeltalakeAdapterConfig(), true);
        tenantModel = createTenant(tenantModel);

        log.info("Wait for Deltalake adapter is enabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getDeltalakeAdapterConfig().getEnabled(), true);

        log.info("Step 2. Enable Data pipeline config");
        dps.setDataPipelineState(queueName, true);
        waitForValue(() -> getTenant().getDataPipelineConfig().getEnabled(), true);

        log.info("Step 3. Execute validate adapter request w/o databricks token. Validation should fail");

        waitForValue(() -> validateAdapter(dps.validate(), "errorMessage", String.format("Secret not found for adapter deltalake and tenant %s.",
                Config.getTenantId()), "Adapter validation errorMessage is incorrect"), true);

        log.info("Step 4. Set deltalake secret with Databricks token only");
        dps.putSecrets("", DELTALAKE_DATABRICKS_TOKEN, "", null, 5);

        log.info("Step 5. Execute validation request. Validation should fail");
        waitForValue(() -> validateAdapter(dps.validate(), "errorMessage", String.format("Secret not found for adapter deltalake and tenant %s.",
                Config.getTenantId()), "Adapter validation errorMessage is incorrect"), true);

        log.info("Step 6. Set deltalake secret with Azure sasToken");
        dps.putSecrets("", DELTALAKE_DATABRICKS_TOKEN, "", DELTALAKE_AZURE_SECRET, 5);

        log.info("Step 7. Execute validation request. Validation should pass");
        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200", "Adapter validate request statusCode is not 200"), true);
    }

    @TmsLink("RP-TC-13647")
    @Test(groups = {"regression", "deltalake", "pipeline-deltalake"}, description = "Deltalake adapter: verify row counts at landing table")
    public void runRpTc13647() throws Exception {
        if (!"AWS".equals(DELTALAKE_CLOUD_PROVIDER))
        {
            log.info("Changing Cloud Provider to AWS");
            DELTALAKE_CLOUD_PROVIDER = "AWS";
            setDeltaLakeAdapterAWS();
            dps = new DataPipelineDeltalakeProvider(dpConfig, DELTALAKE_CLOUD_PROVIDER);
            prepareDeltaLakeAdapterConfig(true);
        }
        precondition();
        log.info("Step 1: Deltalake connector setup is handled by preconditions.");
        assertNotNull(dps, "DataPipelineDeltalakeProvider should be initialized");

        log.info("Step 2: Add secrets and validate adapter connection.");
        String databricksToken = DELTALAKE_DATABRICKS_TOKEN;

        if ("AZURE".equals(DELTALAKE_CLOUD_PROVIDER)) {
            dps.putSecrets("", databricksToken, "", DELTALAKE_AZURE_SECRET, 5);
        } else if ("AWS".equals(DELTALAKE_CLOUD_PROVIDER)) {
            TenantModel currentTenant = getTenant();
            DataPipelineConfig.DeltalakeAdapterConfig adapterConfig = currentTenant.getDataPipelineConfig().getDeltalakeAdapterConfig();

            if (adapterConfig != null && adapterConfig.getAwsConfig() != null && adapterConfig.getAwsConfig().getAwsCredentials() != null &&
                    adapterConfig.getAwsConfig().getAwsCredentials().getExternalId() == null && DELTALAKE_DATABRICKS_AWS_EXTERNALID != null) {

                DataPipelineConfig.DeltalakeAdapterConfig.AwsCloudProviderConfig adapterAwsConfig = adapterConfig.getAwsConfig();

                dps.prepareAdapterConfig(currentTenant, adapterConfig.setAwsCloudProviderConfig(
                        adapterAwsConfig.setAwsCredentials(adapterAwsConfig.getAwsCredentials().setExternalId(DELTALAKE_DATABRICKS_AWS_EXTERNALID))
                ), true);

                createTenant(currentTenant);
                waitForValue(() -> {
                    TenantModel updatedTenant = getTenant();
                    return updatedTenant.getDataPipelineConfig().getDeltalakeAdapterConfig().getAwsConfig().getAwsCredentials().getExternalId() != null;
                }, Boolean.TRUE, "External ID should be set in tenant config");
            }
            dps.putSecrets("", databricksToken, "", null, 5);

        } else {
            throw new SkipException("Unsupported DELTALAKE_CLOUD_PROVIDER: " + DELTALAKE_CLOUD_PROVIDER);
        }

        log.info("Validate adapter connection");
        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200", "Adapter validate request statusCode should be 200"),
                Boolean.TRUE, "Adapter validation must succeed after adding secrets.");

        log.info("Step 3: Add one entity in tenant");
        EntityModel createdEntity = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        assertNotNull(createdEntity, "Entity should be created successfully via helper.");
        assertNotNull(createdEntity.getUri(), "Created entity should have a URI.");

        log.info("Step 4: Sync data to datapipeline");
        PostTaskRequestParameters params = new PostTaskRequestParameters().tenantId(Config.getTenantId());
        syncToDataPipeline(params).getListOfTasks().parallelStream().forEach(task -> waitForTaskStatus(task, COMPLETED));

        log.info("Trigger pipeline and wait for completion");
        DataPipelineDeltalakeProvider deltalakeProvider = (DataPipelineDeltalakeProvider) dps;
        configureTriggerAndWaitForRun(deltalakeProvider, null);

        log.info("Step 5: Verify content in landing table at databricks");
        String landingTableName = "entities_landingtable";
        String fullLandingTableName = datasetName + "." + landingTableName;
        waitForValue(() -> dpCheck.getRowCount(fullLandingTableName, "uri", createdEntity.getUri()) >= 1,
                Boolean.TRUE,
                String.format("Entity URI '%s' should exist in Databricks table '%s'", createdEntity.getUri(), fullLandingTableName)
        );

        log.info("Step 6: Verify content in entities table at databricks");
        String entitiesTableName = "entities";
        String fullEntitiesTableName = datasetName + "." + entitiesTableName;
        int expectedEntityCount = 1;
        waitForValue(() -> dpCheck.getRowCount(fullEntitiesTableName, "uri", createdEntity.getUri()),
                expectedEntityCount,
                String.format("Row count in Databricks entities table '%s' for filter [uri=%s] should become %d",
                        fullEntitiesTableName, createdEntity.getUri(), expectedEntityCount)

        );
    }

    @TmsLink("RP-TC-13783")
    @Test(groups = {"regression", "deltalake", "pipeline-deltalake"}, description = "Deltalake adapter: interactions data")
    public void runRpTc13783() throws Exception {
        log.info("Step 1: Add secrets and validate adapter connection");
        String databricksToken = DELTALAKE_DATABRICKS_TOKEN;

        if ("AZURE".equals(DELTALAKE_CLOUD_PROVIDER)) {
            dps.putSecrets("", databricksToken, "", DELTALAKE_AZURE_SECRET, 5);
        } else if ("AWS".equals(DELTALAKE_CLOUD_PROVIDER)) {
            TenantModel currentTenant = getTenant();
            DataPipelineConfig.DeltalakeAdapterConfig adapterConfig = currentTenant.getDataPipelineConfig().getDeltalakeAdapterConfig();

            if (adapterConfig != null && adapterConfig.getAwsConfig() != null && adapterConfig.getAwsConfig().getAwsCredentials() != null &&
                    adapterConfig.getAwsConfig().getAwsCredentials().getExternalId() == null && DELTALAKE_DATABRICKS_AWS_EXTERNALID != null) {

                DataPipelineConfig.DeltalakeAdapterConfig.AwsCloudProviderConfig adapterAwsConfig = adapterConfig.getAwsConfig();

                dps.prepareAdapterConfig(currentTenant, adapterConfig.setAwsCloudProviderConfig(
                        adapterAwsConfig.setAwsCredentials(adapterAwsConfig.getAwsCredentials().setExternalId(DELTALAKE_DATABRICKS_AWS_EXTERNALID))
                ), true);

                createTenant(currentTenant);
                waitForValue(() -> {
                    TenantModel updatedTenant = getTenant();
                    return updatedTenant.getDataPipelineConfig().getDeltalakeAdapterConfig().getAwsConfig().getAwsCredentials().getExternalId() != null;
                }, Boolean.TRUE, "External ID should be set in tenant config");
            }
            dps.putSecrets("", databricksToken, "", null, 5);

        } else {
            throw new SkipException("Unsupported DELTALAKE_CLOUD_PROVIDER: " + DELTALAKE_CLOUD_PROVIDER);
        }

        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200", "Adapter validate request statusCode should be 200"),
                Boolean.TRUE, "Adapter validation must succeed after adding secrets.");

        log.info("Step 2: Create entities");
        EntityModel hcp = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel hco = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);

        log.info("Step 3.1: Create interactions");
        // Create EMAIL interaction
        InteractionModel interaction = new InteractionModel(INTERACTION_TYPE_EMAIL)
                .addMember("From", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/From")
                        .addSubMember(new InteractionSubMember(hcp.getUri())));
        interaction.addMember("To", new InteractionMemberModel(INTERACTION_TYPE_EMAIL + "/memberTypes/To")
                .addSubMember(new InteractionSubMember(hco.getUri())));
        interaction.post();

        // Create EXHIBITION interaction
        InteractionModel interaction2 = new InteractionModel(INTERACTION_TYPE_EXHIBITION_EVENT);
        interaction2.addAttributeToModel("EXHBT_NAME", new SimpleAttributeModel(RandomGenerator.getInstance().getRandomString(10)));
        interaction2.addAttributeToModel("EXHBT_GLOBAL", new SimpleAttributeModel("false"));
        interaction2.addMember("HCP", new InteractionMemberModel(ENTITY_TYPE_HCP)
                .addSubMember(new InteractionSubMember(hcp.getUri())));
        interaction2.addMember("HCO", new InteractionMemberModel(ENTITY_TYPE_HCO)
                .addSubMember(new InteractionSubMember(hco.getUri())));
        interaction2.post();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2: Validate EMAIL interaction exists in external view");
        dpCheck.checkTableInSyncByField(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction.getUri());
        dpCheck.checkTableInSyncByEmailFromField(splittedOrCommon_interactionsView_Email, interaction.getUri(), hcp.getId());
        dpCheck.checkTableInSyncByEmailToField(splittedOrCommon_interactionsView_Email, interaction.getUri(), hco.getId());

        log.info("Step 3.3: Validate EXHIBITION interaction exists in external view");
        dpCheck.checkTableInSyncByField(splittedOrCommon_interactionsView_ExhibitionEvent, URI_FIELD, interaction2.getUri());
        dpCheck.checkTableInSyncByEventHcpField(splittedOrCommon_interactionsView_ExhibitionEvent, interaction2.getUri(), hcp.getId());
        dpCheck.checkTableInSyncByEventHcoField(splittedOrCommon_interactionsView_ExhibitionEvent, interaction2.getUri(), hco.getId());

        log.info("Step 4.1: Delete interactions");
        interaction.delete();
        interaction2.delete();
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 4.2: Validate deleted interaction exists in external table");
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? interactionsTable_Email : interactionsTable, URI_FIELD, interaction.getUri(), 1);
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? interactionsTable_ExhibitionEvent : interactionsTable, URI_FIELD, interaction2.getUri(), 1);

        log.info("Step 4.3: Validate interaction were deleted from external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_Email, URI_FIELD, interaction.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_interactionsView_ExhibitionEvent, URI_FIELD, interaction2.getUri(), 0);
    }

    @TmsLink("RP-TC-13784")
    @Test(groups = {"regression", "deltalake", "pipeline-deltalake"}, description = "Deltalake adapter: create and delete entity")
    public void runRpTc13784() throws Exception {
        log.info("Step 1: Add secrets and validate adapter connection");
        String databricksToken = DELTALAKE_DATABRICKS_TOKEN;

        if ("AZURE".equals(DELTALAKE_CLOUD_PROVIDER)) {
            dps.putSecrets("", databricksToken, "", DELTALAKE_AZURE_SECRET, 5);
        } else if ("AWS".equals(DELTALAKE_CLOUD_PROVIDER)) {
            TenantModel currentTenant = getTenant();
            DataPipelineConfig.DeltalakeAdapterConfig adapterConfig = currentTenant.getDataPipelineConfig().getDeltalakeAdapterConfig();

            if (adapterConfig != null && adapterConfig.getAwsConfig() != null && adapterConfig.getAwsConfig().getAwsCredentials() != null &&
                    adapterConfig.getAwsConfig().getAwsCredentials().getExternalId() == null && DELTALAKE_DATABRICKS_AWS_EXTERNALID != null) {

                DataPipelineConfig.DeltalakeAdapterConfig.AwsCloudProviderConfig adapterAwsConfig = adapterConfig.getAwsConfig();

                dps.prepareAdapterConfig(currentTenant, adapterConfig.setAwsCloudProviderConfig(
                        adapterAwsConfig.setAwsCredentials(adapterAwsConfig.getAwsCredentials().setExternalId(DELTALAKE_DATABRICKS_AWS_EXTERNALID))
                ), true);

                createTenant(currentTenant);
                waitForValue(() -> {
                    TenantModel updatedTenant = getTenant();
                    return updatedTenant.getDataPipelineConfig().getDeltalakeAdapterConfig().getAwsConfig().getAwsCredentials().getExternalId() != null;
                }, Boolean.TRUE, "External ID should be set in tenant config");
            }
            dps.putSecrets("", databricksToken, "", null, 5);

        } else {
            throw new SkipException("Unsupported DELTALAKE_CLOUD_PROVIDER: " + DELTALAKE_CLOUD_PROVIDER);
        }

        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200", "Deltalake adapter validation should return statusCode 200 after adding secrets"),
                Boolean.TRUE, "Deltalake adapter validation must succeed after adding secrets and configuring credentials.");

        log.info("Step 2.1: Create two entities");
        EntityModel entity1 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        EntityModel entity2 = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2: Validate entities exist in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity1.getUri(), 1);
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity1.getUri(), entity1.getAttribute(FIRST_NAME).getValue().toString());
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), 1);
        dpCheck.checkTableInSyncByHcpFirstNameField(splittedOrCommon_entitiesView_HCP, entity2.getUri(), entity2.getAttribute(FIRST_NAME).getValue().toString());

        log.info("Step 2.3: Validate entity attributes");
        dpCheck.checkTableInSyncByCrosswalksUriFieldPartialMatch(splittedOrCommon_entitiesView_HCP, entity1.getUri(), entity1.getUri() + "/crosswalks/");
        dpCheck.checkTableInSyncByCrosswalksSourceFieldPartialMatch(splittedOrCommon_entitiesView_HCP, entity1.getUri(), "configuration/sources/");
        dpCheck.checkTableInSyncByCrosswalksRefEntityField(splittedOrCommon_entitiesView_HCP, entity1.getUri());
        dpCheck.checkTableInSyncByCrosswalksUriFieldPartialMatch(splittedOrCommon_entitiesView_HCP, entity2.getUri(), entity2.getUri() + "/crosswalks/");
        dpCheck.checkTableInSyncByCrosswalksSourceFieldPartialMatch(splittedOrCommon_entitiesView_HCP, entity2.getUri(), "configuration/sources/");
        dpCheck.checkTableInSyncByCrosswalksRefEntityField(splittedOrCommon_entitiesView_HCP, entity2.getUri());

        log.info("Step 3.1. Delete both entities");
        entity1.delete(true);
        entity2.delete(true);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2. Validate deleted entities exist in landing table");
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? entitiesTable_HCP : entitiesTable, URI_FIELD, entity1.getUri(), 1);
        dpCheck.checkTableInSyncByCountWithDeletedTrue(IS_LEGACY_OR_SPLITTED ? entitiesTable_HCP : entitiesTable, URI_FIELD, entity2.getUri(), 1);

        log.info("Step 3.3. Validate deleted entities don't exist in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity1.getUri(), 0);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, entity2.getUri(), 0);
    }

    @TmsLink("RP-TC-13785")
    @Test(groups = {"regression", "deltalake", "pipeline-deltalake"}, description = "Deltalake adapter - links data")
    public void runRpTc13785() throws Exception {
        log.info("Step 1: Add secrets and validate");
        String databricksToken = DELTALAKE_DATABRICKS_TOKEN;

        if ("AZURE".equals(DELTALAKE_CLOUD_PROVIDER)) {
            dps.putSecrets("", databricksToken, "", DELTALAKE_AZURE_SECRET, 5);
        } else if ("AWS".equals(DELTALAKE_CLOUD_PROVIDER)) {
            TenantModel currentTenant = getTenant();
            DataPipelineConfig.DeltalakeAdapterConfig adapterConfig = currentTenant.getDataPipelineConfig().getDeltalakeAdapterConfig();

            if (adapterConfig != null && adapterConfig.getAwsConfig() != null && adapterConfig.getAwsConfig().getAwsCredentials() != null &&
                    adapterConfig.getAwsConfig().getAwsCredentials().getExternalId() == null && DELTALAKE_DATABRICKS_AWS_EXTERNALID != null) {

                DataPipelineConfig.DeltalakeAdapterConfig.AwsCloudProviderConfig adapterAwsConfig = adapterConfig.getAwsConfig();

                dps.prepareAdapterConfig(currentTenant, adapterConfig.setAwsCloudProviderConfig(
                        adapterAwsConfig.setAwsCredentials(adapterAwsConfig.getAwsCredentials().setExternalId(DELTALAKE_DATABRICKS_AWS_EXTERNALID))
                ), true);

                createTenant(currentTenant);
                waitForValue(() -> {
                    TenantModel updatedTenant = getTenant();
                    return updatedTenant.getDataPipelineConfig().getDeltalakeAdapterConfig().getAwsConfig().getAwsCredentials().getExternalId() != null;
                }, Boolean.TRUE, "External ID should be set in tenant config");
            }
            dps.putSecrets("", databricksToken, "", null, 5);

        } else {
            throw new SkipException("Unsupported DELTALAKE_CLOUD_PROVIDER: " + DELTALAKE_CLOUD_PROVIDER);
        }

        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200", "Adapter validate request statusCode should be 200"),
                Boolean.TRUE, "Adapter validation must succeed after adding secrets.");

        log.info("Step 2.1: Create 2 entities: HCO and HCP");
        EntityModel hco = EntitiesHelper.createEntity(ENTITY_TYPE_HCO, List.of(FIRST_NAME), null);
        EntityModel hcp = EntitiesHelper.createEntity(ENTITY_TYPE_HCP, List.of(FIRST_NAME), null);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 2.2: Validate entities exist in external view");
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCO, URI_FIELD, hco.getUri(), 1);
        dpCheck.checkTableInSyncByCount(splittedOrCommon_entitiesView_HCP, URI_FIELD, hcp.getUri(), 1);

        log.info("Step 3.1: Create links data");
        EntityModel merge = EntityService.mergeEntitiesWithWaiter(hcp, hco);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 3.2: Validate links data exist in external view");
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, hco.getId(), 1);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, hcp.getInitialId(), 1);

        log.info("Step 4.1: Delete links data");
        EntityService.unmergeEntitiesWithWaiter(hcp, hco);
        dps.waitForEmptyQueueAndStagingBucketUpdated();

        log.info("Step 4.2: Validate deleted links exist in external table");
        dpCheck.checkTableInSyncByCountWithDeletedTrue(linksTable, WINNER_ID_FIELD, hco.getId(), 1);
        dpCheck.checkTableInSyncByCountWithDeletedTrue(linksTable, LOSER_ID_FIELD, hcp.getInitialId(), 1);

        log.info("Step 4.3: Validate links do not exist in external view");
        dpCheck.checkTableInSyncByCount(linksView, WINNER_ID_FIELD, hco.getId(), 0);
        dpCheck.checkTableInSyncByCount(linksView, LOSER_ID_FIELD, hcp.getInitialId(), 0);
    }

    private static boolean validateAdapter(JsonElement validationResponse, String jsonField, String jsonValue, String errorMessage) {
        assertNotNull(validationResponse.getAsJsonObject(), "Adapter validate request result is null");
        try {
            if (jsonField.equals("statusCode") && validationResponse.getAsJsonObject().has("status")) {
                return "SUCCESS".equals(validationResponse.getAsJsonObject().get("status").getAsString());
            }
            assertEquals(validationResponse.getAsJsonObject().get(jsonField).getAsString(), jsonValue, errorMessage);
            return true;
        } catch (NullPointerException e) {
            log.error(String.format("NPE happen when parsing validation response. Check the response: \n{}", validationResponse));
            throw new ApplicationGlobalException(e.getMessage());
        } catch (AssertionError e) {
            log.error(e.getMessage());
            return false;
        }
    }

    @TmsLink("RP-TC-13758")
    @Test(groups = {"smoke", "regression", "deltalake", "pipeline-deltalake"}, description = "DeltaLake adapter: configure pipeline with Azure credentials")
    public void runRpTc13758() throws Exception {
        if (!"AZURE".equals(DELTALAKE_CLOUD_PROVIDER)) {
            log.warn("Cloud provider will be changed!");
            DELTALAKE_CLOUD_PROVIDER = "AZURE";
            setDeltalakeAdapterConfigAzure();
            dps = new DataPipelineDeltalakeProvider(dpConfig, DELTALAKE_CLOUD_PROVIDER);
            prepareDeltaLakeAdapterConfig(true);
        }

        log.info("Step 1. Set Deltalake Adapter config with Azure ExternalId");
        tenantModel = dps.prepareAdapterConfig(null, dpConfig.getDeltalakeAdapterConfig(), true);
        tenantModel = createTenant(tenantModel);

        log.info("Wait for Deltalake adapter to be enabled in tenant's config");
        waitForValue(() -> getTenant().getDataPipelineConfig().getDeltalakeAdapterConfig().getEnabled(), true);
        waitForValue(() -> getTenant().getDataPipelineConfig().getEnabled(), true);

        log.info("Step 2. Execute validate adapter request");
        dps.putSecrets("", DELTALAKE_DATABRICKS_TOKEN, "", DELTALAKE_AZURE_SECRET, 5);
        waitForValue(() -> validateAdapter(dps.validate(), "statusCode", "200",
                "Adapter validate request statusCode is not 200"), true);

        log.info("Step 3. Configure pipeline and verify response");
        DataPipelineDeltalakeProvider deltalakeProvider = (DataPipelineDeltalakeProvider) dps;
        waitForValue(() -> validateAdapter(deltalakeProvider.configurePipeline(),
                "statusCode", "200", "Pipeline configure request statusCode is not 200"), true);
    }
}
