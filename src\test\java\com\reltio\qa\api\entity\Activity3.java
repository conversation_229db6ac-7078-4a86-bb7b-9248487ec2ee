package com.reltio.qa.api.entity;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.reltio.qa.model.*;
import com.reltio.qa.service.retry.RetryAnalyzerWithClean;
import com.reltio.qa.utils.GsonUtils;
import io.qameta.allure.TmsLink;
import lombok.extern.log4j.Log4j2;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Objects;

import static com.reltio.qa.services.AccountService.ADMIN_NAME;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForConsistency;
import static com.reltio.qa.services.smartwaiting.SmartWaiting.waitForEmptyMatchEventsQueue;
import static org.testng.Assert.*;

@Log4j2
public class Activity3 extends ActivityBase {

    @TmsLink("RP-TC-782")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete reference attribute. One String attribute. One crosswalk")
    public void runRpTc0782() throws Exception {
        int expectedSize = 2;
        generateNewState();
        UnqNum = generateNewUnqNum(10);
        EntityModel eHCP = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCP15.json")).post();
        EntityModel eLoc = new EntityModel(loadFile("resources:api/entity/ActivityBase/Location15.json")).post();
        waitForConsistency(eHCP, eLoc);
        postRelation(loadFile("resources:api/entity/ActivityBase/Relation15C.json"), ADMIN_NAME);
        waitForEmptyMatchEventsQueue();
        eHCP.get().getAttribute("Address").getAsReference().getRelation().get().getAttribute
                ("AddressRank").getAsSimple().delete(new CrosswalkModel("configuration/sources/AMS",
                parseString("location.t1{{UnqNum}}.r1")), ACTIVITY_ID);

        String response = waitForItemsSize(expectedSize, "'RELATIONSHIP_CHANGED' x 1, 'ENTITY_CHANGED' x 1", ACTIVITY_ID);
        String delta_changedE = "";
        JsonElement deltaCollectionR = null;
        JsonElement deltaCollectionE = null;

        JsonArray array = getItemsArrayFromStringModel(response);
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("deltaCollection")) {
                if (item.getAsJsonObject().get("data").getAsJsonObject().get("type").toString().equals("\"RELATIONSHIP_CHANGED\"")) {
                    deltaCollectionR = GsonUtils.getByPath(item, "deltaCollection");
                } else {
                    if (item.getAsJsonObject().get("data").getAsJsonObject().get("type").toString().equals("\"ENTITY_CHANGED\"")) {
                        deltaCollectionE = GsonUtils.getByPath(item, "deltaCollection");
                    }
                }
            }
        }

        array = deltaCollectionE.getAsJsonObject().get("delta").getAsJsonArray();
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("type")) {
                if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_CHANGED\"")) {
                    delta_changedE = item.toString();
                }
            }
        }

        assertTrue(deltaCollectionR.toString().startsWith("{\"ovChanged\":true"),
                "Delta for ATTRIBUTE_CHANGED (relation) is not expected");
        assertTrue(delta_changedE.startsWith("{\"type\":\"ATTRIBUTE_CHANGED\",\"attributeType\":\"configuration/entityTypes/HCP/attributes/Address\",\"newValue\""),
                "Delta for ATTRIBUTE_CHANGED (entity) is not expected");
    }

    @TmsLink("RP-TC-785")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete reference attribute. One String attribute. Few crosswalks")
    public void runRpTc0785() throws Exception {
        int expectedSize = 2;
        generateNewState();
        generateNewUnqNum(10);
        generateNewSource();

        EntityModel eHCP = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCP15.json")).post();
        EntityModel eHCO = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCO15.json")).post();
        EntityModel eLoc = new EntityModel(loadFile("resources:api/entity/ActivityBase/Location15.json")).post();
        waitForConsistency(eHCO, eHCP, eLoc);
        postRelation(loadFile("resources:api/entity/ActivityBase/Relation15A.json"), ADMIN_NAME);
        RelationModel rLoc = new RelationModel(postRelation(loadFile("resources:api/entity/ActivityBase/Relation15D.json"), ADMIN_NAME));
        rLoc.get().postAttribute("AddressRank", "[{\"value\":\"10\"}]");
        rLoc.get().getAttribute("AddressRank").getAsSimple().delete(ACTIVITY_ID);
        waitForEmptyMatchEventsQueue();

        String response = waitForItemsSize(expectedSize, "'RELATIONSHIP_CHANGED' x 1, 'ENTITY_CHANGED' x 1", ACTIVITY_ID);
        String delta_changedE = "";
        String delta_removedR = "";

        JsonElement deltaCollectionR = null;
        JsonElement deltaCollectionE = null;

        JsonArray array = getItemsArrayFromStringModel(response);
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("deltaCollection")) {
                if (item.getAsJsonObject().get("data").getAsJsonObject().get("type").toString().equals("\"RELATIONSHIP_CHANGED\"")) {
                    deltaCollectionR = GsonUtils.getByPath(item, "deltaCollection");
                } else {
                    if (item.getAsJsonObject().get("data").getAsJsonObject().get("type").toString().equals("\"ENTITY_CHANGED\"")) {
                        deltaCollectionE = GsonUtils.getByPath(item, "deltaCollection");
                    }
                }
            }
        }

        array = deltaCollectionR.getAsJsonObject().get("delta").getAsJsonArray();
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("type")) {
                if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_REMOVED\"")) {
                    delta_removedR = item.toString();
                }
            }
        }

        array = deltaCollectionE.getAsJsonObject().get("delta").getAsJsonArray();
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("type")) {
                if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_CHANGED\"")) {
                    delta_changedE = item.toString();
                }
            }
        }

        assertTrue(delta_removedR.startsWith("{\"type\":\"ATTRIBUTE_REMOVED\",\"attributeType\":\"configuration/relationTypes/HasAddress/attributes/AddressRank\",\"oldValue\":{\"value\":\"10\""),
                "Delta for ATTRIBUTE_REMOVED (relation) is not expected");
        assertTrue(delta_changedE.startsWith("{\"type\":\"ATTRIBUTE_CHANGED\",\"attributeType\":\"configuration/entityTypes/HCP/attributes/Address\",\"newValue\""),
                "Delta for ATTRIBUTE_CHANGED (entity) is not expected");
    }

    @TmsLink("RP-TC-788")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete reference attribute. One relation attribute. Few crosswalks")
    public void runRpTc0788() throws Exception {
        int expectedSize = 2;
        generateNewState();
        generateNewUnqNum(10);
        generateNewSource();

        EntityModel eHCP = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCP15.json")).post();
        EntityModel eHCO = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCO15.json")).post();
        EntityModel eLoc = new EntityModel(loadFile("resources:api/entity/ActivityBase/Location15.json")).post();
        waitForConsistency(eHCO, eHCP, eLoc);
        postRelation(loadFile("resources:api/entity/ActivityBase/Relation15A.json"), ADMIN_NAME);
        postRelation(loadFile("resources:api/entity/ActivityBase/Relation15B.json"), ADMIN_NAME);
        postRelation(loadFile("resources:api/entity/ActivityBase/Relation15C.json"), ADMIN_NAME);
        waitForEmptyMatchEventsQueue();

        eLoc.get().getAttribute("AddressLine1").getAsSimple().delete(ACTIVITY_ID);
        waitForEmptyMatchEventsQueue();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CHANGED' x 2", ACTIVITY_ID);
        String delta_changedH = "";
        String delta_changedL = "";
        String delta_removedH = "";
        String delta_removedL = "";

        JsonElement deltaCollectionH = null;
        JsonElement deltaCollectionL = null;

        JsonArray array = getItemsArrayFromStringModel(response);
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("deltaCollection")) {
                if (item.getAsJsonObject().get("objectType").toString().equals("\"configuration/entityTypes/HCP\"")) {
                    deltaCollectionH = GsonUtils.getByPath(item, "deltaCollection");
                } else {
                    if (item.getAsJsonObject().get("objectType").toString().equals("\"configuration/entityTypes/Location\"")) {
                        deltaCollectionL = GsonUtils.getByPath(item, "deltaCollection");
                    }
                }
            }
        }

        array = deltaCollectionH.getAsJsonObject().get("delta").getAsJsonArray();
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("type")) {
                if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_CHANGED\"")) {
                    delta_changedH = item.toString();
                } else {
                    if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_REMOVED\"")) {
                        delta_removedH = item.toString();
                    }
                }
            }
        }
        array = deltaCollectionL.getAsJsonObject().get("delta").getAsJsonArray();
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("type")) {
                if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_CHANGED\"")) {
                    delta_changedL = item.toString();
                } else {
                    if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_REMOVED\"")) {
                        delta_removedL = item.toString();
                    }
                }
            }
        }

        assertEquals(delta_removedH, delta_removedL, "Delta for ATTRIBUTE_REMOVED is not expected");
        assertEquals(delta_changedH, delta_changedL, "Delta for ATTRIBUTE_CHANGED is not expected");
    }

    @TmsLink("RP-TC-791")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete reference attribute. one relation attribute. one crosswalk")
    public void runRpTc0791() throws Exception {
        int expectedSize = 2;
        generateNewState();
        generateNewUnqNum(10);
        generateNewSource();

        EntityModel eHCP = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCP15.json")).post();
        EntityModel eHCO = new EntityModel(loadFile("resources:api/entity/ActivityBase/HCO15.json")).post();
        EntityModel eLoc = new EntityModel(loadFile("resources:api/entity/ActivityBase/Location15.json")).post();
        waitForConsistency(eHCO, eHCP, eLoc);
        postRelation(loadFile("resources:api/entity/ActivityBase/Relation15A.json"), ADMIN_NAME);
        RelationModel rLoc = new RelationModel(postRelation(loadFile("resources:api/entity/ActivityBase/Relation15D.json"), ADMIN_NAME));
        waitForEmptyMatchEventsQueue();

        rLoc.get().delete(ACTIVITY_ID);

        String response = waitForItemsSize(expectedSize, "'RELATIONSHIP_REMOVED' x 1, 'ENTITY_CHANGED' x 1", ACTIVITY_ID);
        JsonArray array = getItemsArrayFromStringModel(response);

        String delta_removedR = "";
        String delta_removedH = "";

        JsonElement deltaCollectionH = null;


        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("deltaCollection")) {
                if (item.getAsJsonObject().get("objectType").toString().equals("\"configuration/entityTypes/HCP\"")) {
                    deltaCollectionH = GsonUtils.getByPath(item, "deltaCollection");
                }
            }
            if (item.getAsJsonObject().has("data")) {
                if (item.getAsJsonObject().get("objectType").toString().equals("\"configuration/relationTypes/HasAddress\"")) {
                    delta_removedR = GsonUtils.getByPath(item, "data").toString();
                }
            }
        }
        array = deltaCollectionH.getAsJsonObject().get("delta").getAsJsonArray();
        for (int i = 0; i < array.size(); i++) {
            JsonElement item = array.get(i);
            if (item.getAsJsonObject().has("type")) {
                if (item.getAsJsonObject().get("type").toString().equals("\"ATTRIBUTE_REMOVED\"")) {
                    delta_removedH = item.toString();
                }
            }
        }

        JsonElement jo = JsonParser.parseString(delta_removedH);
        JsonElement jo2 = JsonParser.parseString(delta_removedR);
        assertEquals(jo.getAsJsonObject().get("type").getAsString(), "ATTRIBUTE_REMOVED");
        assertEquals(jo.getAsJsonObject().get("attributeType").getAsString(), "configuration/entityTypes/HCP/attributes/Address");
        assertTrue(jo.getAsJsonObject().get("oldValue").getAsJsonObject().get("ov").getAsBoolean());
        assertEquals(jo2.getAsJsonObject().get("type").getAsString(), "RELATIONSHIP_REMOVED");
    }

    @TmsLink("RP-TC-721")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple string attribute, one crosswalk")
    public void runRpTc0721() throws Exception {
        String simpleAttributeName = "LastName";
        String simpleAttributeValue = "LastName";
        int expectedSize = 1;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();
        e1.getAttribute(simpleAttributeName).getAsSimple().delete(ACTIVITY_ID);

        String response = waitForItemsSize(expectedSize, "'ENTITY_CHANGED' x 1", ACTIVITY_ID);
        JsonObject item = JsonParser.parseString(response).getAsJsonObject().get("items").getAsJsonArray().get(0).getAsJsonObject();
        assertEquals(GsonUtils.getByPath(item, "data/type").getAsString(), "ENTITY_CHANGED");
        assertEquals(GsonUtils.getByPath(item, "deltaCollection/delta/0/type").getAsString(), "ATTRIBUTE_REMOVED");
        assertNotNull(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue"));
        assertEquals(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue/value").getAsString(), simpleAttributeValue);
        assertTrue(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue/ov").getAsBoolean());
    }

    @TmsLink("RP-TC-748")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple Boolean attribute. Few crosswalks")
    public void runRpTc0748() throws Exception {
        String simpleAttributeName = "MNComplianceFlag";
        String simpleAttributeValue = "true";
        int expectedSize = 5;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();
        e1.updateOrPostSimpleAttribute(simpleAttributeName, simpleAttributeValue, null);
        e1.refresh();
        e1.getAttribute(simpleAttributeName).getAsSimple().delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 2");
        for (int i = 0; i < expectedSize; i++) {
            JsonObject item = JsonParser.parseString(response).getAsJsonObject().get("items").getAsJsonArray().get(i).getAsJsonObject();
            if (GsonUtils.getByPath(item, "data/type").getAsString().equals("ENTITY_CHANGED")) {
                if (GsonUtils.getByPath(item, "deltaCollection/delta/0/type").getAsString().equals("ATTRIBUTE_REMOVED")) {
                    assertNotNull(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue"));
                    assertTrue(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue/value").getAsBoolean());
                    assertTrue(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue/ov").getAsBoolean());
                    break;
                }
            }
        }
    }

    @TmsLink("RP-TC-739")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple Date attribute, one crosswalk")
    public void runRpTc0739() throws Exception {
        String simpleAttributeName = "DoD";
        String simpleAttributeValue = "2015-06-01";
        int expectedSize = 5;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();
        e1.updateOrPostSimpleAttribute(simpleAttributeName, simpleAttributeValue, null);
        e1.refresh();

        e1.getAttribute(simpleAttributeName).getAsSimple().delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 2");

        for (int i = 0; i < expectedSize; i++) {
            JsonObject item = JsonParser.parseString(response).getAsJsonObject().get("items").getAsJsonArray().get(i).getAsJsonObject();
            if (GsonUtils.getByPath(item, "data/type").getAsString().equals("ENTITY_CHANGED")) {
                if (GsonUtils.getByPath(item, "deltaCollection/delta/0/type").getAsString().equals("ATTRIBUTE_REMOVED")) {
                    assertNotNull(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue"));
                    assertEquals(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue/value").getAsString(), simpleAttributeValue);
                    assertTrue(GsonUtils.getByPath(item, "deltaCollection/delta/0/oldValue/ov").getAsBoolean());
                    break;
                }
            }
        }
    }

    @TmsLink("RP-TC-735")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple String attribute, few crosswalks")
    public void runRpTc0735() throws Exception {
        String simpleAttributeName = "LastName";
        String simpleAttributeValue = "LastName";
        String simpleAttributeValue2 = "LastName2";
        int expectedSize = 6;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();
        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute = new SimpleAttributeModel(simpleAttributeValue2);
        e1.postAttribute(simpleAttributeName, e1.getCrosswalks().get(0), simpleAttribute.getPostBody(), null);
        e1.refresh();

        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue).delete();
        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue2).delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 3");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue),
                "Activity details are wrong");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue2),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-726")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple Int attribute, one crosswalk")
    public void runRpTc0726() throws Exception {
        String SimpleAttributeName = "YoB";
        String SimpleAttributeValue = "2100";
        String SimpleAttributeValue2 = "3100";
        int expectedSize = 7;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();
        waitForConsistency(e1);
        waitForConsistency(new EntityModel(e1.getAllRefEntities().get(0).getObjectURI()).get());

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute = new SimpleAttributeModel(SimpleAttributeValue);
        SimpleAttributeModel simpleAttribute2 = new SimpleAttributeModel(SimpleAttributeValue2);
        e1.postAttribute(SimpleAttributeName, e1.getCrosswalks().get(0), simpleAttribute.getPostBody(), null);
        e1.postAttribute(SimpleAttributeName, e1.getCrosswalks().get(0), simpleAttribute2.getPostBody(), null);
        e1.refresh();
        waitForConsistency(e1);

        findSimpleAttributeByValue(e1, SimpleAttributeName, SimpleAttributeValue).delete();
        findSimpleAttributeByValue(e1, SimpleAttributeName, SimpleAttributeValue2).delete();
        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 4");

        ActivityRecordModel activity = getActivityModelByString(response);
        Assert.assertEquals(activity.getItems().stream().filter(i -> i.getData().getType().equals("ENTITY_CREATED")).count(), 2);
        Assert.assertEquals(activity.getItems().stream().filter(i -> i.getData().getType().equals("ENTITY_CHANGED")).count(), 4);
        Assert.assertEquals(activity.getItems().stream().filter(i -> i.getData().getType().equals("RELATIONSHIP_CREATED")).count(), 1);
        ActivityItemModel item = activity.getItems().stream().filter(i -> i.getData().getType().equals("ENTITY_CHANGED")).findAny().get();
        Assert.assertEquals(item.getDeltaCollection().getDeltas().size(), 1);
        Assert.assertEquals(item.getDeltaCollection().getDeltas().get(0).getType(), "ATTRIBUTE_ADDED");
    }

    @TmsLink("RP-TC-729")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple Boolean attribute, one crosswalk")
    public void runRpTc0729() throws Exception {
        String SimpleAttributeName = "MNComplianceFlag";
        String SimpleAttributeValue = "true";
        String SimpleAttributeValue2 = "false";
        int expectedSize = 7;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute = new SimpleAttributeModel(SimpleAttributeValue);
        SimpleAttributeModel simpleAttribute2 = new SimpleAttributeModel(SimpleAttributeValue2);
        e1.postAttribute(SimpleAttributeName, e1.getCrosswalks().get(0), simpleAttribute.getPostBody(), null);
        e1.postAttribute(SimpleAttributeName, e1.getCrosswalks().get(0), simpleAttribute2.getPostBody(), null);
        e1.refresh();
        findSimpleAttributeByValue(e1, SimpleAttributeName, SimpleAttributeValue).delete();
        findSimpleAttributeByValue(e1, SimpleAttributeName, SimpleAttributeValue2).delete();
        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 4");

        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, SimpleAttributeValue),
                "Activity details are wrong");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, SimpleAttributeValue2),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-742")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple Int attribute. few crosswalks")
    public void runRpTc0742() throws Exception {
        String simpleAttributeName = "YoB";
        String simpleAttributeValue = "2000";
        String simpleAttributeValue2 = "2100";
        int expectedSize = 8;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        CrosswalkModel crosswalk2 = new CrosswalkModel("Reltio", "Reltio-CROSSWALK-" + UnqNum);

        e1.getCrosswalks().add(crosswalk2);
        e1.post();//this change will modify existing relation by changing start/end related entity crosswalks

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue);
        e1.postAttribute(simpleAttributeName, crosswalk1, simpleAttribute1.getPostBody(), null);
        e1.refresh();

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute2 = new SimpleAttributeModel(simpleAttributeValue2);
        e1.postAttribute(simpleAttributeName, crosswalk2, simpleAttribute2.getPostBody(), null);
        e1.refresh();

        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue).delete();
        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue2).delete();
        String response = waitForItemsSize(
                expectedSize,
                "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 4, 'RELATIONSHIP_CHANGED' x 1"
        );

        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue),
                "Activity details are wrong");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue2),
                "Activity details are wrong");

    }

    @TmsLink("RP-TC-745")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete simple Date attribute. Few crosswalks")
    public void runRpTc0745() throws Exception {
        String simpleAttributeName = "DoD";
        String simpleAttributeValue = "2015-06-01";
        String simpleAttributeValue2 = "2015-07-01";
        int expectedSize = 8;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        CrosswalkModel crosswalk2 = new CrosswalkModel("Reltio", "Reltio-CROSSWALK-" + UnqNum);

        e1.getCrosswalks().add(crosswalk2);
        e1.post();//this change will modify existing relation by changing start/end related entity crosswalks
        waitForConsistency(e1);

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue);
        e1.postAttribute(simpleAttributeName, crosswalk1, simpleAttribute1.getPostBody(), null);
        e1.refresh();

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute2 = new SimpleAttributeModel(simpleAttributeValue2);
        e1.postAttribute(simpleAttributeName, crosswalk2, simpleAttribute2.getPostBody(), null);
        e1.refresh();

        SimpleAttributeModel attr1 = findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue);
        Assert.assertNotNull(attr1, "Failed to find simple attribute with value: " + simpleAttributeValue);
        attr1.delete();
        SimpleAttributeModel attr2 = findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue2);
        Assert.assertNotNull(attr2, "Failed to find simple attribute with value: " + simpleAttributeValue2);
        attr2.delete();

        String response = waitForItemsSize(
                expectedSize,
                "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 4, 'RELATIONSHIP_CHANGED' x 1"
        );
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue),
                "Activity details are wrong");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue2),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-760")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete nested attribute. One String attribute. One crosswalk")
    public void runRpTc0760() throws Exception {
        String nestedAttributeName1 = "Narcolepsy";
        String simpleAttributeName1 = "NarcolepsyInterest";
        String simpleAttributeValue1 = "NarcolepsyInterest1" + " - " + UnqNum;
        int expectedSize = 7;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        crosswalk1.setDataProvider(false);
        CrosswalkModel crosswalk2 = new CrosswalkModel("SHS", "SHS-CROSSWALK-" + UnqNum);
        e1.getCrosswalks().add(crosswalk2);
        e1.removeAttributeFromModelByPath("Address");
        e1.post();

        //Create Nested attribute. Add simple attribute to nested attribute.
        NestedAttributeModel nestedAttr1 = new NestedAttributeModel(new HashMap<>());
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue1);
        nestedAttr1.addAttributeToModel(simpleAttributeName1, simpleAttribute1);

        e1.postAttribute(nestedAttributeName1, crosswalk2, nestedAttr1.getPostBody(), null);
        e1.refresh();

        e1.getAttribute(nestedAttributeName1).getAsNested().delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITIES_MERGED_ON_THE_FLY' x 1,'ENTITY_CHANGED' x 3");
        assertTrue(checkDeleteStringSimpleAttributeOfNestedAttributeActivity(response, expectedSize, simpleAttributeName1, simpleAttributeValue1),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-752")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete nested attribute. One Date attribute. One crosswalk")
    public void runRpTc0752() throws Exception {
        String nestedAttributeName1 = "Narcolepsy";
        String simpleAttributeName1 = "FellowshipYear";
        String simpleAttributeValue1 = "2000";
        int expectedSize = 7;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        crosswalk1.setDataProvider(false);
        CrosswalkModel crosswalk2 = new CrosswalkModel("SHS", "SHS-CROSSWALK-" + UnqNum);
        e1.getCrosswalks().add(crosswalk2);
        e1.removeAttributeFromModelByPath("Address");
        e1.post();

        //Create Nested attribute. Add simple attribute to nested attribute.
        NestedAttributeModel nestedAttr1 = new NestedAttributeModel(new HashMap<>());
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue1);
        nestedAttr1.addAttributeToModel(simpleAttributeName1, simpleAttribute1);

        e1.postAttribute(nestedAttributeName1, crosswalk2, nestedAttr1.getPostBody(), null);
        e1.refresh();

        e1.getAttribute(nestedAttributeName1).getAsNested().delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITIES_MERGED_ON_THE_FLY' x 1, 'ENTITY_CHANGED' x 3");
        assertTrue(checkDeleteStringSimpleAttributeOfNestedAttributeActivity(response, expectedSize, simpleAttributeName1, simpleAttributeValue1),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-757")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete nested attribute. One Boolean attribute. One crosswalk")
    public void runRpTc0757() throws Exception {
        String nestedAttributeName1 = "Narcolepsy";
        String simpleAttributeName1 = "XyremICEligible";
        String simpleAttributeValue1 = "true";
        int expectedSize = 7;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        crosswalk1.setDataProvider(false);
        CrosswalkModel crosswalk2 = new CrosswalkModel("SHS", "SHS-CROSSWALK-" + UnqNum);
        e1.getCrosswalks().add(crosswalk2);
        e1.removeAttributeFromModelByPath("Address");
        e1.post();

        //Create Nested attribute. Add simple attribute to nested attribute.
        NestedAttributeModel nestedAttr1 = new NestedAttributeModel(new HashMap<>());
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue1);
        nestedAttr1.addAttributeToModel(simpleAttributeName1, simpleAttribute1);

        e1.postAttribute(nestedAttributeName1, crosswalk2, nestedAttr1.getPostBody(), null);
        e1.refresh();

        e1.getAttribute(nestedAttributeName1).getAsNested().delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITIES_MERGED_ON_THE_FLY' x 1, 'ENTITY_CHANGED' x 3");
        assertTrue(checkDeleteStringSimpleAttributeOfNestedAttributeActivity(response, expectedSize, simpleAttributeName1, simpleAttributeValue1),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-754")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity delete nested attribute. One Int attribute. One crosswalk")
    public void runRpTc0754() throws Exception {
        String nestedAttributeName1 = "Narcolepsy";
        String simpleAttributeName1 = "DateRegisteredSDS";
        String simpleAttributeValue1 = "2000-06-01";
        int expectedSize = 7;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        crosswalk1.setDataProvider(false);
        CrosswalkModel crosswalk2 = new CrosswalkModel("SHS", "SHS-CROSSWALK-" + UnqNum);
        e1.getCrosswalks().add(crosswalk2);
        e1.removeAttributeFromModelByPath("Address");
        e1.post();

        //Create Nested attribute. Add simple attribute to nested attribute.
        NestedAttributeModel nestedAttr1 = new NestedAttributeModel(new HashMap<>());
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue1);
        nestedAttr1.addAttributeToModel(simpleAttributeName1, simpleAttribute1);

        e1.postAttribute(nestedAttributeName1, crosswalk2, nestedAttr1.getPostBody(), null);
        e1.refresh();

        e1.getAttribute(nestedAttributeName1).getAsNested().delete();

        String response = waitForItemsSize(expectedSize, "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITIES_MERGED_ON_THE_FLY' x 1, 'ENTITY_CHANGED' x 3");
        assertTrue(checkDeleteStringSimpleAttributeOfNestedAttributeActivity(response, expectedSize, simpleAttributeName1, simpleAttributeValue1),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-4003")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity. Delete simple string attribute. Few simple attribute values, few crosswalks")
    public void runRpTc4003() throws Exception {
        String simpleAttributeName = "FirstName";
        String simpleAttributeValue = "FirstName1 " + "-" + UnqNum;
        String simpleAttributeValue2 = "FirstName2 " + "-" + UnqNum;
        int expectedSize = 8;

        ADDRESS_NUMBER = "611";
        setVar("ADDRESS_NUMBER", ADDRESS_NUMBER, true);
        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        CrosswalkModel crosswalk2 = new CrosswalkModel("Reltio", "Reltio-CROSSWALK-" + UnqNum);

        e1.getCrosswalks().add(crosswalk2);
        e1.post();//this change will modify existing relation by changing start/end related entity crosswalks

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue);
        e1.postAttribute(simpleAttributeName, crosswalk1, simpleAttribute1.getPostBody(), null);
        e1.refresh();

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute2 = new SimpleAttributeModel(simpleAttributeValue2);
        e1.postAttribute(simpleAttributeName, crosswalk2, simpleAttribute2.getPostBody(), null);
        e1.refresh();

        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue).delete();
        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue2).delete();
        String response = waitForItemsSize(
                expectedSize,
                "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 4, 'RELATIONSHIP_CHANGED' x 1"
        );
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue),
                "Activity details are wrong");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue2),
                "Activity details are wrong");
    }

    @TmsLink("RP-TC-4005")
    @Test(retryAnalyzer = RetryAnalyzerWithClean.class, groups = {"regression"}, description = "Activity. Delete simple boolean attribute. Few simple attribute values, few crosswalks")
    public void runRpTc4005() throws Exception {
        String simpleAttributeName = "MNComplianceFlag";
        String simpleAttributeValue = "true";
        String simpleAttributeValue2 = "false";
        int expectedSize = 8;

        EntityModel e1 = new EntityModel(postEntity(loadFile("resources:api/entity/ActivityBase/HcpWithAddress.json"), ADMIN_NAME, null)).get();

        CrosswalkModel crosswalk1 = e1.getCrosswalks().get(0);
        CrosswalkModel crosswalk2 = new CrosswalkModel("Reltio", "Reltio-CROSSWALK-" + UnqNum);

        e1.getCrosswalks().add(crosswalk2);
        e1.post();//this change will modify existing relation by changing start/end related entity crosswalks

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute1 = new SimpleAttributeModel(simpleAttributeValue);
        e1.postAttribute(simpleAttributeName, crosswalk1, simpleAttribute1.getPostBody(), null);
        e1.refresh();

        //add new simple attribute to existing crosswalk
        SimpleAttributeModel simpleAttribute2 = new SimpleAttributeModel(simpleAttributeValue2);
        e1.postAttribute(simpleAttributeName, crosswalk2, simpleAttribute2.getPostBody(), null);
        e1.refresh();

        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue).delete();
        findSimpleAttributeByValue(e1, simpleAttributeName, simpleAttributeValue2).delete();

        String response = waitForItemsSize(
                expectedSize,
                "'ENTITY_CREATED' x 2, 'RELATIONSHIP_CREATED' x 1, 'ENTITY_CHANGED' x 4, 'RELATIONSHIP_CHANGED' x 1"
        );
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue),
                "Activity details are wrong");
        assertTrue(checkDeleteStringSimpleAttributeActivity(response, expectedSize, simpleAttributeValue2),
                "Activity details are wrong");
    }
}
